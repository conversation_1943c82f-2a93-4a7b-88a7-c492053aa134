import {
	AfterContentInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ContentChildren,
	ElementRef,
	forwardRef,
	Input,
	OnInit,
	QueryList,
	Renderer2,
	ViewEncapsulation,
} from "@angular/core";
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from "@angular/forms";
import { getHostElement, hasSomeHostAttributes } from "../ds3.utils";
import { Ds3RadioComponent } from "./ds3-radio/ds3-radio.component";

let uniqueId = 0;

@Component({
	selector: "ds3-radio-group",
	templateUrl: "./ds3-radio-group.component.html",
	styleUrls: ["./ds3-radio-group.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
	host: {
		class: "ds3-radio-group",
	},
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => Ds3RadioGroupComponent),
			multi: true,
		},
	],
})
export class Ds3RadioGroupComponent
	implements OnInit, AfterContentInit, ControlValueAccessor
{
	@Input()
	id: string;

	@Input()
	name: string;

	@Input()
	labelPosition: "top" | "inline" = "top";

	_value: any;

	@Input()
	get value() {
		return this._value;
	}

	set value(value: any) {
		this._value = value;
		this._updateRadioCheckedFromValue();
		this._cd.markForCheck();
	}

	@ContentChildren(Ds3RadioComponent, { descendants: true })
	ds3Radios: QueryList<Ds3RadioComponent>;

	private _onChange: (value: any) => void = (v) => {
		/* NOOP */
	};
	private _onTouched: () => void = () => {
		/* NOOP */
	};

	constructor(
		private elementRef: ElementRef,
		private renderer2: Renderer2,
		private _cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.addId();

		if (!this.name) {
			this.name = `${this.id}-name`;
		}
	}

	ngAfterContentInit() {
		if (this.ds3Radios) {
			this._configureRadio();
			this.ds3Radios.changes.subscribe(() => {
				/**
				 * Evita o erro "Expression has changed after it was checked"
				 * que pode ocorrer em renderização que há um delay, por exemplo
				 * um array que é populado em uma subscription.
				 */
				Promise.resolve().then(() => {
					this._configureRadio();
				});
			});
		}
	}

	private _configureRadio() {
		this.ds3Radios.forEach((radio) => {
			if (!radio.name) {
				radio.name = this.name;
				radio.markForCheck();
			}

			this._verifyIfChecked(radio);

			if (radio.checked) {
				this._onChange(radio.value);
			}

			radio.changeEvent.subscribe((changeEvent) => {
				if (changeEvent.checked) {
					this.ds3Radios.forEach((r) => {
						if (r.id !== radio.id) {
							r.checked = false;
							r.markForCheck();
						}
					});
					this._onChange(radio.value);
					this._cd.markForCheck();
				}
			});
		});
	}

	private addId() {
		if (!this.id) {
			this.id = `ds3-radio-group-${uniqueId++}`;
		}
		const hostElement = getHostElement(this.elementRef);
		if (!hasSomeHostAttributes(hostElement, "id")) {
			this.renderer2.setAttribute(hostElement, "id", `${this.id}`);
		}
	}

	registerOnChange(fn: never): void {
		this._onChange = fn;
		this._cd.markForCheck();
	}

	registerOnTouched(fn: never): void {
		this._onTouched = fn;
	}

	setDisabledState(isDisabled: boolean): void {}

	writeValue(obj: any): void {
		if (obj) {
			this.value = obj;
		}
	}

	private _updateRadioCheckedFromValue() {
		if (this.ds3Radios) {
			this.ds3Radios.forEach((radio) => {
				this._verifyIfChecked(radio);
			});
		}
	}

	private _verifyIfChecked(radio: Ds3RadioComponent) {
		if (this.value) {
			radio.checked = this.value === radio.value;
			radio.markForCheck();
		}
	}
}
