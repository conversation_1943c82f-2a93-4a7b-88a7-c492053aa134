<input
	[id]="inputId"
	type="radio"
	[checked]="_checked"
	(change)="onInputChange($event)"
	[disabled]="disabled"
	[name]="_name"
	[value]="value" />
<div class="ds3-radio-mark" (click)="onChecked()">
	<svg
		width="14"
		height="14"
		viewBox="0 0 14 14"
		fill="none"
		xmlns="http://www.w3.org/2000/svg">
		<path
			class="ds3-radio-mark-checked"
			d="M6.45312 12.9062C6.89974 12.9062 7.33268 12.8607 7.75195 12.7695C8.18034 12.6875 8.58594 12.5599 8.96875 12.3867C9.35156 12.2227 9.71615 12.0267 10.0625 11.7988C10.4089 11.5618 10.7279 11.2975 11.0195 11.0059C11.3112 10.7142 11.5755 10.3952 11.8125 10.0488C12.0495 9.70247 12.2454 9.33789 12.4004 8.95508C12.5645 8.57227 12.6875 8.17122 12.7695 7.75195C12.8607 7.33268 12.9062 6.89518 12.9062 6.43945C12.9062 6.00195 12.8652 5.57357 12.7832 5.1543C12.5918 4.28841 12.2682 3.52279 11.8125 2.85742C11.3659 2.18294 10.7826 1.60417 10.0625 1.12109C9.34245 0.628906 8.57682 0.300781 7.76562 0.136719C7.33724 0.0455729 6.89974 0 6.45312 0C6.00651 0 5.56901 0.0455729 5.14062 0.136719C4.32943 0.300781 3.5638 0.619792 2.84375 1.09375C1.77734 1.82292 0.99349 2.77083 0.492188 3.9375C0.164062 4.75781 0 5.5918 0 6.43945C0 7.77018 0.369141 8.97786 1.10742 10.0625C1.58138 10.7552 2.16016 11.3294 2.84375 11.7852C3.53646 12.2773 4.30208 12.6055 5.14062 12.7695C5.56901 12.8607 6.00651 12.9062 6.45312 12.9062ZM6.45312 11.7168C5.72396 11.7168 5.04036 11.5801 4.40234 11.3066C3.76432 11.0332 3.20378 10.6595 2.7207 10.1855C2.23763 9.70247 1.85938 9.14193 1.58594 8.50391C1.3125 7.86589 1.17578 7.17773 1.17578 6.43945C1.17578 5.7194 1.3125 5.04036 1.58594 4.40234C1.85938 3.75521 2.23763 3.19466 2.7207 2.7207C3.20378 2.24674 3.76432 1.86849 4.40234 1.58594C5.04036 1.30339 5.72396 1.16211 6.45312 1.16211C7.19141 1.16211 7.875 1.30339 8.50391 1.58594C9.14193 1.86849 9.70247 2.24674 10.1855 2.7207C10.6686 3.19466 11.0469 3.75521 11.3203 4.40234C11.5938 5.04036 11.7305 5.7194 11.7305 6.43945C11.7305 7.17773 11.5938 7.86589 11.3203 8.50391C11.0469 9.14193 10.6686 9.70247 10.1855 10.1855C9.70247 10.6595 9.14193 11.0332 8.50391 11.3066C7.875 11.5801 7.19141 11.7168 6.45312 11.7168ZM6.45312 10.1035C5.44141 10.1035 4.58008 9.74805 3.86914 9.03711C3.1582 8.31706 2.80273 7.45573 2.80273 6.45312C2.80273 5.44141 3.1582 4.58008 3.86914 3.86914C4.58008 3.1582 5.44141 2.80273 6.45312 2.80273C7.46484 2.80273 8.32617 3.1582 9.03711 3.86914C9.74805 4.58008 10.1035 5.44141 10.1035 6.45312C10.1035 7.45573 9.74805 8.31706 9.03711 9.03711C8.32617 9.74805 7.46484 10.1035 6.45312 10.1035Z"
			fill="#1E60FA" />
		<path
			class="ds3-radio-mark-unchecked"
			d="M12.91 6.44813C12.91 6.00817 12.8687 5.57738 12.7861 5.15575C12.6944 4.73412 12.5659 4.33083 12.4008 3.94587C12.2448 3.56091 12.0475 3.19427 11.8089 2.84597C11.5795 2.49767 11.318 2.17687 11.0244 1.88357C10.7308 1.59943 10.4097 1.3382 10.061 1.09989C9.7215 0.870749 9.35906 0.673685 8.97369 0.508701C8.58832 0.343717 8.18459 0.219979 7.76252 0.137487C7.34044 0.0458289 6.9046 0 6.455 0C6.0054 0 5.56956 0.0458289 5.14748 0.137487C4.72541 0.219979 4.32168 0.343717 3.93631 0.508701C3.55094 0.673685 3.1885 0.870749 2.84901 1.09989C2.50034 1.3382 2.17919 1.59943 1.88558 1.88357C1.59196 2.17687 1.33045 2.49767 1.10107 2.84597C0.862502 3.19427 0.66064 3.56091 0.49548 3.94587C0.339495 4.33083 0.215625 4.73412 0.12387 5.15575C0.04129 5.57738 0 6.00817 0 6.44813C0 6.89725 0.04129 7.33262 0.12387 7.75425C0.215625 8.17587 0.339495 8.57917 0.49548 8.96413C0.66064 9.34909 0.862502 9.71573 1.10107 10.064C1.33045 10.4032 1.59196 10.7194 1.88558 11.0127C2.17919 11.306 2.50034 11.5672 2.84901 11.7964C3.1885 12.0347 3.55094 12.2363 3.93631 12.4013C4.32168 12.5663 4.72541 12.69 5.14748 12.7725C5.56956 12.8642 6.0054 12.91 6.455 12.91C6.9046 12.91 7.34044 12.8642 7.76252 12.7725C8.18459 12.69 8.58832 12.5663 8.97369 12.4013C9.35906 12.2363 9.7215 12.0347 10.061 11.7964C10.4097 11.5672 10.7308 11.306 11.0244 11.0127C11.318 10.7194 11.5795 10.4032 11.8089 10.064C12.0475 9.71573 12.2448 9.34909 12.4008 8.96413C12.5659 8.57917 12.6944 8.17587 12.7861 7.75425C12.8687 7.33262 12.91 6.89725 12.91 6.44813ZM11.7401 6.44813C11.7401 7.18139 11.6025 7.86882 11.3272 8.51043C11.052 9.15203 10.6758 9.71114 10.1986 10.1878C9.71232 10.6644 9.14802 11.0402 8.50574 11.3152C7.87262 11.5901 7.18904 11.7276 6.455 11.7276C5.72096 11.7276 5.03279 11.5901 4.3905 11.3152C3.75739 11.0402 3.19768 10.6644 2.71138 10.1878C2.23425 9.71114 1.85805 9.15203 1.58278 8.51043C1.30752 7.86882 1.16988 7.18139 1.16988 6.44813C1.16988 5.72403 1.30752 5.04118 1.58278 4.39957C1.85805 3.75797 2.23425 3.19886 2.71138 2.72224C3.19768 2.24562 3.75739 1.86982 4.3905 1.59485C5.03279 1.31071 5.72096 1.16864 6.455 1.16864C7.18904 1.16864 7.87262 1.31071 8.50574 1.59485C9.14802 1.86982 9.71232 2.24562 10.1986 2.72224C10.6758 3.19886 11.052 3.75797 11.3272 4.39957C11.6025 5.04118 11.7401 5.72403 11.7401 6.44813Z"
			fill="#1E60FA" />
	</svg>
</div>
<label
	[for]="inputId"
	class="text-color-typography-default-title typography-body-2"
	#labelElement>
	<ng-content></ng-content>
</label>
