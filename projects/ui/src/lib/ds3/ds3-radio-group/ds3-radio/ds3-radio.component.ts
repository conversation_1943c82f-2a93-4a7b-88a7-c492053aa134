import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	EventEmitter,
	Input,
	OnChanges,
	OnInit,
	Output,
	Renderer2,
	SimpleChanges,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { getHostElement, hasSomeHostAttributes } from "../../ds3.utils";

let uniqueId = 0;

@Component({
	selector: "ds3-radio",
	templateUrl: "./ds3-radio.component.html",
	styleUrls: ["./ds3-radio.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
	host: {
		class: "ds3-radio",
		"[id]": "id",
		"(click)": "onHostClick()",
	},
})
export class Ds3RadioComponent implements OnInit, OnChanges {
	@Input()
	disabled: boolean = false;

	@Input()
	id: string;

	@Output()
	changeEvent: EventEmitter<{ checked: boolean }> = new EventEmitter();

	@ViewChild("labelElement", { static: false })
	labelElement: ElementRef;

	_checked: boolean = false;
	@Input()
	get checked(): boolean {
		return this._checked;
	}

	set checked(value: boolean) {
		this._checked = value;
		this._addCheckedClass();
		this._cd.markForCheck();
	}

	_name: string;
	@Input()
	get name(): string {
		return this._name;
	}

	set name(value: string) {
		this._name = value;
		this._cd.markForCheck();
	}

	@Input()
	value: any;

	inputId: string;

	constructor(
		private elementRef: ElementRef,
		private renderer2: Renderer2,
		private _cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.addId();
		this._addCheckedClass();
	}

	ngOnChanges(changes: SimpleChanges) {}

	private addId() {
		if (!this.id) {
			this.id = `ds3-radio-${uniqueId++}`;
		}
		const hostElement = getHostElement(this.elementRef);
		if (!hasSomeHostAttributes(hostElement, "id")) {
			this.renderer2.setAttribute(hostElement, "id", `${this.id}`);
		}
		this.inputId = `${this.id}-input`;
	}

	onInputChange(event: Event) {
		event.stopPropagation();
		this.onChecked();
	}

	onChecked() {
		this.checked = !this.checked;

		this.changeEvent.emit({ checked: this.checked });
	}

	private _addCheckedClass() {
		if (this.checked) {
			this.renderer2.addClass(
				getHostElement(this.elementRef),
				"ds3-radio-checked"
			);
		} else {
			this.renderer2.removeClass(
				getHostElement(this.elementRef),
				"ds3-radio-checked"
			);
		}
	}

	onHostClick() {
		this.labelElement.nativeElement.click();
	}

	markForCheck() {
		this._cd.markForCheck();
	}
}
