.ds3-radio {
	display: flex;
	align-items: center;
	gap: 8px;
	cursor: pointer;

	label {
		margin-bottom: unset;
		padding: unset;
		cursor: pointer;
	}

	.ds3-radio-mark {
		display: flex;
		width: 14px;
		height: 14px;

		svg {
			.ds3-radio-mark-checked {
				opacity: 0;
				transition: opacity 180ms cubic-bezier(0, 0, 0.2, 1),
					transform 180ms cubic-bezier(0, 0, 0.2, 1);
			}

			.ds3-radio-mark-unchecked {
				transition: opacity 180ms cubic-bezier(0, 0, 0.2, 1),
					transform 180ms cubic-bezier(0, 0, 0.2, 1);
			}
		}
	}

	input {
		display: none;
	}

	&.ds3-radio-checked {
		.ds3-radio-mark {
			svg {
				.ds3-radio-mark-checked {
					opacity: 1;
				}

				.ds3-radio-mark-unchecked {
					opacity: 0;
				}
			}
		}
	}
}
