import { moduleMetadata } from "@storybook/angular";
import { Ds3Module } from "../ds3.module";
import { array, withKnobs } from "@storybook/addon-knobs";
import { PactoDataTableStateManager } from "./directives/ds3-data-table-state-manager";

//@ts-ignore
import Notes from "./ds3-table.notes.md";

export default {
	title: "Design System 3 | Data/Table",
	decorators: [
		moduleMetadata({
			imports: [Ds3Module],
		}),
		withKnobs,
	],
	parameters: {
		notes: Notes,
	},
};

const tableState: PactoDataTableStateManager<any> =
	new PactoDataTableStateManager();

const data = [{ nome: "nome", idade: "idade", cpf: "cpf" }];

export const table = () => {
	({
		template: `
		<div style="padding: 16px;">
			<table ds3DataTable [stateManager]="tableState">
				<ng-container ds3TableColumn="nome">
					<th *ds3TableHeaderCell>Nome</th>
					<td class="nome" *ds3TableCell="let item">
						{{ item.nome }}
					</td>
				</ng-container>
			
				<ng-container ds3TableColumn="idade">
					<th *ds3TableHeaderCell>Idade</th>
					<td class="idade" *ds3TableCell="let item">
						{{ item?.idade }}
					</td>
				</ng-container>
			
				<ng-container ds3TableColumn="cpf">
					<th class="align-center" *ds3TableHeaderCell>CPF</th>
					<td class="cpf" *ds3TableCell="let item">
						{{ item?.cpf }}
					</td>
				</ng-container>
			
				<tr *ds3TableRow></tr>
			
				<button
					ds3-icon-button
					*ds3TableSortControl="
						let direction = direction;
						let triggerSortToggle = triggerSortToggle
					"
					class="sort-control"
					(click)="triggerSortToggle()">
					<i
						class="pct pct-drop-down"
						[ngClass]="{
							'pct-drop-down': direction === null,
							'pct-caret-up': direction === 'ASC',
							'pct-caret-down': direction === 'DESC'
						}"></i>
				</button>
			
				<tbody *ds3TableLoading>
					<tr>
						<td>
							<div class="spinner-border" role="status">
								<span class="sr-only"></span>
							</div>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
    `,
		props: {
			tableState: tableState,
		},
	});
};
