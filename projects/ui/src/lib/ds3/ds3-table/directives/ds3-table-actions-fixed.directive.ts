import {
	AfterViewChecked,
	AfterViewInit,
	Directive,
	ElementRef,
	HostBinding,
	OnInit,
	Renderer2,
} from "@angular/core";

@Directive({
	selector: "[ds3TableActionsFixed]",
})
export class Ds3TableActionsFixedDirective implements AfterViewChecked {
	// @HostBinding("class.ds3-actions-column-fixed") addClass = true;

	private isBuilt = false;

	constructor(private el: ElementRef, private renderer: Renderer2) {}

	ngAfterViewChecked(): void {
		if (!this.isBuilt) {
			// setTimeout(() => {
			// 	const hostElement = this.el.nativeElement;
			// 	const lastHeaderCell = hostElement.querySelector("thead th:last-child");
			// 	const lastColumnCells = hostElement.querySelectorAll(
			// 		"tbody td:last-child"
			// 	);
			// 	if (lastColumnCells) {
			// 		const lastColumnWidth = lastColumnCells[0].offsetWidth;
			// 		this.renderer.setStyle(
			// 			hostElement,
			// 			"padding-right",
			// 			`${lastColumnWidth + 24}px`
			// 		);
			// 		const scrollButtonNext = hostElement.querySelector(
			// 			".ds3-table-scroll-button-next"
			// 		);
			// 		if (scrollButtonNext) {
			// 			this.renderer.setStyle(
			// 				scrollButtonNext,
			// 				"left",
			// 				`calc( 100% - ${lastColumnWidth + 24}px`
			// 			);
			// 		}
			// 		this.renderer.setStyle(lastHeaderCell, "position", "absolute");
			// 		this.renderer.setStyle(lastHeaderCell, "right", "0");
			// 		this.renderer.setStyle(
			// 			lastHeaderCell,
			// 			"width",
			// 			`${lastColumnWidth}px`
			// 		);
			// 		lastColumnCells.forEach((cell: HTMLElement) => {
			// 			this.renderer.setStyle(cell, "position", "absolute");
			// 			this.renderer.setStyle(cell, "right", "0");
			// 			this.renderer.setStyle(lastHeaderCell, "display", "flex");
			// 			this.renderer.setStyle(lastHeaderCell, "align-items", "center");
			// 		});
			// 		this.isBuilt = true;
			// 	}
			// }, 300);
		}
	}
}
