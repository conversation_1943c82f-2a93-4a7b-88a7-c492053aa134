import {
	AfterViewChecked,
	AfterViewInit,
	Directive,
	ElementRef,
	HostBinding,
	OnDestroy,
	OnInit,
	Renderer2,
} from "@angular/core";

@Directive({
	selector: "[ds3TableColumnFixed]",
})
export class Ds3TableColumnFixedDirective
	implements AfterViewChecked, OnD<PERSON>roy
{
	// @HostBinding("class.ds3-first-column-fixed") addClass = true;

	private isBuilt = false;

	constructor(private el: ElementRef, private renderer: Renderer2) {}

	ngAfterViewChecked(): void {
		if (!this.isBuilt) {
			// setTimeout(() => {
			// 	const hostElement = this.el.nativeElement;
			// 	const firstHeaderCell = hostElement.querySelector(
			// 		"thead th:first-child"
			// 	);
			// 	const firstColumnCells = hostElement.querySelectorAll(
			// 		"tbody td:first-child"
			// 	);
			// 	if (firstColumnCells) {
			// 		const firstColumnWidth = firstColumnCells[0].offsetWidth;
			// 		this.renderer.setStyle(
			// 			hostElement,
			// 			"padding-left",
			// 			`${firstColumnWidth + 24}px`
			// 		);
			// 		const scrollButtonPrev = hostElement.querySelector(
			// 			".ds3-table-scroll-button-prev"
			// 		);
			// 		if (scrollButtonPrev) {
			// 			this.renderer.setStyle(
			// 				scrollButtonPrev,
			// 				"left",
			// 				`${firstColumnWidth}px`
			// 			);
			// 		}
			// 		this.renderer.setStyle(firstHeaderCell, "position", "absolute");
			// 		this.renderer.setStyle(firstHeaderCell, "left", "0");
			// 		this.renderer.setStyle(
			// 			firstHeaderCell,
			// 			"width",
			// 			`${firstColumnWidth}px`
			// 		);
			// 		firstColumnCells.forEach((cell: HTMLElement) => {
			// 			this.renderer.setStyle(cell, "position", "absolute");
			// 			this.renderer.setStyle(cell, "left", "0");
			// 			this.renderer.setStyle(firstHeaderCell, "display", "flex");
			// 			this.renderer.setStyle(firstHeaderCell, "align-items", "center");
			// 		});
			// 		this.isBuilt = true;
			// 	}
			// }, 300);
		}
	}

	ngOnDestroy() {
		this.isBuilt = false;
	}
}
