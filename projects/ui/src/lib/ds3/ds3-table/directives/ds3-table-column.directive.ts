import { ContentChild, ContentChildren, Directive, Input } from "@angular/core";
import { Ds3TableCellDirective } from "./ds3-table-cell.directive";
import { Ds3TableHeaderCellDirective } from "./ds3-table-header-cell.directive";

@Directive({
	selector: "[ds3TableColumn]",
})
export class Ds3TableColumnDirective {
	@Input() ds3TableColumn: string;
	@Input() ds3FixedColumn = false;
	@Input() columnInitiallyVisible = true;
	@ContentChild(Ds3TableHeaderCellDirective, { static: false })
	headerCell: Ds3TableHeaderCellDirective;
	@ContentChild(Ds3TableCellDirective, { static: false })
	cell: Ds3TableCellDirective;

	constructor() {}
}
