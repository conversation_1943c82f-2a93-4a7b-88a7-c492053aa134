import {
	AfterContentInit,
	ContentChild,
	ContentChildren,
	Directive,
	ElementRef,
	HostBinding,
	HostListener,
	Input,
	OnInit,
	QueryList,
	Renderer2,
	ViewContainerRef,
} from "@angular/core";
import { PactoDataTableStateManager } from "./ds3-data-table-state-manager";
import { Ds3TableColumnDirective } from "./ds3-table-column.directive";
import { Ds3TableLoadingDirective } from "./ds3-table-loading.directive";
import { Ds3TableRowDirective } from "./ds3-table-row.directive";
import { Ds3TableSortControlDirective } from "./ds3-table-sort-control.directive";
import { Ds3TableEmptyRowDirective } from "./ds3-table-empty-row.directive";
import { BreakpointObserver } from "@angular/cdk/layout";

@Directive({
	selector: "table[ds3DataTable]",
	exportAs: "ds3DataTable",
	host: {
		"[class.ds3-table-small]": "smallRows",
	},
})
export class Ds3DataTableDirective<T> implements OnInit, AfterContentInit {
	@ContentChildren(Ds3TableColumnDirective)
	contentColumnTemplates: QueryList<Ds3TableColumnDirective>;
	@ContentChild(Ds3TableLoadingDirective, { static: true })
	contentLoadingTemplate: Ds3TableLoadingDirective;
	@ContentChild(Ds3TableRowDirective, { static: true })
	contentRowTemplate: Ds3TableRowDirective;
	@ContentChild(Ds3TableEmptyRowDirective, { static: true })
	emptyRowTemplate: Ds3TableEmptyRowDirective;
	@ContentChild(Ds3TableSortControlDirective, { static: true })
	sortTemplate: Ds3TableSortControlDirective;

	@Input() stateManager: PactoDataTableStateManager<T>;
	/**
	 * Colunas adicinais no formato Ds3TableColumnDirective
	 */
	@Input() columnTemplates: Ds3TableColumnDirective[] = [];
	@Input() loadingTemplate: Ds3TableLoadingDirective;
	@Input() rowTemplate: Ds3TableRowDirective;
	@Input() smallRows: boolean = false;

	private thead;
	private tbody;
	private headerRow;
	private dotsNav;
	private buttonPrev;
	private buttonNext;

	private navigationBuilt = false;

	private resizeTimeout: any;
	private scrollStep = 100;

	private tableViewWidth: number;

	private hostElement: HTMLElement;
	private parentElement: HTMLElement;

	private activeDot: number;

	@HostBinding("class.ds3-table") addClass = true;

	@HostListener("window:resize")
	onWindowResize() {
		clearTimeout(this.resizeTimeout);

		this.resizeTimeout = window.setTimeout(() => {
			this.checkOverflow();
		}, 200);
	}

	@HostListener("scroll", ["$event"])
	onParentScroll(event: Event) {
		if (event.target === this.parentElement) {
			this.updateActiveDot();
		}
	}

	constructor(
		private el: ElementRef,
		private renderer: Renderer2,
		private tableElement: ElementRef,
		private tableViewContainerRef: ViewContainerRef,
		private breakpointObserver: BreakpointObserver
	) {}

	get allColumns(): Ds3TableColumnDirective[] {
		return [...this.contentColumnTemplates.toArray(), ...this.columnTemplates];
	}

	get activeLoadingTemplate(): Ds3TableLoadingDirective {
		return this.loadingTemplate || this.contentLoadingTemplate;
	}

	get activeRowTemplate(): Ds3TableRowDirective {
		return this.rowTemplate || this.contentRowTemplate;
	}

	ngOnInit() {}

	ngAfterContentInit() {
		if (!this.stateManager) return;
		this.initializeColumnConfig();
		this.renderTable();
		this.stateManager.state$.subscribe(() => {
			this.renderTable();
			setTimeout(() => {
				this.checkOverflow();
			}, 200);
		});
	}

	get state() {
		return this.stateManager.state$.value;
	}

	get data() {
		return this.state.data;
	}

	private initializeColumnConfig() {
		const columnConfig = this.allColumns.map((column) => ({
			id: column.ds3TableColumn,
			initiallyVisible: column.columnInitiallyVisible,
		}));
		this.stateManager.initializeColumnConfig(columnConfig);
	}

	private get visibleColumns(): Ds3TableColumnDirective[] {
		return this.allColumns.filter((column) => {
			const columnId = column.ds3TableColumn;
			return this.state.columnVisibility[columnId];
		});
	}

	private renderTable() {
		this.clearState();

		if (this.state.loading) {
			this.renderLoading();
		} else {
			this.renderTableHeader(this.visibleColumns);
			this.renderTableBody(this.state.data);
		}
	}

	private clearState() {
		this.tableViewContainerRef.clear();
	}

	private renderTableHeader(columns: Ds3TableColumnDirective[]) {
		const headerCells = this.buildHeaderCells(columns);

		if (!this.thead) {
			this.thead = this.renderer.createElement("thead");
		}
		if (!this.headerRow) {
			this.headerRow = this.renderer.createElement("tr");
		}

		this.thead.insertBefore(this.headerRow, this.thead.lastChild);
		headerCells.reverse().forEach((headerCell) => {
			this.headerRow.insertBefore(headerCell, this.headerRow.firstChild);
		});

		const tableRef = this.tableElement.nativeElement;
		tableRef.insertBefore(this.thead, tableRef.firstChild);
	}

	private renderTableBody(data: any[]) {
		const tableBody = this.buildTableBody(data);
		const tableRef = this.tableElement.nativeElement;
		tableRef.insertBefore(tableBody, tableRef.lastChild);
	}

	private renderLoading() {
		if (!this.activeLoadingTemplate) return;
		const loadingTemplate = this.activeLoadingTemplate.templateRef;
		const embbededView =
			this.tableViewContainerRef.createEmbeddedView(loadingTemplate);

		this.tableElement.nativeElement.insertBefore(
			embbededView.rootNodes[0],
			this.tableElement.nativeElement.lastChild
		);
	}

	private buildHeaderCells(columns: Ds3TableColumnDirective[]): HTMLElement[] {
		return columns.map((column) => this.buildHeaderCell(column));
	}

	private buildHeaderCell(column: Ds3TableColumnDirective): HTMLElement {
		const headerCellTemplate = column.headerCell.template;
		const viewContainer = this.tableViewContainerRef;

		const cellEmbeddedViewRef =
			viewContainer.createEmbeddedView(headerCellTemplate);

		const cellElement: HTMLElement = cellEmbeddedViewRef.rootNodes[0];
		const sortControl = this.buildSortControl(column.ds3TableColumn);
		if (sortControl) {
			cellElement.insertBefore(sortControl, null);
		}

		return cellEmbeddedViewRef.rootNodes[0];
	}

	private buildSortControl(columnId: string): HTMLElement {
		if (!this.sortTemplate) return null;

		const sortTemplate = this.sortTemplate.templateRef;
		const viewContainer = this.tableViewContainerRef;
		const sortEmbeddedViewRef = viewContainer.createEmbeddedView(sortTemplate, {
			direction: this.stateManager.getSortDirection(columnId),
			triggerSortToggle: () => {
				this.stateManager.triggerSortToggle(columnId);
			},
			triggerSortChange: () => {
				this.stateManager.triggerSortChange(
					columnId,
					this.stateManager.getSortDirection(columnId)
				);
			},
		});
		return sortEmbeddedViewRef.rootNodes[0];
	}

	private buildBodyCells(data: any): HTMLElement[] {
		const templates = this.visibleColumns.map((column) => column.cell.template);
		const viewContainerRef = this.tableViewContainerRef;
		return templates.map((template, index) => {
			const embeddedViewRef = viewContainerRef.createEmbeddedView(template, {
				$implicit: data,
				index,
			});
			return embeddedViewRef.rootNodes[0];
		});
	}

	private buildRow(dataItem: any): HTMLElement {
		const rowCells = this.buildBodyCells(dataItem);
		const rowTemplate = this.activeRowTemplate.templateRef;
		const rowEmbeddedView =
			this.tableViewContainerRef.createEmbeddedView(rowTemplate);
		const rowNativeRef = rowEmbeddedView.rootNodes[0];

		rowCells.forEach((rowCell) => {
			rowNativeRef.insertBefore(rowCell, null);
		});

		return rowNativeRef;
	}

	private buildTableBody(data: any[]) {
		let tableRows: Array<HTMLElement> = [];
		if (data === null || (data && data.length === 0)) {
			const embbededView = this.tableViewContainerRef.createEmbeddedView(
				this.emptyRowTemplate.templateRef
			);

			const emptyRowElement: HTMLElement = embbededView.rootNodes[0];
			emptyRowElement
				.getElementsByTagName("td")[0]
				.setAttribute("colspan", this.visibleColumns.length.toString());
			tableRows.push(emptyRowElement);
		}

		if (data && data.length > 0) {
			tableRows = data.map((dataItem) => {
				return this.buildRow(dataItem);
			});
		}

		if (!this.tbody) {
			this.tbody = this.renderer.createElement("tbody");
		}

		tableRows.forEach((tableRow) => {
			this.renderer.appendChild(this.tbody, tableRow);
		});
		return this.tbody;
	}

	private checkOverflow() {
		this.hostElement = this.el.nativeElement;
		this.parentElement = this.hostElement.parentElement;

		const grandparentEl = this.renderer.createElement("div");

		const grandParent = this.parentElement.parentNode;
		this.renderer.insertBefore(grandParent, grandparentEl, this.parentElement);

		this.renderer.appendChild(grandparentEl, this.parentElement);

		if (this.parentElement && this.hostElement) {
			const parentRect = this.parentElement.getBoundingClientRect();
			const hostRect = this.hostElement.getBoundingClientRect();

			if (
				hostRect.width > parentRect.width ||
				hostRect.height > parentRect.height
			) {
				if (this.navigationBuilt) {
					this.removeTableScrollNavigation();
					this.renderer.removeStyle(grandparentEl, "position");
				}

				this.buildTableScrollNavigation();
				this.buildTableDotsNavigation();

				this.renderer.setStyle(grandparentEl, "position", "relative");

				if (this._canBuildFixedColumns("ds3FirstColumnFixed")) {
					this.buildFirstColumnFixed();
				}
				if (this._canBuildFixedColumns("ds3ActionsColumnFixed")) {
					this.buildActionsColumnFixed();
				}

				this.navigationBuilt = true;
			} else {
				if (this.navigationBuilt) {
					this.removeTableScrollNavigation();
					this.navigationBuilt = false;
					this.renderer.removeStyle(grandparentEl, "position");
				}
			}
		}
	}

	private _canBuildFixedColumns(atributeName: string) {
		const breakpoint640 =
			this.breakpointObserver.isMatched("(max-width: 640px)");
		return (
			this.data &&
			this.data.length > 0 &&
			!breakpoint640 &&
			this.parentElement.hasAttribute(atributeName)
		);
	}

	private buildFirstColumnFixed() {
		setTimeout(() => {
			const hostElement = this.el.nativeElement;
			const firstHeaderCell = this.hostElement.querySelector(
				"thead th:first-child"
			);
			const firstColumnCells = hostElement.querySelectorAll(
				"tbody td:first-child, tfoot td:first-child"
			);

			if (firstColumnCells) {
				this.renderer.addClass(this.parentElement, "ds3-first-column-fixed");

				const biggerColumn =
					this.getLargestOffsetWidthElement(firstColumnCells) < 144
						? 180
						: this.getLargestOffsetWidthElement(firstColumnCells);

				this.renderer.setStyle(
					this.parentElement,
					"padding-left",
					`${biggerColumn + 24}px`
				);

				const scrollButtonPrev = this.parentElement.querySelector(
					".ds3-table-scroll-button-prev"
				);
				if (scrollButtonPrev) {
					this.renderer.setStyle(scrollButtonPrev, "left", `${biggerColumn}px`);
				}

				this.renderer.setStyle(firstHeaderCell, "position", "absolute");
				this.renderer.setStyle(firstHeaderCell, "left", "0");

				this.renderer.setStyle(firstHeaderCell, "width", `${biggerColumn}px`);

				firstColumnCells.forEach((cell: HTMLElement) => {
					this.renderer.setStyle(cell, "position", "absolute");
					this.renderer.setStyle(cell, "left", "0");
					this.renderer.setStyle(cell, "display", "flex");
					this.renderer.setStyle(cell, "align-items", "center");
					this.renderer.setStyle(cell, "width", `${biggerColumn}px`);
				});
			}
		}, 101);
	}

	private buildActionsColumnFixed() {
		setTimeout(() => {
			const hostElement = this.el.nativeElement;
			const lastHeaderCell = hostElement.querySelector("thead th:last-child");
			const lastColumnCells = hostElement.querySelectorAll(
				"tbody td:last-child, tfoot td:last-child"
			);

			if (lastColumnCells) {
				this.renderer.addClass(this.parentElement, "ds3-actions-column-fixed");
				const lastColumnWidth = lastColumnCells[0].offsetWidth;

				this.renderer.setStyle(
					this.parentElement,
					"padding-right",
					`${lastColumnWidth + 24}px`
				);

				const scrollButtonNext = this.parentElement.querySelector(
					".ds3-table-scroll-button-next"
				);
				if (scrollButtonNext) {
					this.renderer.setStyle(
						scrollButtonNext,
						"left",
						`calc( 100% - ${lastColumnWidth + 24}px`
					);
				}

				this.renderer.setStyle(lastHeaderCell, "position", "absolute");
				this.renderer.setStyle(lastHeaderCell, "right", "0");
				this.renderer.setStyle(lastHeaderCell, "width", `${lastColumnWidth}px`);

				lastColumnCells.forEach((cell: HTMLElement) => {
					this.renderer.setStyle(cell, "position", "absolute");
					this.renderer.setStyle(cell, "right", "0");
					this.renderer.setStyle(cell, "display", "flex");
					this.renderer.setStyle(cell, "align-items", "center");
				});
			}
		}, 110);
	}

	private getLargestOffsetWidthElement(elements) {
		if (!elements || elements.length === 0) {
			return null;
		}

		let largestElement = elements[0];
		let largestWidth = largestElement.offsetWidth;

		elements.forEach((element) => {
			if (element.offsetWidth > largestWidth) {
				largestWidth = element.offsetWidth;
				largestElement = element;
			}
		});

		return largestWidth;
	}

	private buildTableScrollNavigation() {
		this.renderer.addClass(this.parentElement, "ds3-h-scroll");

		this.buttonPrev = this.buildTableScrollNavigationPrev();
		this.buttonNext = this.buildTableScrollNavigationNext();

		this.parentElement.append(this.buttonPrev);
		this.parentElement.append(this.buttonNext);
	}

	private buildTableDotsNavigation() {
		console.log("dots nav", this.dotsNav);
		this.dotsNav = this.renderer.createElement("div");

		const dotsPrev = this.buildTableDotsNavigationPrev();
		const dotsNext = this.buildTableDotsNavigationNext();

		this.renderer.addClass(this.dotsNav, "ds3-table-dots-nav");

		this.dotsNav.append(dotsPrev);
		this.dotsNav.append(dotsNext);

		setTimeout(() => {
			const parentComputedStyle = getComputedStyle(this.parentElement);
			const parentPaddingLeft = parseFloat(parentComputedStyle.paddingLeft);
			const parentPaddingRight = parseFloat(parentComputedStyle.paddingRight);
			const hostWidth = this.hostElement.offsetWidth;
			const parentWidth = this.parentElement.offsetWidth;

			this.tableViewWidth =
				parentWidth - (parentPaddingLeft + parentPaddingRight);
			const viewRatio = hostWidth / this.tableViewWidth;
			const dotsInt = Math.trunc(hostWidth / this.tableViewWidth);
			const dotsQty = viewRatio > dotsInt ? dotsInt + 1 : dotsInt;

			console.log(`dotsInt ${dotsInt} dotsQty ${dotsQty}`);

			for (let i = 0; i < dotsQty; i++) {
				const dotElement = this.renderer.createElement("button");
				this.renderer.addClass(dotElement, "ds3-table-dot");
				dotElement.addEventListener("click", () => this.scrollTableDots(i));
				if (this.dotsNav.querySelectorAll(".ds3-table-dot").length >= dotsQty) {
					return;
				}
				this.dotsNav.append(dotElement);
			}

			this.updateActiveDot();
		}, 200);

		this.parentElement.append(this.dotsNav);
		this.renderer.setStyle(this.parentElement, "margin-bottom", "50px");
	}

	private removeTableScrollNavigation() {
		this.renderer.removeChild(this.hostElement, this.buttonPrev);
		this.renderer.removeChild(this.hostElement, this.buttonNext);
		this.renderer.removeChild(this.hostElement, this.dotsNav);
		this.renderer.removeClass(this.parentElement, "ds3-first-column-fixed");
		this.renderer.removeClass(this.parentElement, "ds3-actions-column-fixed");
		this.renderer.removeClass(this.parentElement, "ds3-h-scroll");
		this.renderer.removeStyle(this.parentElement, "position");
		this.renderer.removeStyle(this.parentElement, "left");
		this.renderer.removeStyle(this.parentElement, "padding-left");
		this.renderer.removeStyle(this.parentElement, "padding-right");
		this.renderer.removeStyle(this.parentElement, "margin-bottom");
		this.renderer.removeStyle(
			this.hostElement.querySelector("thead th:first-child"),
			"position"
		);
		this.hostElement
			.querySelectorAll("tbody td:first-child")
			.forEach((item) => {
				this.renderer.removeStyle(item, "position");
				this.renderer.removeStyle(item, "left");
				this.renderer.removeStyle(item, "display");
				this.renderer.removeStyle(item, "align-items");
			});
		this.hostElement
			.querySelectorAll("tfoot td:first-child")
			.forEach((item) => {
				this.renderer.removeStyle(item, "position");
				this.renderer.removeStyle(item, "left");
				this.renderer.removeStyle(item, "display");
				this.renderer.removeStyle(item, "align-items");
			});
		this.renderer.removeStyle(
			this.hostElement.querySelector("thead th:last-child"),
			"position"
		);
		this.hostElement.querySelectorAll("tbody td:last-child").forEach((item) => {
			this.renderer.removeStyle(item, "position");
			this.renderer.removeStyle(item, "right");
			this.renderer.removeStyle(item, "display");
			this.renderer.removeStyle(item, "align-items");
		});
		this.hostElement.querySelectorAll("tfoot td:last-child").forEach((item) => {
			this.renderer.removeStyle(item, "position");
			this.renderer.removeStyle(item, "right");
			this.renderer.removeStyle(item, "display");
			this.renderer.removeStyle(item, "align-items");
		});
	}

	private buildTableScrollNavigationPrev(): HTMLElement {
		const buttonPrev = this.renderer.createElement("button");
		const buttonArrowPrev = this.renderer.createElement("i");
		this.renderer.addClass(buttonPrev, "ds3-table-scroll-button");
		this.renderer.addClass(buttonPrev, "ds3-table-scroll-button-prev");
		this.renderer.addClass(buttonArrowPrev, "pct");
		this.renderer.addClass(buttonArrowPrev, "pct-chevron-left");
		buttonPrev.append(buttonArrowPrev);

		buttonPrev.addEventListener("click", () => this.scrollTablePrev());
		return buttonPrev;
	}

	private buildTableScrollNavigationNext(): HTMLElement {
		const buttonNext = this.renderer.createElement("button");
		const buttonArrowNext = this.renderer.createElement("i");
		this.renderer.addClass(buttonNext, "ds3-table-scroll-button");
		this.renderer.addClass(buttonNext, "ds3-table-scroll-button-next");
		this.renderer.addClass(buttonArrowNext, "pct");
		this.renderer.addClass(buttonArrowNext, "pct-chevron-right");
		buttonNext.append(buttonArrowNext);

		buttonNext.addEventListener("click", () => this.scrollTableNext());
		return buttonNext;
	}

	private buildTableDotsNavigationPrev(): HTMLElement {
		const buttonPrev = this.renderer.createElement("button");
		const buttonArrowPrev = this.renderer.createElement("i");
		this.renderer.setAttribute(buttonPrev, "ds3-icon-button", "true");
		this.renderer.addClass(buttonPrev, "ds3-table-dots-button");
		this.renderer.addClass(buttonPrev, "ds3-table-dots-button-prev");
		this.renderer.addClass(buttonArrowPrev, "pct");
		this.renderer.addClass(buttonArrowPrev, "pct-chevrons-left");
		buttonPrev.append(buttonArrowPrev);

		buttonPrev.addEventListener("click", () => this.scrollTableDotsPrev());
		return buttonPrev;
	}

	private buildTableDotsNavigationNext(): HTMLElement {
		const buttonNext = this.renderer.createElement("button");
		const buttonArrowNext = this.renderer.createElement("i");
		this.renderer.setAttribute(buttonNext, "ds3-icon-button", "");
		this.renderer.addClass(buttonNext, "ds3-table-dots-button");
		this.renderer.addClass(buttonNext, "ds3-table-dots-button-next");
		this.renderer.addClass(buttonArrowNext, "pct");
		this.renderer.addClass(buttonArrowNext, "pct-chevrons-right");
		buttonNext.append(buttonArrowNext);

		buttonNext.addEventListener("click", () => this.scrollTableDotsNext());
		return buttonNext;
	}

	private scrollTableNext() {
		if (this.parentElement) {
			this.parentElement.scrollLeft += this.scrollStep;
			this.updateActiveDot();
		}
	}

	private scrollTablePrev() {
		if (this.parentElement) {
			this.parentElement.scrollLeft -= this.scrollStep;
			this.updateActiveDot();
		}
	}

	private scrollTableDots(targetDotIndex: number) {
		const parentComputedStyle = getComputedStyle(this.parentElement);
		const parentPaddingLeft = parseFloat(parentComputedStyle.paddingLeft);
		const parentPaddingRight = parseFloat(parentComputedStyle.paddingRight);

		if (this.parentElement) {
			let totalScrollableWidth =
				this.parentElement.offsetWidth -
				(parentPaddingLeft + parentPaddingRight);

			const targetScrollLeft = targetDotIndex * totalScrollableWidth;

			this.parentElement.scrollLeft = targetScrollLeft;

			this.updateActiveDot();
		}
	}

	private scrollTableDotsNext() {
		const parentComputedStyle = getComputedStyle(this.parentElement);
		const parentPaddingLeft = parseFloat(parentComputedStyle.paddingLeft);
		const parentPaddingRight = parseFloat(parentComputedStyle.paddingRight);

		if (this.parentElement) {
			const viewWidth =
				this.parentElement.offsetWidth -
				(parentPaddingLeft + parentPaddingRight);

			this.parentElement.scrollLeft = viewWidth * this.activeDot;
			this.updateActiveDot();
		}
	}

	private scrollTableDotsPrev() {
		const parentComputedStyle = getComputedStyle(this.parentElement);
		const parentPaddingLeft = parseFloat(parentComputedStyle.paddingLeft);
		const parentPaddingRight = parseFloat(parentComputedStyle.paddingRight);

		if (this.parentElement) {
			const viewWidth =
				this.parentElement.offsetWidth -
				(parentPaddingLeft + parentPaddingRight);
			this.parentElement.scrollLeft = viewWidth * (this.activeDot - 2);

			this.updateActiveDot();
		}
	}

	private updateActiveDot() {
		const parentComputedStyle = getComputedStyle(this.parentElement);
		const parentPaddingLeft = parseFloat(parentComputedStyle.paddingLeft);
		const parentPaddingRight = parseFloat(parentComputedStyle.paddingRight);

		const currentScrollLeft = this.parentElement.scrollLeft;
		const dotElements = this.dotsNav.querySelectorAll(".ds3-table-dot");

		let totalScrollableWidth =
			this.parentElement.offsetWidth - (parentPaddingLeft + parentPaddingRight);

		const dotCount = dotElements.length;

		dotElements.forEach((dot: HTMLElement, index: number) => {
			const startRange = index * totalScrollableWidth;
			const endRange = startRange + totalScrollableWidth;

			if (index === dotCount - 1) {
				if (currentScrollLeft >= startRange) {
					this.renderer.addClass(dot, "active");
					this.activeDot = index + 1;
				} else if (currentScrollLeft > (dotCount - 2) * totalScrollableWidth) {
					this.renderer.addClass(dot, "active");
					this.activeDot = index + 1;
					dotElements.forEach((dot: HTMLElement, index: number) => {
						if (index === dotCount - 1) return;
						this.renderer.removeClass(dot, "active");
					});
				} else {
					this.renderer.removeClass(dot, "active");
				}
			} else {
				if (currentScrollLeft >= startRange && currentScrollLeft < endRange) {
					this.renderer.addClass(dot, "active");
					this.activeDot = index + 1;
				} else {
					this.renderer.removeClass(dot, "active");
				}
			}
		});
	}
}
