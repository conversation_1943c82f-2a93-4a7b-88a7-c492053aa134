import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Ds3DataTableDirective } from "./directives/ds3-data-table.directive";
import { Ds3TableRowDirective } from "./directives/ds3-table-row.directive";
import { Ds3TableCellDirective } from "./directives/ds3-table-cell.directive";
import { Ds3TableHeaderCellDirective } from "./directives/ds3-table-header-cell.directive";
import { Ds3TableColumnDirective } from "./directives/ds3-table-column.directive";
import { Ds3DataTableFilterDirective } from "./directives/ds3-data-table-filter.directive";
import { Ds3TableLoadingDirective } from "./directives/ds3-table-loading.directive";
import { Ds3TableSortControlDirective } from "./directives/ds3-table-sort-control.directive";
import { CdkTableModule } from "@angular/cdk/table";
import { Ds3TableComponent } from "./ds3-table.component";
import { Ds3TableScrollDirective } from "./directives/ds3-table-scroll.directive";
import { Ds3ButtonModule } from "../ds3-button/ds3-button.module";
import { Ds3StatusModule } from "../ds3-status/ds3-status.module";
import { Ds3AvatarModule } from "../ds3-avatar/ds3-avatar.module";
import { Ds3TableEmptyRowDirective } from "./directives/ds3-table-empty-row.directive";

@NgModule({
	declarations: [
		Ds3DataTableFilterDirective,
		Ds3DataTableDirective,
		Ds3TableCellDirective,
		Ds3TableColumnDirective,
		Ds3TableHeaderCellDirective,
		Ds3TableLoadingDirective,
		Ds3TableRowDirective,
		Ds3TableSortControlDirective,
		Ds3TableComponent,
		Ds3TableScrollDirective,
		Ds3TableEmptyRowDirective,
	],
	imports: [
		CommonModule,
		Ds3ButtonModule,
		Ds3StatusModule,
		Ds3AvatarModule,
		CdkTableModule,
	],
	exports: [
		Ds3DataTableFilterDirective,
		Ds3DataTableDirective,
		Ds3TableCellDirective,
		Ds3TableColumnDirective,
		Ds3TableHeaderCellDirective,
		Ds3TableLoadingDirective,
		Ds3TableRowDirective,
		Ds3TableSortControlDirective,
		Ds3TableScrollDirective,
		Ds3TableComponent,
		Ds3TableEmptyRowDirective,
	],
})
export class Ds3TableModule {}
