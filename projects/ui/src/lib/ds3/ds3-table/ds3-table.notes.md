**Seletor:** `ds3-table`

**Utilização:** `<ds3-table></ds3-table>`

`<ds3-table>` é um componente usado para envolver e exibir tabelas de forma responsiva e adicionar comportamentos como fixar colunas em scroll horizontal. 

A tabela é encapsulada dentro da tag `<ds3-table>`.

Atributos Opcionais:
- `ds3FirstColumnFixed`: Fixa a primeira coluna.
- `ds3TableActionsFixed`: Fixa a coluna de ações.


### Uso Básico

Dentro do elemento `<ds3-table>`, você deve usar a tag `<table>` com a diretiva `ds3DataTable`. Nessa diretiva é onde os dados e colunas são configurados.

Importe o módulo Ds3Modules:

```ts
import { Ds3Modules } from 'ui-kit';

@NgModule({
    declarations: [...],
    imports: [
        ...
        Ds3Modules,
        ...
    ],
})
class MeuAppModule {}
```

> meu-exemplo-de-tela.component.html

```html
	<ds3-table>
		<table ds3DataTable [stateManager]="tableState">
			<!-- Configuração de colunas -->
		</table>
	<ds3-table>
```

### Colunas

As colunas são definidas com a diretiva ds3TableColumn em um contêiner `<ng-container>`. Cada coluna precisa de dois blocos: cabeçalho (`*ds3TableHeaderCell`) e célula (`*ds3TableCell`).

Exemplo:

```html

<ng-container ds3TableColumn="nome">
	<th *ds3TableHeaderCell>Nome</th>
	<td class="nome" *ds3TableCell="let item">
		{{ item.nome }}
	</td>
</ng-container>
```

O alinhamento das colunas pode ser definido através das classes `align-center` e `align-right`.

Exemplo:

```html

<ng-container ds3TableColumn="nome">
	<th class="align-center" *ds3TableHeaderCell>Nome</th>
	<td class="align-center" class="nome" *ds3TableCell="let item">
		{{ item.nome }}
	</td>
</ng-container>
```

#### Atributos:
- `ds3TableColumn="nome"`: Identifica a coluna com o nome único.
- `*ds3TableHeaderCell`: Define o conteúdo do cabeçalho.
- `*ds3TableCell`: Define o conteúdo de cada célula, acessando o dado com let item.

### Ordenação de Dados

Use o diretiva `*ds3TableSortControl` para configurar a ordenação.

Exemplo:

```html
<button
	ds3-icon-button
	*ds3TableSortControl="let direction = direction; let triggerSortToggle = triggerSortToggle"
	(click)="triggerSortToggle()">
	<i
		[ngClass]="{
			'pct-drop-down': direction === null,
			'pct-caret-up': direction === 'ASC',
			'pct-caret-down': direction === 'DESC'
		}">
	</i>
</button>
```

#### Propriedades:
- direction: Estado atual da ordenação (ASC, DESC, ou null).
- triggerSortToggle(): Alterna entre os estados de ordenação.

### Loading State

Use a diretiva `*ds3TableLoading` para exibir um indicador de carregamento enquanto os dados estão sendo buscados.

Exemplo:

```html

<tbody *ds3TableLoading>
  <tr>
    <td>
      <div class="spinner-border" role="status">
        <span class="sr-only">Carregando...</span>
      </div>
    </td>
  </tr>
</tbody>

```

### Tabela completa

```html
<table ds3DataTable [stateManager]="tableState">
	<ng-container ds3TableColumn="nome">
		<th *ds3TableHeaderCell>Nome</th>
		<td class="nome" *ds3TableCell="let item">
			{{ item.nome }}
		</td>
	</ng-container>

	<ng-container ds3TableColumn="idade">
		<th *ds3TableHeaderCell>Idade</th>
		<td class="idade" *ds3TableCell="let item">
			{{ item?.idade }}
		</td>
	</ng-container>

	<ng-container ds3TableColumn="cpf">
		<th class="align-center" *ds3TableHeaderCell>CPF</th>
		<td class="cpf" *ds3TableCell="let item">
			{{ item?.cpf }}
		</td>
	</ng-container>

	<tr *ds3TableRow></tr>

	<button
		ds3-icon-button
		*ds3TableSortControl="
			let direction = direction;
			let triggerSortToggle = triggerSortToggle
		"
		class="sort-control"
		(click)="triggerSortToggle()">
		<i
			class="pct pct-drop-down"
			[ngClass]="{
				'pct-drop-down': direction === null,
				'pct-caret-up': direction === 'ASC',
				'pct-caret-down': direction === 'DESC'
			}"></i>
	</button>

	<tbody *ds3TableLoading>
		<tr>
			<td>
				<div class="spinner-border" role="status">
					<span class="sr-only"></span>
				</div>
			</td>
		</tr>
	</tbody>
</table>
```

### Tabela com campo editável	

```html
<table ds3DataTable [stateManager]="tableState">
	<ng-container ds3TableColumn="nome">
		<th *ds3TableHeaderCell>Nome</th>
		<td class="nome" *ds3TableCell="let item">
			<ds3-form-field>
				<input ds3Input formControlName="cpf" />
			</ds3-form-field>
		</td>
	</ng-container>

	<ng-container ds3TableColumn="idade">
		<th *ds3TableHeaderCell>Idade</th>
		<td class="idade" *ds3TableCell="let item">
			<ds3-form-field>
				<input ds3Input formControlName="cpf" />
			</ds3-form-field>
		</td>
	</ng-container>

	<ng-container ds3TableColumn="cpf">
		<th class="align-center" *ds3TableHeaderCell>CPF</th>
		<td class="cpf" *ds3TableCell="let item">
			<ds3-form-field>
				<input ds3Input formControlName="cpf" />
			</ds3-form-field>
		</td>
	</ng-container>

	<tr *ds3TableRow></tr>

	<button
		ds3-icon-button
		*ds3TableSortControl="
			let direction = direction;
			let triggerSortToggle = triggerSortToggle
		"
		class="sort-control"
		(click)="triggerSortToggle()">
		<i
			class="pct pct-drop-down"
			[ngClass]="{
				'pct-drop-down': direction === null,
				'pct-caret-up': direction === 'ASC',
				'pct-caret-down': direction === 'DESC'
			}"></i>
	</button>

	<tbody *ds3TableLoading>
		<tr>
			<td>
				<div class="spinner-border" role="status">
					<span class="sr-only"></span>
				</div>
			</td>
		</tr>
	</tbody>
</table>
```

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
		form: FormGroup;

	constructor(private fb: FormBuilder) {}

	ngOnInit() {
		this.form = this.fb.group({
      nome: [''],
      idade: [''],
      cpf: [''],
    });
	}

	onSubmit() {
    if (this.form.valid) {
      console.log('Dados do formulário:', this.form.value);
    } else {
      console.log('Formulário inválido.');
    }
  }

}
```

### Estado

O estado da tabela é gerenciado por uma instância de PactoDataTableStateManager.

```ts
import { Component } from '@angular/core';
import { PactoLogoModule, PactoLogoSize } from 'ui-kit';

@Component({
	selector: 'meu-exemplo-de-tela',
	styleUrls: ['./meu-exemplo-de-tela.component.scss'],
	templateUrl: './meu-exemplo-de-tela.component.html'
})
class MeuExemploDeTelaComponent {
  tableState: PactoDataTableStateManager<any> = 
    new PactoDataTableStateManager();
}
```
PactoDataTableStateManager<any>:

- Classe que gerencia o estado da tabela.
- O tipo genérico (<any>) pode ser substituído por uma interface para tipar os dados retornados.

### ngOnInit
```ts
Copy code
ngOnInit() {
    this.updateTable({ currentPage: 1, pageSize: 10 });

    this.tableState.update$.subscribe((filter: PactoDataTableFilter) => {
        this.updateTable(filter);
    });
}
```

Função:

- Inicializa a tabela com os dados da primeira página (currentPage: 1, pageSize: 10).
- Inscreve-se no observable update$, que emite mudanças no estado da tabela, como alterações de filtros ou paginação.

updateTable():

- Chamado inicialmente para carregar dados com os valores padrão.
- É chamado novamente sempre que o estado da tabela muda.

### Código Completo
```ts
import { Component, OnInit } from '@angular/core';
import { PactoDataTableStateManager, PactoDataTableFilter } from 'pacto-data-table';
import { MockService } from './mock.service';

@Component({
    selector: 'app-table-example',
    templateUrl: './table-example.component.html',
    styleUrls: ['./table-example.component.scss']
})
export class TableExampleComponent implements OnInit {
    tableState: PactoDataTableStateManager<any> = 
        new PactoDataTableStateManager();

    constructor(private mock: MockService) {}

    ngOnInit() {
        this.updateTable({ currentPage: 1, pageSize: 10 });

        this.tableState.update$.subscribe((filter: PactoDataTableFilter) => {
            this.updateTable(filter);
        });
    }

    private updateTable(tableFilter: PactoDataTableFilter) {
        this.tableState.patchState({ loading: true });

        this.mock.getPeople(tableFilter).subscribe((result) => {
            this.tableState.patchState({
                ...result,
                loading: false,
            });
        });
    }
}
```

&nbsp;  

## Props / Inputs

| Property                                 | Values              | Default              |
|---                                       |                  ---|                   ---|
| @Input() tabs: array                     |                     |[]                    |

## Outputs / Events

No Outputs

## More info

No more info.
