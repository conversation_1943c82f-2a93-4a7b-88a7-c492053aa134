import { coerceBooleanProperty } from "@angular/cdk/coercion";
import {
	AfterContentInit,
	Component,
	ElementRef,
	HostListener,
	Input,
	OnInit,
	Renderer2,
	TemplateRef,
	ViewChild,
	ViewChildren,
	ViewEncapsulation,
	forwardRef,
	EventEmitter,
} from "@angular/core";
import {
	ControlValueAccessor,
	FormControl,
	NG_VALUE_ACCESSOR,
	Validators,
} from "@angular/forms";
import {
	MatCalendar,
	MatDialog,
	MatCalendarCellCssClasses,
	DateAdapter,
	MAT_DATE_LOCALE,
	MAT_DATE_FORMATS,
} from "@angular/material";
import { Ds3InputDateHeaderComponent } from "./ds3-input-date-header/ds3-input-date-header.component";
import { NgbDatepickerI18n } from "@ng-bootstrap/ng-bootstrap";
import {
	NgxMaterialTimepickerComponent,
	NgxMaterialTimepickerTheme,
} from "ngx-material-timepicker";
import moment from "moment";
import { Ds3ButtonComponent } from "../../ds3-button/ds3-button.component";

const FORMATO_PT_BR = {
	parse: {
		dateInput: "DD/MM/YYYY",
	},
	display: {
		dateInput: "DD/MM/YYYY",
		monthYearLabel: "MMMM YYYY",
		dateA11yLabel: "LL",
		monthYearA11yLabel: "MMMM YYYY",
	},
};

@Component({
	selector: "ds3-input-date",
	templateUrl: "./ds3-input-date.component.html",
	styleUrls: ["./ds3-input-date.component.scss"],
	encapsulation: ViewEncapsulation.None,
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => Ds3InputDateComponent),
			multi: true,
		},
		{ provide: MAT_DATE_LOCALE, useValue: "pt-BR" },
		{ provide: MAT_DATE_FORMATS, useValue: FORMATO_PT_BR },
	],
})
export class Ds3InputDateComponent
	implements OnInit, AfterContentInit, ControlValueAccessor
{
	private static sequence = 0;
	@Input() label: string;
	@Input() showLabelOnModal: boolean = true;
	@Input() dateType: "dateranger" | "datepicker" | "timeranger" | "timepicker" =
		"datepicker";
	@Input() format?: 12 | 24 = 24;
	@Input() position?;
	@Input() control?: FormControl;
	@Input() controlStart?: FormControl;
	@Input() controlEnd?: FormControl;
	@Input() separatorText?: string = "Até";
	@Input() startView?: "month" | "year" | "multi-year" = "month";
	@Input() reactive: boolean = true;

	@ViewChild("datePicker", { static: false }) datePicker: any;
	@ViewChild("dateRanger", { static: false }) dateRanger: TemplateRef<any>;
	@ViewChild("timePick", { static: false }) timePick: TemplateRef<any>;
	@ViewChild("timepicker", { static: false })
	timepicker: NgxMaterialTimepickerComponent;
	@ViewChild("calendarStart", { static: false })
	calendarStart: MatCalendar<any>;
	@ViewChild("calendarEnd", { static: false }) calendarEnd: MatCalendar<any>;
	@ViewChild("labelRef", { static: false }) labelRef;
	@ViewChild("inputRef", { static: false })
	inputRef: ElementRef<HTMLInputElement>;
	lastfocusedElement;

	openedDateRange: boolean = false;
	public dateMask = {
		guide: true,
		showMask: true,
		mask: [/\d/, /\d/, "/", /\d/, /\d/, "/", /\d/, /\d/, /\d/, /\d/],
	};

	ds3TimePicker: NgxMaterialTimepickerTheme = {
		container: {
			bodyBackgroundColor: "#FFFFFF",
			// primaryFontFamily:'',
			buttonColor: "#1E60FA",
		},

		dial: {
			dialBackgroundColor: "#FFFFFF",
			// dialInactiveColor: '',
			// dialActiveColor: '',
			// dialEditableActiveColor: '',
		},
		clockFace: {
			clockFaceTimeActiveColor: "#FFFFFF",
			clockFaceTimeInactiveColor: "#797D86",
			clockFaceInnerTimeInactiveColor: "#797D86",
			clockFaceTimeDisabledColor: "#797D86",
			clockFaceBackgroundColor: "#FAFAFA",
			clockHandColor: "#1E60FA",
		},
	};

	selectedDateStart;
	selectedDateEnd;
	inputDateRangeStart;
	inputDateRangeEnd;
	selectedPreset:
		| "ultimaSemana"
		| "ultimoMes"
		| "ultimos6Meses"
		| "ultimoAno"
		| "personalizado" = "personalizado";
	public dialogRef;
	public _internalValue;
	private _disabled = false;
	ds3InputDateHeaderComponent = Ds3InputDateHeaderComponent;

	private onChange: any = () => {};
	private onTouch: any = () => {};

	constructor(
		public readonly elementRef: ElementRef<HTMLElement>,
		private readonly renderer2: Renderer2,
		public i18n: NgbDatepickerI18n,
		public dialog: MatDialog,
		private _adapter: DateAdapter<any>
	) {}

	public ngOnInit(): void {
		this._adapter.setLocale("pt-br");
		moment.locale("pt-br");
		this.renderer2.addClass(this.elementRef.nativeElement, "ds3-input-date");

		if (
			this.dateType === "dateranger" &&
			this.controlStart &&
			this.controlEnd
		) {
			const hasInitialValue = this.controlStart.value && this.controlEnd.value;

			if (hasInitialValue) {
				this.inputDateRangeStart = this.controlStart.value;
				this.inputDateRangeEnd = this.controlEnd.value;
				this.selectedDateStart = this.controlStart.value;
				this.selectedDateEnd = this.controlEnd.value;
			}

			if (this.reactive) {
				this.controlStart.valueChanges.subscribe((value) => {
					this.inputDateRangeStart = value;
					this.selectedDateStart = value;
				});

				this.controlEnd.valueChanges.subscribe((value) => {
					this.inputDateRangeEnd = value;
					this.selectedDateEnd = value;
				});
			}
		}
	}

	public ngAfterContentInit(): void {
		this.generateAndSetId();
	}

	public get disabled() {
		return this._disabled;
	}

	@Input()
	public set disabled(value: boolean) {
		this._disabled = coerceBooleanProperty(value);
	}

	private generateAndSetId(): void {
		const currentId = this.elementRef.nativeElement.getAttribute("id");
		const id = currentId || String(Ds3InputDateComponent.sequence++);
		if (this.labelRef) {
			this.renderer2.setAttribute(this.inputRef, "id", `ds3-date-input__${id}`);
		}
		if (this.inputRef) {
			this.renderer2.setAttribute(
				this.labelRef,
				"for",
				`ds3-date-input__${id}`
			);
		}
	}

	public setDisabledState(isDisabled: boolean): void {
		this.disabled = isDisabled;
	}

	public writeValue(value: any): void {
		this._internalValue = value;

		// Handle date range values
		if (this.dateType === "dateranger" && value) {
			if (value.start) {
				this.selectedDateStart = value.start;
				this.inputDateRangeStart = value.start;
				if (this.controlStart) {
					this.controlStart.setValue(value.start, { emitEvent: false });
				}
			}
			if (value.end) {
				this.selectedDateEnd = value.end;
				this.inputDateRangeEnd = value.end;
				if (this.controlEnd) {
					this.controlEnd.setValue(value.end, { emitEvent: false });
				}
			}
		} else if (this.dateType === "datepicker" && value) {
			// Handle single date picker
			this.selectedDateStart = value;
			if (this.control) {
				this.control.setValue(value, { emitEvent: false });
			}
		}
	}

	public registerOnChange(fn: any): void {
		this.onChange = fn;
	}

	public registerOnTouched(fn: any): void {
		this.onTouch = fn;
	}

	@HostListener("blur", ["$event.target"])
	public blurHandler(target: HTMLInputElement) {
		this.onTouch();
	}

	public setValue(value: any) {
		if (this.disabled) {
			return;
		}
		// this.indeterminate = false;
		this._internalValue = value;
		this.onChange(this._internalValue);
		this.onTouch(this._internalValue);
	}

	onSelectStart(event, origin?) {
		try {
			// Allow null values for clearing dates
			if (event !== null && event !== undefined) {
				// Check if it's a valid date object or can be converted to one
				const testDate = event instanceof Date ? event : new Date(event);
				if (isNaN(testDate.getTime())) {
					console.warn("Invalid date provided to onSelectStart:", event);
					return;
				}
			}

			this.selectedDateStart = event;
			if (this.calendarEnd && event) {
				this.calendarEnd.minDate = event;
			}
			if (event !== null) {
				if (this.calendarStart) {
					this.calendarStart.updateTodaysDate();
				}
				if (this.calendarEnd) {
					this.calendarEnd.updateTodaysDate();
				}
			}
			if (this.calendarStart && event) {
				this.calendarStart.activeDate = event;
			}
			this.selectedPreset =
				origin === "click" ? "personalizado" : this.selectedPreset;

			// Update input display for date range
			if (this.dateType === "dateranger") {
				this.inputDateRangeStart = event;
			}
		} catch (error) {
			console.warn("Error in onSelectStart:", error);
		}
	}

	onSelectEnd(event, origin?) {
		try {
			// Allow null values for clearing dates
			if (event !== null && event !== undefined) {
				// Check if it's a valid date object or can be converted to one
				const testDate = event instanceof Date ? event : new Date(event);
				if (isNaN(testDate.getTime())) {
					console.warn("Invalid date provided to onSelectEnd:", event);
					return;
				}
			}

			this.selectedDateEnd = event;
			if (this.calendarStart && event) {
				this.calendarStart.maxDate = event;
				this.calendarStart.updateTodaysDate();
			}
			if (this.calendarEnd) {
				this.calendarEnd.updateTodaysDate();
				if (event) {
					this.calendarEnd.activeDate = event;
				}
			}
			this.selectedPreset =
				origin === "click" ? "personalizado" : this.selectedPreset;

			// Update input display for date range
			if (this.dateType === "dateranger") {
				this.inputDateRangeEnd = event;
			}
		} catch (error) {
			console.warn("Error in onSelectEnd:", error);
		}
	}

	setPreset(presetEscolhido) {
		const TODAY = new Date();
		const LASTWEEK = new Date(new Date().setDate(TODAY.getDate() - 7));
		const LASTMONTH = new Date(new Date().setMonth(TODAY.getMonth() - 1));
		const LAST6MONTH = new Date(new Date().setMonth(TODAY.getMonth() - 6));
		const LASTYEAR = new Date(new Date().setFullYear(moment().get("year") - 1));
		switch (presetEscolhido) {
			case "ultimaSemana":
				this.selectedPreset = "ultimaSemana";
				this.onSelectEnd(TODAY, "preset");
				this.onSelectStart(LASTWEEK, "preset");
				break;
			case "ultimoMes":
				this.selectedPreset = "ultimoMes";
				this.onSelectEnd(TODAY, "preset");
				this.onSelectStart(LASTMONTH, "preset");
				break;
			case "ultimos6Meses":
				this.selectedPreset = "ultimos6Meses";
				this.onSelectEnd(TODAY, "preset");
				this.onSelectStart(LAST6MONTH, "preset");
				break;
			case "ultimoAno":
				this.selectedPreset = "ultimoAno";
				this.onSelectEnd(TODAY, "preset");
				this.onSelectStart(LASTYEAR, "preset");
				break;
			case "personalizado":
				this.selectedPreset = "personalizado";
				this.onSelectEnd(null, "preset");
				this.onSelectStart(null, "preset");
				break;
			case "initialValue":
				this.selectedPreset = "personalizado";
				this.onSelectEnd(this.controlEnd.value, "click");
				this.onSelectStart(this.controlStart.value, "click");
				break;
		}
	}

	private dateClassCache = new Map<string, MatCalendarCellCssClasses>();
	private lastStartDate: Date | null = null;
	private lastEndDate: Date | null = null;

	dateClass() {
		return (date: Date): MatCalendarCellCssClasses => {
			try {
				// Validate input date
				if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
					return "";
				}

				// Clear cache if date range changed
				if (
					this.selectedDateStart !== this.lastStartDate ||
					this.selectedDateEnd !== this.lastEndDate
				) {
					this.dateClassCache.clear();
					this.lastStartDate = this.selectedDateStart;
					this.lastEndDate = this.selectedDateEnd;
				}

				const dateKey = date.toISOString().split("T")[0];
				if (this.dateClassCache.has(dateKey)) {
					return this.dateClassCache.get(dateKey)!;
				}

				let cssClass: MatCalendarCellCssClasses = "";

				if (this.selectedDateStart && this.selectedDateEnd) {
					const startMoment = moment(this.selectedDateStart);
					const endMoment = moment(this.selectedDateEnd);
					const dateMoment = moment(date);

					// Validate moments
					if (
						startMoment.isValid() &&
						endMoment.isValid() &&
						dateMoment.isValid()
					) {
						if (startMoment.isSame(dateMoment, "date")) {
							cssClass = "range-start";
						} else if (endMoment.isSame(dateMoment, "date")) {
							cssClass = "range-end";
						} else if (
							dateMoment.isAfter(startMoment) &&
							dateMoment.isBefore(endMoment)
						) {
							cssClass = "ranged";
						}
					}
				}

				this.dateClassCache.set(dateKey, cssClass);
				return cssClass;
			} catch (error) {
				console.warn("Error in dateClass:", error);
				return "";
			}
		};
	}

	openDialogDateRanger(
		event: MouseEvent,
		position:
			| "top-left"
			| "top-center"
			| "top-right"
			| "middle-left"
			| "middle-center"
			| "middle-right"
			| "bottom-left"
			| "bottom-center"
			| "bottom-right"
			| "middle-screen" = "middle-center"
	): void {
		let topVar: number;
		let leftVar: number;
		const wrapperElement = event.currentTarget as HTMLElement;
		const wrapperRect = wrapperElement.getBoundingClientRect();
		const WRAPPER_Y = wrapperRect.top;
		const WRAPPER_X = wrapperRect.left;
		const INPUT_WIDTH = 350;
		const INPUT_HEIGHT = 40;
		const INPUT_WITH_LABEL_HEIGHT = 66.8;
		const DIALOG_WIDTH = 690;
		const DIALOG_HEIGHT = 510;
		this.lastfocusedElement = event.currentTarget;

		const viewportWidth = window.innerWidth;
		switch (position) {
			case "top-left":
				topVar = WRAPPER_Y - DIALOG_HEIGHT - INPUT_WITH_LABEL_HEIGHT;
				leftVar = WRAPPER_X - DIALOG_WIDTH;
				break;

			case "top-center":
				topVar = WRAPPER_Y - DIALOG_HEIGHT - INPUT_WITH_LABEL_HEIGHT;
				leftVar = WRAPPER_X - INPUT_WIDTH / 2;
				break;

			case "top-right":
				topVar = WRAPPER_Y - DIALOG_HEIGHT - INPUT_WITH_LABEL_HEIGHT;
				leftVar = WRAPPER_X + INPUT_WIDTH;
				break;

			case "middle-left":
				topVar = WRAPPER_Y - DIALOG_HEIGHT / 2;
				leftVar = WRAPPER_X - DIALOG_WIDTH;
				break;

			case "middle-center":
				topVar = WRAPPER_Y - DIALOG_HEIGHT / 2;
				leftVar = WRAPPER_X - INPUT_WIDTH / 2;
				break;

			case "middle-right":
				topVar = WRAPPER_Y - DIALOG_HEIGHT / 2;
				leftVar = WRAPPER_X + INPUT_WIDTH;
				break;

			case "bottom-left":
				topVar = WRAPPER_Y + INPUT_HEIGHT;
				leftVar = WRAPPER_X - DIALOG_WIDTH;
				break;

			case "bottom-center":
				topVar = WRAPPER_Y + INPUT_HEIGHT;
				const margem = 50;
				if (viewportWidth - wrapperRect.right <= margem) {
					leftVar = WRAPPER_X - 20 - INPUT_WIDTH / 2;
				} else if (wrapperRect.left <= margem) {
					leftVar = WRAPPER_X + 20 - INPUT_WIDTH / 2;
				} else {
					leftVar = WRAPPER_X - INPUT_WIDTH / 2;
				}
				break;

			case "bottom-right":
				topVar = WRAPPER_Y + INPUT_HEIGHT;
				leftVar = WRAPPER_X + INPUT_WIDTH;
				break;
		}

		let dialogPosition;

		if (position !== "middle-screen") {
			dialogPosition = { top: `${topVar}px`, left: `${leftVar}px` };
		}

		this.dialogRef = this.dialog.open(this.dateRanger, {
			minWidth: "690px",
			minHeight: "510px",
			closeOnNavigation: false,
			disableClose: false,
			backdropClass: "bg-fundo",
			autoFocus: true,
			id: "date-ranger-dialog",
			position: dialogPosition,
		});

		this.dialogRef.afterOpened().subscribe(() => {
			if (this.calendarStart) {
				this.calendarStart.activeDate = this.controlStart.value;
			}
			if (this.calendarEnd) {
				this.calendarEnd.activeDate = this.controlEnd.value;
			}
			this.setPreset("initialValue");
			this.openedDateRange = true;
		});
		this.dialogRef.afterClosed().subscribe(() => {
			this.openedDateRange = false;
			this.lastfocusedElement.target.focus();
		});
	}

	onCancel(picker: any) {
		this.control.setValue(null);
		this.changeTime(null, this.control);
		picker.close();
	}

	limparFiltro() {
		this.inputDateRangeStart = new Date();
		this.inputDateRangeEnd = new Date();
		this.controlStart.setValue(new Date());
		this.controlEnd.setValue(new Date());
		this.onSelectStart(new Date(), "called");
		this.onSelectEnd(new Date(), "called");
	}

	aplicarFiltro() {
		this.inputDateRangeStart = this.selectedDateStart;
		this.inputDateRangeEnd = this.selectedDateEnd;

		if (this.controlStart) {
			this.controlStart.setValue(this.selectedDateStart);
		}
		if (this.controlEnd) {
			this.controlEnd.setValue(this.selectedDateEnd);
		}

		// Emit the value change for reactive forms
		if (this.dateType === "dateranger") {
			const rangeValue = {
				start: this.selectedDateStart,
				end: this.selectedDateEnd,
			};
			this.onChange(rangeValue);
			this._internalValue = rangeValue;
		}

		if (this.selectedDateStart && this.selectedDateEnd) {
			this.dialogRef.close();
		}
	}

	get inputValue() {
		return `${this.selectedDateStart} - ${this.selectedDateEnd}`;
	}

	changeTime(event, controle) {
		controle.setValue(event, { emitEvent: true });
	}

	manualEntry(event: KeyboardEvent) {
		try {
			let input = this.inputRef.nativeElement;
			if (!input || !input.value) {
				return;
			}

			let dividedString = input.value.split("/");
			if (input.value.length == 10 && dividedString.length === 3) {
				// Validate date parts
				const day = parseInt(dividedString[0], 10);
				const month = parseInt(dividedString[1], 10);
				const year = parseInt(dividedString[2], 10);

				if (
					isNaN(day) ||
					isNaN(month) ||
					isNaN(year) ||
					day < 1 ||
					day > 31 ||
					month < 1 ||
					month > 12 ||
					year < 1900
				) {
					return;
				}

				let data = new Date(year, month - 1, day);
				if (
					data &&
					data.toString() !== "Invalid Date" &&
					data.getFullYear() === year &&
					data.getMonth() === month - 1 &&
					data.getDate() === day
				) {
					this.datePicker.select(data);
					this.datePicker.close();
					this.inputRef.nativeElement.blur();
				}
			} else {
				if (input.value.length == 2 || input.value.length == 5) {
					if (!(event.code === "Delete" || event.code === "Backspace")) {
						input.value += "/";
					}
				}
			}
		} catch (error) {
			console.warn("Error in manualEntry:", error);
		}
	}

	datepickerOpenHandler() {
		if (
			this.inputRef &&
			this.inputRef.nativeElement &&
			document.activeElement !== this.inputRef.nativeElement
		) {
			setTimeout(() => {
				this.inputRef.nativeElement.focus();
				this.inputRef.nativeElement.select();
			}, 100);
		}
	}

	setFocus(element) {
		setTimeout(() => {
			element.elementRef.nativeElement.focus();
		}, 100);
	}
}
