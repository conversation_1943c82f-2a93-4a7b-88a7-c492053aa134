@import "projects/ui/assets/ds3/_ds3-charts.scss";

.apexcharts-xaxistooltip {
	display: none !important;
}

.tooltip-container {
	padding: 8px 12px;
	display: flex;
	flex-direction: column;
	background: #303236cc;
	color: #ffffff;

	.tooltip-header {
		font-family: "Nunito Sans", sans-serif;
		font-size: 14px;
		font-weight: 400;
		line-height: 17.5px;
		text-align: left;
		text-underline-position: from-font;
		text-decoration-skip-ink: none;
		border-bottom: 1px solid #c9cbcf;
		color: #ffffff;
	}

	.tooltip-text {
		margin-top: 12px;
		font-family: "Nunito Sans", sans-serif;
		font-size: 12px;
		font-weight: 400;
		line-height: 16px;
		color: #ffffff;
	}
}

.apexcharts-xaxis-label {
	transform: rotate(90deg); // Ajuste o ângulo conforme necessário
	transform-origin: center;
	white-space: nowrap; // Evita quebra de texto
	display: inline-block; // Garante que o texto respeite a rotação
}
