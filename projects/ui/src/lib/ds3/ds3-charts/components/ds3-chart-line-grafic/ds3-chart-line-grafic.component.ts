import {
	ChangeDetectorRef,
	Component,
	Input,
	OnChanges,
	OnInit,
	SimpleChanges,
	ViewEncapsulation,
} from "@angular/core";

@Component({
	selector: "ds3-chart-line-grafic",
	templateUrl: "./ds3-chart-line-grafic.component.html",
	styleUrls: ["./ds3-chart-line-grafic.component.scss"],
	encapsulation: ViewEncapsulation.None,
})
export class Ds3ChartLineGraficComponent implements OnInit, OnChanges {
	private chartInstance;

	constructor(private cd: ChangeDetectorRef) {}

	ngOnInit() {
		this.initCharts();
	}

	ngOnChanges(changes: SimpleChanges): void {
		setTimeout(() => {
			this.initCharts();
		});
	}

	public initCharts(): void {
		if (this.chartInstance) {
			this.chartInstance.destroy();
		}

		this.chartInstance = new ApexCharts(
			document.querySelector(`#${"testeId"}`),
			this.getOptions()
		);
		this.chartInstance.render();
	}

	private getOptions() {
		const options = {
			series: [
				{
					name: "Meta",
					data: [
						{
							x: new Date("14 Nov 2017").getTime(),
							y: 3,
							metaDoDia: 1500,
							metaAlcancada: 800,
						},
						{
							x: new Date("15 Nov 2017").getTime(),
							y: 1,
							metaDoDia: 400,
							metaAlcancada: 350,
						},
						{
							x: new Date("23 Nov 2017").getTime(),
							y: 3,
							metaDoDia: 600,
							metaAlcancada: 400,
						},
						{
							x: new Date("26 Nov 2017").getTime(),
							y: -2,
							metaDoDia: 100,
							metaAlcancada: 99,
						},
						{
							x: new Date("29 Nov 2017").getTime(),
							y: 5,
							metaDoDia: 300,
							metaAlcancada: 250,
						},
						{
							x: new Date("02 Dec 2017").getTime(),
							y: 1,
							metaDoDia: 800,
							metaAlcancada: 800,
						},
						{
							x: new Date("05 Dec 2017").getTime(),
							y: 0,
							metaDoDia: 900,
							metaAlcancada: 210,
						},
						{
							x: new Date("08 Dec 2017").getTime(),
							y: 2,
							metaDoDia: 150,
							metaAlcancada: 100,
						},
					],
				},
			],

			chart: {
				toolbar: {
					show: false,
				},
				height: 350,
				type: "line",
				id: "areachart-2",

				zoom: {
					enabled: false,
				},
			},
			yaxis: {
				labels: { show: false },
				axisBorder: { show: false },
				axisTicks: { show: false },
				lines: { show: false },
			},

			annotations: {
				yaxis: [
					{
						y: 0,
						strokeDashArray: 0,
						label: {
							borderWidth: 0,
							text: "Meta do dia",
							textAnchor: "end",
							position: "left",
							offsetX: -10,
							style: {
								color: "#797D86",
							},
						},
					},
				],
				xaxis: [
					{
						x: new Date("15 Nov 2017").getTime(),
						y: 100,
						strokeDashArray: 3,
						borderColor: "#7B169C",
						label: {
							borderColor: "#775DD0",
							position: "bottom",
							style: {
								color: "#fff",
								background: "#775DD0",
								cssClass: "apexcharts-xaxis-label",
							},
							text: "1º Meta batida",
						},
					},
					{
						x: new Date("23 Nov 2017").getTime(),
						strokeDashArray: 3,
						borderColor: "#163E9C",
						label: {
							borderColor: "#775DD0",
							style: {
								color: "#fff",
								background: "#775DD0",
							},
							text: "2º Meta batida",
						},
					},
					{
						x: new Date("29 Nov 2017").getTime(),
						strokeDashArray: 3,
						borderColor: "#167B9C",
						label: {
							borderColor: "#775DD0",
							style: {
								color: "#fff",
								background: "#775DD0",
							},
							text: "3º Meta batida",
						},
					},
				],
				// points: [{
				//   x: new Date('01 Dec 2017').getTime(),

				//   marker: {
				//     size: 8,
				//     fillColor: '#fff',
				//     strokeColor: 'red',
				//     radius: 2,
				//     cssClass: 'apexcharts-custom-class'
				//   },
				//   label: {
				//     borderColor: '#FF4560',
				//     offsetY: 0,
				//     style: {
				//       color: '#fff',
				//       background: '#FF4560',
				//     },

				//     text: 'Point Annotation',
				//   }
				// }, {
				//   x: new Date('08 Dec 2017').getTime(),
				//   y: 9340.85,
				//   marker: {
				//     size: 0
				//   },
				//   image: {
				//     path: '../../assets/images/ico-instagram.png'
				//   }
				// }]
			},
			dataLabels: {
				enabled: false,
			},
			stroke: {
				curve: "straight",
			},
			grid: {
				show: false,
				padding: {
					top: 0,
					right: 30,
					bottom: 0,
					left: 60,
				},
			},
			xaxis: {
				labels: {
					show: false,
				},
				axisBorder: {
					show: false,
				},
				axisTicks: {
					show: false,
				},
				type: "datetime",
				hidden: false,
			},
			tooltip: {
				enabled: true, // Desativa o tooltip padrão
				custom: ({ series, seriesIndex, dataPointIndex, w }) => {
					const dataPoint =
						w.globals.initialSeries[seriesIndex].data[dataPointIndex];
					if (!dataPoint) {
						return "";
					}
					const date = new Date(dataPoint.x).toLocaleDateString("pt-BR");
					const metaDoDia = dataPoint.metaDoDia || 0;
					const metaAlcancada = dataPoint.metaAlcancada || 0;
					const percentualAcima = metaDoDia
						? ((metaAlcancada - metaDoDia) / metaDoDia) * 100
						: 0;

					return `
            <div class="tooltip-container">
              <span class="tooltip-header">Dia ${date}</span>
              <span class="tooltip-text">Meta do dia: R$ ${metaDoDia.toFixed(
								2
							)}</span>
              <span class="tooltip-text">Meta alcançada: R$ ${metaAlcancada.toFixed(
								2
							)}</span>
              <span class="tooltip-text">${percentualAcima.toFixed(
								2
							)}% acima do esperado</span>
            </div>
          `;
				},
			},
		};
		return options;
	}
}
