import { Injectable } from "@angular/core";
import { CatTolltipComponent } from "../cat-tolltip/cat-tolltip.component";

@Injectable({
	providedIn: "root",
})
export class TooltipManagerService {
	private tooltips: Set<CatTolltipComponent> = new Set<CatTolltipComponent>();
	private clickListener: ((event: Event) => void) | null = null;
	private isListenerActive: boolean = false;

	constructor() {}

	/**
	 * Registra um tooltip no gerenciador global
	 * @param tooltip Instância do componente tooltip
	 */
	public registerTooltip(tooltip: CatTolltipComponent): void {
		this.tooltips.add(tooltip);

		// Se é o primeiro tooltip, configura o listener global
		if (this.tooltips.size === 1 && !this.isListenerActive) {
			this.setupGlobalListener();
		}
	}

	/**
	 * Remove um tooltip do gerenciador global
	 * @param tooltip Instância do componente tooltip
	 */
	public unregisterTooltip(tooltip: CatTolltipComponent): void {
		this.tooltips.delete(tooltip);

		// Se não há mais tooltips, remove o listener global
		if (this.tooltips.size === 0 && this.isListenerActive) {
			this.removeGlobalListener();
		}
	}

	/**
	 * Configura o listener global único para todos os tooltips
	 */
	private setupGlobalListener(): void {
		if (this.isListenerActive) {
			return;
		}

		this.clickListener = (event: Event) => {
			this.handleGlobalClick(event);
		};

		document.addEventListener("click", this.clickListener, { passive: true });
		this.isListenerActive = true;
	}

	/**
	 * Remove o listener global
	 */
	private removeGlobalListener(): void {
		if (!this.isListenerActive || !this.clickListener) {
			return;
		}

		document.removeEventListener("click", this.clickListener);
		this.clickListener = null;
		this.isListenerActive = false;
	}

	/**
	 * Manipula cliques globais e notifica apenas tooltips visíveis
	 * @param event Evento de clique
	 */
	private handleGlobalClick(event: Event): void {
		const target = event.target as Element;

		if (!target) {
			return;
		}

		// Itera apenas sobre tooltips visíveis para otimizar performance
		this.tooltips.forEach((tooltip: CatTolltipComponent) => {
			if (tooltip.isVisible()) {
				tooltip.handleDocumentClick(event);
			}
		});
	}

	/**
	 * Retorna o número de tooltips registrados (para debug)
	 */
	public getTooltipCount(): number {
		return this.tooltips.size;
	}

	/**
	 * Verifica se o listener global está ativo (para debug)
	 */
	public isGlobalListenerActive(): boolean {
		return this.isListenerActive;
	}

	/**
	 * Força a limpeza de todos os tooltips (para casos extremos)
	 */
	public clearAllTooltips(): void {
		this.tooltips.forEach((tooltip: CatTolltipComponent) => {
			tooltip.forceHide();
		});
	}

	/**
	 * Cleanup manual do service (se necessário)
	 */
	public destroy(): void {
		this.removeGlobalListener();
		this.tooltips.clear();
	}
}
