import { BiGlobalFilter } from "@adm/modules/bi/bi-shared/models/bi-global.filter";
import { BiSidenavRef } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "adm-bi-card-movientacao-contratos-filter",
	templateUrl: "./bi-card-movientacao-contratos-filter.component.html",
	styleUrls: ["./bi-card-movientacao-contratos-filter.component.scss"],
})
export class BiCardMovientacaoContratosFilterComponent implements OnInit {
	globalFilter: BiGlobalFilter;

	form: FormGroup = new FormGroup({
		data: new FormControl(),
		desconsiderarCancelamentoMudancaPlano: new FormControl(),
	});

	filters: {
		data: Date;
		desconsiderarCancelamentoMudancaPlano: boolean;
	};

	constructor(
		private cd: ChangeDetectorRef,
		private biSidenavRef: BiSidenavRef<BiCardMovientacaoContratosFilterComponent>
	) {}

	ngOnInit() {
		this._populateByGlobalFilter();
		this._populateFormByFilters();
	}

	private _populateByGlobalFilter() {
		if (this.globalFilter) {
			this.form.get("data").setValue(this.globalFilter.dataGeral);
		}
	}

	private _populateFormByFilters() {
		if (this.filters) {
			this.form.patchValue({
				data: this.filters.data,
				desconsiderarCancelamentoMudancaPlano:
					this.filters.desconsiderarCancelamentoMudancaPlano,
			});
		}
	}

	onClose() {
		this.biSidenavRef.close();
	}

	onFilter() {
		if (!this.filters) {
			this.filters = this.form.value;
		} else {
			Object.assign(this.filters, this.form.value);
		}
		this.biSidenavRef.close({
			filters: {
				...this.filters,
			},
		});
	}

	selectDesconsiderarCancelamentoMudancaPlano(selected: boolean) {
		this.form.get("desconsiderarCancelamentoMudancaPlano").setValue(selected);
	}

	onClear() {
		this.form.reset({});
		this._populateByGlobalFilter();
		this.biSidenavRef.close({ filters: this.form.value });
	}
}
