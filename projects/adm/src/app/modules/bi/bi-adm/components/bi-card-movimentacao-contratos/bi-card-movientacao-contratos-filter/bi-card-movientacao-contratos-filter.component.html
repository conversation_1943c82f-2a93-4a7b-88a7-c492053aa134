<adm-bi-side-filter-base-content
	(closeSidenav)="onClose()"
	(filter)="onFilter()"
	(clear)="onClear()">
	<form [formGroup]="form">
		<ds3-form-field>
			<ds3-field-label>Data</ds3-field-label>
			<ds3-input-date
				ds3Input
				[control]="form.controls['data']"></ds3-input-date>
		</ds3-form-field>

		<ds3-form-field style="margin-top: 16px; display: block">
			<ds3-field-label>Cancelados</ds3-field-label>
			<ds3-chips
				(selectionChange)="selectDesconsiderarCancelamentoMudancaPlano($event)"
				[isActive]="
					form.controls['desconsiderarCancelamentoMudancaPlano'].value
				">
				Desconsiderar cancelados por mudança de plano
			</ds3-chips>
		</ds3-form-field>
	</form>
</adm-bi-side-filter-base-content>
