import { BiGlobalFilter } from "@adm/modules/bi/bi-shared/models/bi-global.filter";
import { BiSidenavRef } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import {
	AdmCoreApiGrupoColaboradorService,
	ApiResponseList,
	Colaborador,
	GrupoColaborador,
	GrupoColaboradorParticipante,
} from "adm-core-api";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";

@Component({
	selector: "adm-bi-card-pendencias-de-clientes-filter",
	templateUrl: "./bi-card-pendencias-de-clientes-filter.component.html",
	styleUrls: ["./bi-card-pendencias-de-clientes-filter.component.scss"],
})
export class BiCardPendenciasDeClientesFilterComponent
	implements OnInit, OnDestroy
{
	globalFilter: BiGlobalFilter;

	form: FormGroup = new FormGroup({
		data: new FormControl(),
		gruposColaboradores: new FormControl(),
		colaboradores: new FormControl(),
	});

	filters: {
		data: Date;
		gruposColaboradores: Array<number>;
		colaboradores: Array<number>;
		colaboradoresObj: Array<{ label: string; value: any }>;
		gruposColaboradoresObj: Array<GrupoColaborador>;
	};

	gruposColaboradores: Array<GrupoColaborador> = new Array<GrupoColaborador>();
	optionsColaboradores: Array<{ value: any; label: string }> = new Array<{
		value: any;
		label: string;
	}>();

	private _destroyed$: Subject<void> = new Subject<void>();

	constructor(
		private admCoreApiGrupoColaboradorService: AdmCoreApiGrupoColaboradorService,
		private biSidenavRef: BiSidenavRef<BiCardPendenciasDeClientesFilterComponent>,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this._loadgruposColaboradores();
		this._populateByGlobalFilter();
		this._populateFormByFilters();
		this._listenValueChanges();
	}

	ngOnDestroy() {
		this._destroyed$.next();
	}

	private _populateByGlobalFilter() {
		if (this.globalFilter) {
			this.form.get("data").setValue(this.globalFilter.dataGeral);
		}
	}

	private _populateFormByFilters() {
		if (this.filters) {
			this.form.patchValue({
				data: this.filters.data,
				gruposColaboradores: this.filters.gruposColaboradores,
			});
		}
	}

	private _loadgruposColaboradores() {
		this.admCoreApiGrupoColaboradorService
			.findByEmpresaLogada()
			.subscribe((response: ApiResponseList<GrupoColaborador>) => {
				this.gruposColaboradores = response.content;
				this.gruposColaboradores = this.gruposColaboradores.map((v) => {
					if (v.descricao === "SEM GRUPO") {
						v.codigo = -1;
					}
					return v;
				});
				const colaboradores = [];
				const grupoColaboradores: Array<GrupoColaboradorParticipante> = [];
				const colaboradoresDistinct: Map<number, Colaborador> = new Map<
					number,
					Colaborador
				>();
				this.gruposColaboradores.forEach((gc) =>
					grupoColaboradores.push(...gc.grupoColaboradorParticipantes)
				);
				grupoColaboradores.forEach((v) => {
					if (!colaboradoresDistinct.has(v.codigo)) {
						colaboradoresDistinct.set(
							v.colaboradorParticipante.codigo,
							v.colaboradorParticipante
						);
					}
				});
				colaboradoresDistinct.forEach((v) => {
					colaboradores.push({ value: v.codigo, label: v.pessoa.nome });
				});
				colaboradores.forEach((c) => this.optionsColaboradores.push(c));
				this.optionsColaboradores = this.optionsColaboradores.sort((c1, c2) => {
					if (c1.label > c2.label) {
						return 1;
					} else if (c1.label < c2.label) {
						return -1;
					} else {
						return 0;
					}
				});

				if (this.filters && this.filters.colaboradores) {
					this.form.get("colaboradores").setValue(this.filters.colaboradores);
				}
				this.cd.detectChanges();
			});
	}

	onClose() {
		this.biSidenavRef.close();
	}

	onFilter() {
		if (!this.filters) {
			this.filters = this.form.value;
		} else {
			Object.assign(this.filters, this.form.value);
		}
		this._setObjectValuesForFilters();
		this.biSidenavRef.close({
			filters: {
				...this.filters,
				colaboradoresNomes: this.optionsColaboradores
					.filter(
						(v) =>
							this.form.value.colaboradores &&
							this.form.value.colaboradores.includes(v.value)
					)
					.map((v) => v.label),
			},
		});
	}

	private _setObjectValuesForFilters() {
		if (this.filters) {
			if (this.filters.colaboradores) {
				const filterOptions = this.optionsColaboradores.filter((v) =>
					this.filters.colaboradores.includes(v.value)
				);

				if (filterOptions) {
					this.filters.colaboradoresObj = filterOptions;
				}
			}
			if (this.filters.gruposColaboradores) {
				const filterOptions = this.gruposColaboradores.filter((v) =>
					this.filters.gruposColaboradores.includes(v.codigo)
				);

				if (filterOptions) {
					this.filters.gruposColaboradoresObj = filterOptions;
				}
			}
		}
	}

	onClear() {
		this.form.reset({});
		this._populateByGlobalFilter();
		this.biSidenavRef.close({ filters: this.form.value });
	}

	private _listenValueChanges() {
		this._listenGrupoColaboradorChanges();
	}

	private _listenGrupoColaboradorChanges() {
		this.form
			.get("gruposColaboradores")
			.valueChanges.pipe(takeUntil(this._destroyed$))
			.subscribe((value) => {
				const grupoColaboradorSelecionado = this.gruposColaboradores.filter(
					(gc) => value.includes(gc.codigo)
				);

				if (grupoColaboradorSelecionado) {
					const colaboradores = new Array<number>();
					const colaboradoresDistinct: Map<number, Colaborador> = new Map<
						number,
						Colaborador
					>();
					const grupoColaboradores: Array<GrupoColaboradorParticipante> = [];
					grupoColaboradorSelecionado.forEach((gcs) => {
						grupoColaboradores.push(...gcs.grupoColaboradorParticipantes);
					});
					grupoColaboradores.forEach((v) => {
						if (!colaboradoresDistinct.has(v.codigo)) {
							colaboradoresDistinct.set(
								v.colaboradorParticipante.codigo,
								v.colaboradorParticipante
							);
						}
					});
					colaboradoresDistinct.forEach((v) => {
						colaboradores.push(v.codigo);
					});

					this.form.get("colaboradores").setValue(colaboradores);
					this.cd.detectChanges();
				}
			});
	}
}
