import { AdmRestService } from "@adm/adm-rest.service";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import {
	Component,
	Inject,
	OnDestroy,
	OnInit,
	Pipe,
	PipeTransform,
} from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiPendenciaClientesService,
	ApiResponseList,
	ClienteAniversarianteModel,
	FiltroBIModel,
	IndicadorPendenciaClientesEnum,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import {
	LayoutNavigationService,
	PermissaoService,
	PlataformModuleConfig,
	PlatformMenuItem,
} from "pacto-layout";
import { Observable, Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";

@Pipe({
	name: "objectValue",
})
export class ObjectValuePipe implements PipeTransform {
	transform(
		object: any,
		attribute: string,
		valueTransform?: (v) => string
	): any {
		const attrArr = attribute.split(".");

		let value: any = object;

		attrArr.forEach((key) => {
			value = value[key];
		});

		if (valueTransform) {
			value = valueTransform(value);
		} else if (!value || value === "") {
			value = "-";
		}

		return value;
	}
}

@Component({
	selector: "adm-bi-pendencias-cliente-modal",
	templateUrl: "./bi-pendencias-cliente-modal.component.html",
	styleUrls: [
		"./bi-pendencias-cliente-modal.component.scss",
		"../../../../../bi-shared/bi-shared.scss",
	],
})
export class BiPendenciasClienteModalComponent implements OnInit, OnDestroy {
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "matricula",
			visible: true,
			titulo: "Matrícula",
			ordenavel: true,
			nome: "matricula",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "pessoa.nome",
			visible: true,
			titulo: "Nome",
			ordenavel: true,
			nome: "pessoa",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "contratoRenovado",
			visible: true,
			titulo: "Renovou contrato",
			ordenavel: true,
			date: true,
			nome: "contratoRenovado",
			inputType: "date",
		},
		{
			mostrarTitulo: true,
			campo: "verificadoEm",
			visible: true,
			titulo: "Verificado em",
			ordenavel: true,
			date: true,
			nome: "verificadoEm",
			inputType: "date",
		},
		{
			mostrarTitulo: true,
			campo: "usuarioVerificacao",
			visible: true,
			titulo: "Verificado por",
			ordenavel: true,
			nome: "usuarioVerificacao",
			inputType: "text",
		},
	];
	modalTitle: string;

	filters: FiltroBIModel = {} as FiltroBIModel;
	shareUrl: string = this.admRest.buildFullUrlAdmCore("pendencia-clientes");
	indicador: IndicadorPendenciaClientesEnum;
	IndicadorPendenciaClientesEnum = IndicadorPendenciaClientesEnum;

	columns: Array<any> = new Array<any>();

	private _destroy$: Subject<void> = new Subject();
	private orderBy: string = "pessoa";
	private orderDirection: "ASC" | "DESC" = "ASC";

	constructor(
		private dialog: MatDialogRef<BiPendenciasClienteModalComponent>,
		private admCoreApiPendenciaClientesService: AdmCoreApiPendenciaClientesService,
		private admRest: AdmRestService,
		private biCommonService: BiCommonService,
		private toastrService: ToastrService,
		private layoutNavigationService: LayoutNavigationService,
		private permissaoService: PermissaoService,
		private objectValuePipe: ObjectValuePipe,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = `${this.modalData.modalTitle}`;
			}
			this.indicador = this.modalData.indicador;
		}
		this._configColumnByIndicador();
		this._configFiltersByIndicador();
		this._loadData();
		this._tableStateUpdated();
		this.shareUrl = `${this.shareUrl}?indicador=${this.indicador}`;
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	quickSearch(value) {
		this.filters.quickSearchValue = value;
		this.page = 0;
		this._loadData();
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}

	openCliente(item: any, attribute: string = "matricula") {
		this.biCommonService.openCliente(
			this.objectValuePipe.transform(item, attribute)
		);
	}

	openColaborador(item: any) {
		const permition = this.permissaoService.temRecursoAdm("2.07");
		if (permition) {
			const nomeFuncionalidade = item.codigo
				? `COLABORADOR&codigoColaborador=${item.codigo}`
				: `COLABORADOR`;
			const menu: PlatformMenuItem = {
				id: "Colaborador",
				route: {
					queryParams: {
						funcionalidadeNome: nomeFuncionalidade,
						openAsPopup: true,
						windowWidth: 1070,
						windowHeight: 600,
					},
				},
			};
			this.layoutNavigationService.makeRedirect(
				this.layoutNavigationService.redirectToModule(
					PlataformModuleConfig.ADM_LEGADO,
					menu
				) as Observable<string>
			);
		} else {
			this.toastrService.warning(
				"Você não possui as permissões necessárias (2.07 - Colaborador) para acessar esta tela."
			);
			return;
		}
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this.admCoreApiPendenciaClientesService
			.pendenciasClientes({
				filtroBI: this.filters,
				indicador: this.indicador,
				page: this.page,
				size: this.size,
				orderBy: this.orderBy,
				orderDirection: this.orderDirection,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (response: ApiResponseList<ClienteAniversarianteModel>) => {
					this._populateCorSituacao(response.content);
					this.totalItems = response.totalElements;
					this.tableState.patchState({
						data: response.content,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
						orderBy: this.orderBy,
						orderDirection: this.orderDirection,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	private _tableStateUpdated() {
		this.tableState.update$
			.pipe(takeUntil(this._destroy$))
			.subscribe((value) => {
				this.orderBy = value.orderBy;
				this.orderDirection = value.orderDirection;

				this._loadData();
			});
	}

	private _configColumnByIndicador() {
		switch (this.indicador) {
			case IndicadorPendenciaClientesEnum.PARCELAS_EM_ATRASO:
				this._columnsParcelaEmAtraso();
				break;
			case IndicadorPendenciaClientesEnum.PARCELAS_EM_ABERTO:
				this._columnsParcelaEmAberto();
				break;
			case IndicadorPendenciaClientesEnum.PARCELAS_EM_ABERTO_COLABORADOR:
				this._columnsParcelaEmAbertoColaborador();
				break;
			case IndicadorPendenciaClientesEnum.CLIENTES_ANIVERSARIANTES:
				this._columnsClienteAniversariante();
				break;
			case IndicadorPendenciaClientesEnum.COLABORADOR_ANIVERSARIANTES:
				this._columnsColaboradorAniversariante();
				break;
		}

		this.colunasShare = this.columns.map((column) => ({
			mostrarTitulo: true,
			campo: column.celulaSituacaoCliente
				? "cliente.situacaoApresentar"
				: column.attribute,
			visible: true,
			titulo: column.label,
			ordenavel: true,
			nome: column.attribute,
			date: column.date,
			inputType: "text",
			decimal: column.money || column.decimal,
		}));
	}

	private _columnsClienteAniversariante() {
		this.columns.push(
			{
				id: "pessoa",
				label: "Nome",
				attribute: "cliente.pessoa.nome",
				titlecase: true,
				clickable: true,
				onClick: (item) => this.openCliente(item),
			},
			{
				id: "matricula",
				label: "Matrícula",
				attribute: "cliente.codigoMatricula",
				textColumn: true,
			},
			{
				id: "dataNascimento",
				label: "Dt. de nascimento",
				attribute: "cliente.pessoa.dataNascimento",
				date: true,
			},
			{
				id: "situacaoCliente",
				label: "Situação",
				attribute: "cliente.situacao",
				celulaSituacaoCliente: true,
			},
			{
				id: "cpf",
				label: "CPF",
				attribute: "cliente.pessoa.cpf",
				textColumn: true,
			},
			{
				id: "nomePlano",
				label: "Plano",
				attribute: "nomePlano",
				textColumn: true,
			},
			{
				id: "duracaoContrato",
				label: "Duração",
				attribute: "duracaoContrato",
				textColumn: true,
			},
			{
				id: "telefonesCliente",
				label: "Telefone",
				attribute: "cliente.telefonesCliente",
				textColumn: true,
			}
		);
	}

	private _columnsColaboradorAniversariante() {
		this.columns.push(
			{
				id: "pessoa",
				label: "Nome",
				attribute: "pessoa.nome",
				titlecase: true,
				clickable: this.permissaoService.temRecursoAdm("2.07"),
				onClick: (item) => this.openColaborador(item),
			},
			{
				id: "situacao",
				label: "Situação",
				attribute: "situacao",
				textColumn: true,
				valueTransform: (v) => {
					if (["IN", "NA"].includes(v)) {
						return "Inativo";
					}
					return "Ativo";
				},
			},
			{
				id: "cpf",
				label: "CPF",
				attribute: "cpf",
				textColumn: true,
			}
		);
	}

	private _columnsParcelaEmAtraso() {
		this.orderBy = "nome";
		this.columns.push(
			{
				id: "nome",
				label: "Nome",
				attribute: "cliente.pessoa.nome",
				titlecase: true,
				clickable: true,
				onClick: (item) => this.openCliente(item, "cliente.codigoMatricula"),
			},
			{
				id: "matricula",
				label: "Matrícula",
				attribute: "cliente.codigoMatricula",
				textColumn: true,
			},
			{
				id: "situacaoCliente",
				label: "Situação",
				attribute: "cliente.situacao",
				celulaSituacaoCliente: true,
			},
			{
				id: "cpf",
				label: "CPF",
				attribute: "cliente.pessoa.cpf",
				textColumn: true,
			},
			{
				id: "codContrato",
				label: "Contrato",
				attribute: "codContrato",
				textColumn: true,
				valueTransform: (v) => {
					if (!v) {
						v = 0;
					}
					return v;
				},
			},
			{
				id: "qtdParcelas",
				label: "Qtd. Atrasadas",
				attribute: "qtdParcelas",
				textColumn: true,
			},
			{
				id: "valorEmAberto",
				label: "Valor",
				attribute: "valorEmAberto",
				money: true,
			},
			{
				id: "nomePlano",
				label: "Plano",
				attribute: "nomePlano",
				textColumn: true,
			},
			{
				id: "duracaoContrato",
				label: "Duração",
				attribute: "duracaoContrato",
				textColumn: true,
				valueTransform: (v) => {
					if (!v) {
						v = 0;
					}
					return v;
				},
			},
			{
				id: "telefonescliente",
				label: "Telefone",
				attribute: "cliente.telefones",
				textColumn: true,
			}
		);
	}

	private _columnsParcelaEmAberto() {
		this.orderBy = "nome";
		this.columns.push(
			{
				id: "nome",
				label: "Nome",
				attribute: "cliente.pessoa.nome",
				titlecase: true,
				clickable: true,
				onClick: (item) => this.openCliente(item, "cliente.codigoMatricula"),
			},
			{
				id: "matricula",
				label: "Matrícula",
				attribute: "cliente.codigoMatricula",
				textColumn: true,
			},
			{
				id: "situacaoCliente",
				label: "Situação",
				attribute: "cliente.situacao",
				celulaSituacaoCliente: true,
			},
			{
				id: "cpf",
				label: "CPF",
				attribute: "cliente.pessoa.cpf",
				textColumn: true,
			},
			{
				id: "codContrato",
				label: "Contrato",
				attribute: "codContrato",
				textColumn: true,
				valueTransform: (v) => {
					if (!v) {
						v = 0;
					}
					return v;
				},
			},
			{
				id: "valorEmAberto",
				label: "Valor",
				attribute: "valorEmAberto",
				money: true,
			},
			{
				id: "nomePlano",
				label: "Plano",
				attribute: "nomePlano",
				textColumn: true,
			},
			{
				id: "duracaoContrato",
				label: "Duração",
				attribute: "duracaoContrato",
				textColumn: true,
				valueTransform: (v) => {
					if (!v) {
						v = 0;
					}
					return v;
				},
			},
			{
				id: "telefonescliente",
				label: "Telefone",
				attribute: "cliente.telefones",
				textColumn: true,
			}
		);
	}

	private _columnsParcelaEmAbertoColaborador() {
		this.orderBy = "nome";
		this.columns.push(
			{
				id: "nome",
				label: "Nome",
				attribute: "colaborador.pessoa.nome",
				titlecase: true,
				clickable: true,
				onClick: (item) => this.openColaborador(item.colaborador),
			},
			{
				id: "codigo",
				label: "Código",
				attribute: "colaborador.codigo",
				textColumn: true,
			},
			{
				id: "cpf",
				label: "CPF",
				attribute: "colaborador.pessoa.cpf",
				textColumn: true,
			},
			{
				id: "situacao",
				label: "Situação",
				attribute: "colaborador.situacao",
				textColumn: true,
				valueTransform: (v) => {
					if (["IN", "NA"].includes(v)) {
						return "Inativo";
					}
					return "Ativo";
				},
			}
		);
	}

	private _populateCorSituacao(data: Array<any>) {
		data.forEach((item) => {
			let value = "";
			switch (this.indicador) {
				case IndicadorPendenciaClientesEnum.PARCELAS_EM_ATRASO:
					value = this.objectValuePipe.transform(item, "cliente.situacao");
					break;
				case IndicadorPendenciaClientesEnum.CLIENTES_ANIVERSARIANTES:
					value = this.objectValuePipe.transform(item, "situacaoCliente");
					break;
			}

			if (value && value !== "") {
				item.corSituacaoCliente =
					this.biCommonService.corDs3StatusCliente(value);
			}
		});
	}

	private _configFiltersByIndicador() {
		switch (this.indicador) {
			case IndicadorPendenciaClientesEnum.PARCELAS_EM_ATRASO:
				this.filters.inicio = null;
				break;
		}
	}
}
