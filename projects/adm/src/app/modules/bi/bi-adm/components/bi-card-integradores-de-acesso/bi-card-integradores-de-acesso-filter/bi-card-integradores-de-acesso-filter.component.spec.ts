import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiCardIntegradoresDeAcessoFilterComponent } from "./bi-card-integradores-de-acesso-filter.component";

describe("BiCardIntegradoresDeAcessoFilterComponent", () => {
	let component: BiCardIntegradoresDeAcessoFilterComponent;
	let fixture: ComponentFixture<BiCardIntegradoresDeAcessoFilterComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiCardIntegradoresDeAcessoFilterComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiCardIntegradoresDeAcessoFilterComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
