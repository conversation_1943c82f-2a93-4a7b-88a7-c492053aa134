<adm-bi-side-filter-base-content
	(closeSidenav)="onClose()"
	(filter)="onFilter()"
	(clear)="onClear()">
	<form [formGroup]="form">
		<ds3-form-field>
			<ds3-field-label>Data</ds3-field-label>
			<ds3-input-date
				ds3Input
				[control]="form.controls['data']"></ds3-input-date>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Fonte receita/despesa</ds3-field-label>
			<ds3-select
				ds3Input
				formControlName="fonteReceitaDespesa"
				nameKey="label"
				valueKey="value"
				[useFullOption]="true"
				[options]="fonteReceitaDespesaOptions"></ds3-select>
		</ds3-form-field>
		<ds3-diviser></ds3-diviser>
		<ds3-form-field>
			<ds3-field-label>Bolsas</ds3-field-label>
			<ds3-chips
				(selectionChange)="onChipSelected($event, 'incluirBolsas')"
				[isActive]="form.controls['incluirBolsas'].value">
				Incluir bolsas no cálculo
			</ds3-chips>
		</ds3-form-field>
		<ds3-diviser></ds3-diviser>
		<ds3-form-field>
			<ds3-field-label>Dependentes</ds3-field-label>
			<ds3-chips
				(selectionChange)="onChipSelected($event, 'considerarDependentes')"
				[isActive]="form.controls['considerarDependentes'].value">
				Considerar dependentes
			</ds3-chips>
		</ds3-form-field>
		<ds3-diviser></ds3-diviser>
	</form>
</adm-bi-side-filter-base-content>
