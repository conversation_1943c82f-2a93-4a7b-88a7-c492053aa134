import { BiGlobalFilter } from "@adm/modules/bi/bi-shared/models/bi-global.filter";
import { BiSidenavRef } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { ChangeDetectorRef, Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "adm-bi-inadimplencia-filter",
	templateUrl: "./bi-inadimplencia-filter.component.html",
	styleUrls: ["./bi-inadimplencia-filter.component.scss"],
})
export class BiInadimplenciaFilterComponent implements OnInit {
	globalFilter: BiGlobalFilter;

	form: FormGroup = new FormGroup({
		data: new FormControl(),
		desconsiderarCanceladas: new FormControl(),
		dataMatricula: new FormControl(),
		diaPagamento: new FormControl(),
	});

	filters: {
		data: Date;
		desconsiderarCanceladas: boolean;
		dataMatricula: Date;
		diaPagamento: number;
	};
	diaPagamento: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>(
		{ label: "5", value: 5 },
		{ label: "10", value: 10 },
		{ label: "15", value: 15 },
		{ label: "20", value: 20 },
		{ label: "25", value: 25 }
	);

	constructor(
		private biSidenavRef: BiSidenavRef<BiInadimplenciaFilterComponent>,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this._populateByGlobalFilter();
		this._populateFormByFilters();
	}

	private _populateByGlobalFilter() {
		if (this.globalFilter) {
			this.form.get("data").setValue(this.globalFilter.dataGeral);
		}
	}

	private _populateFormByFilters() {
		if (this.filters) {
			this.form.patchValue({
				data: this.filters.data,
				desconsiderarCanceladas: this.filters.desconsiderarCanceladas,
				dataMatricula: this.filters.dataMatricula,
				diaPagamento: this.filters.diaPagamento,
			});
		}
	}

	onClose() {
		this.biSidenavRef.close();
	}

	onFilter() {
		if (!this.filters) {
			this.filters = this.form.value;
		} else {
			Object.assign(this.filters, this.form.value);
		}
		this.biSidenavRef.close({
			filters: {
				...this.filters,
			},
		});
	}

	onClear() {
		this.form.reset({});
		this._populateByGlobalFilter();
		this.biSidenavRef.close({ filters: this.form.value });
	}

	onChipSelected(selected: boolean, controlName: string) {
		this.form.get(controlName).setValue(selected);
	}
}
