<adm-bi-side-filter-base-content
	(closeSidenav)="onClose()"
	(filter)="onFilter()"
	(clear)="onClear()">
	<form [formGroup]="form">
		<ds3-form-field>
			<ds3-field-label>Data</ds3-field-label>
			<ds3-input-date
				ds3Input
				[control]="form.controls['data']"></ds3-input-date>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Alunos matriculados a partir do dia</ds3-field-label>
			<ds3-input-date
				ds3Input
				[control]="form.controls['dataMatricula']"></ds3-input-date>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Considerar pagamento até o dia</ds3-field-label>
			<ds3-select
				ds3Input
				formControlName="diaPagamento"
				[options]="diaPagamento"
				nameKey="label"
				valueKey="value"></ds3-select>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Parcelas</ds3-field-label>
			<ds3-chips
				(selectionChange)="onChipSelected($event, 'desconsiderarCanceladas')"
				[isActive]="form.controls['desconsiderarCanceladas'].value">
				Desconsiderar canceladas
			</ds3-chips>
		</ds3-form-field>
	</form>
</adm-bi-side-filter-base-content>
