import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiIndiceRenovacaoFilterComponentComponent } from "./bi-indice-renovacao-filter-component.component";

describe("BiIndiceRenovacaoFilterComponentComponent", () => {
	let component: BiIndiceRenovacaoFilterComponentComponent;
	let fixture: ComponentFixture<BiIndiceRenovacaoFilterComponentComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiIndiceRenovacaoFilterComponentComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiIndiceRenovacaoFilterComponentComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
