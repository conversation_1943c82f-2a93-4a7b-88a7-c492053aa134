<adm-bi-modal-content
	(close)="onClose()"
	(pageChange)="onPageChange($event)"
	(pageSizeChange)="onPageSizeChange($event)"
	(search)="quickSearch($event)"
	[colunasShare]="colunasShare"
	[filters]="filters"
	[modalTitle]="modalTitle"
	[pageSize]="size"
	[page]="page"
	[shareUrl]="shareUrl"
	[totalItems]="totalItems">
	<ds3-table>
		<table [stateManager]="tableState" ds3DataTable>
			<ng-container ds3TableColumn="pessoa">
				<th *ds3TableHeaderCell>Nome</th>
				<td *ds3TableCell="let item" class="nomeEntidade">
					<button (click)="openCliente(item)" ds3-text-button>
						{{ item?.pessoa?.nome | titlecase }}
					</button>
				</td>
			</ng-container>

			<ng-container ds3TableColumn="matricula">
				<th *ds3TableHeaderCell class="align-center">Matrícula</th>
				<td *ds3TableCell="let item" class="matricula align-center">
					{{ item?.matricula }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="contratoRenovado">
				<th *ds3TableHeaderCell class="align-center">Renovou contrato</th>
				<td *ds3TableCell="let item" class="bi-modal-situacao align-center">
					{{ item?.contratoRenovado ? "Sim" : "Não" }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="verificadoEm">
				<th *ds3TableHeaderCell class="align-center">Verificado em</th>
				<td *ds3TableCell="let item" class="bi-modal-situacao align-center">
					{{ item?.verificadoEm | date : "dd/MM/yyyy" : "UTC" }}
				</td>
			</ng-container>

			<ng-container ds3TableColumn="usuarioVerificacao">
				<th *ds3TableHeaderCell class="align-center">Verificado por</th>
				<td *ds3TableCell="let item" class="bi-modal-situacao align-center">
					{{ item?.usuarioVerificacao | titlecase }}
				</td>
			</ng-container>

			<tr *ds3TableRow></tr>

			<button
				(click)="triggerSortToggle()"
				*ds3TableSortControl="
					let direction = direction;
					let triggerSortToggle = triggerSortToggle
				"
				class="sort-control"
				ds3-icon-button>
				<i
					[ngClass]="{
						'pct-drop-down': direction === null,
						'pct-caret-up': direction === 'ASC',
						'pct-caret-down': direction === 'DESC'
					}"
					class="pct"></i>
			</button>

			<tr *ds3TableEmptyRow class="ds3-table-empty">
				<td>
					<h2>Nenhum item encontrado</h2>
					<p>
						Nenhum item encontrado no período, tente realizar uma nova busca.
					</p>
				</td>
			</tr>

			<tbody *ds3TableLoading>
				<tr>
					<td>
						<div class="bi-modal-table-loader" role="status">
							<img
								alt="Loading pacto"
								src="pacto-ui/images/gif/loading-pacto.gif" />
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</ds3-table>
</adm-bi-modal-content>
