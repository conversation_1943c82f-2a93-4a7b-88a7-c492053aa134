import { Component, Inject, On<PERSON><PERSON>roy, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import {
	AdmCoreApiClienteService,
	ApiResponseList,
	ClienteParaVerificarResponseModel,
	FiltroBIModel,
} from "adm-core-api";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { PactoDataGridColumnConfig, PactoDataTableStateManager } from "ui-kit";
import { AdmRestService } from "../../../../../../adm-rest.service";
import { BiCommonService } from "../../../../bi-shared/services/bi-common/bi-common.service";

@Component({
	selector: "adm-bi-clientes-para-verificar-modal",
	templateUrl: "./bi-clientes-para-verificar-modal.component.html",
	styleUrls: [
		"./bi-clientes-para-verificar-modal.component.scss",
		"../../../../bi-shared/bi-shared.scss",
	],
})
export class BiClientesParaVerificarModalComponent
	implements OnInit, OnDestroy
{
	tableState: PactoDataTableStateManager<any> =
		new PactoDataTableStateManager();
	page: number = 1;
	size: number = 50;
	totalItems: number = 0;
	colunasShare: Array<PactoDataGridColumnConfig> = [
		{
			mostrarTitulo: true,
			campo: "matricula",
			visible: true,
			titulo: "Matrícula",
			ordenavel: true,
			nome: "matricula",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "pessoa.nome",
			visible: true,
			titulo: "Nome",
			ordenavel: true,
			nome: "pessoa",
			inputType: "text",
		},
		{
			mostrarTitulo: true,
			campo: "contratoRenovado",
			visible: true,
			titulo: "Renovou contrato",
			ordenavel: true,
			date: true,
			nome: "contratoRenovado",
			inputType: "date",
		},
		{
			mostrarTitulo: true,
			campo: "verificadoEm",
			visible: true,
			titulo: "Verificado em",
			ordenavel: true,
			date: true,
			nome: "verificadoEm",
			inputType: "date",
		},
		{
			mostrarTitulo: true,
			campo: "usuarioVerificacao",
			visible: true,
			titulo: "Verificado por",
			ordenavel: true,
			nome: "usuarioVerificacao",
			inputType: "text",
		},
	];
	modalTitle: string;

	filters: FiltroBIModel = {} as FiltroBIModel;
	shareUrl: string = this.admRest.buildFullUrlAdmCore(
		"clientes/clientes-para-verificar"
	);
	verificado: boolean;

	private _destroy$: Subject<void> = new Subject();
	private orderBy: string = "pessoa";
	private orderDirection: "ASC" | "DESC" = "ASC";

	constructor(
		private dialog: MatDialogRef<BiClientesParaVerificarModalComponent>,
		private admCoreApiClienteService: AdmCoreApiClienteService,
		private admRest: AdmRestService,
		private biCommonService: BiCommonService,
		private toastrService: ToastrService,
		@Inject(MAT_DIALOG_DATA) private modalData
	) {}

	ngOnInit() {
		if (this.modalData) {
			if (this.modalData.filters) {
				this.filters = this.modalData.filters;
			}
			if (this.modalData.modalTitle) {
				this.modalTitle = `Verificação de clientes - ${this.modalData.modalTitle}`;
			}
			this.verificado = this.modalData.verificado;
		}
		this._loadData();
		this._tableStateUpdated();
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	quickSearch(value) {
		this.filters.quickSearchValue = value;
		this.page = 0;
		this._loadData();
	}

	onClose() {
		this.dialog.close();
	}

	onPageSizeChange(size: number) {
		this.size = size;
		this._loadData();
	}

	onPageChange(page: number) {
		this.page = page;
		this._loadData();
	}

	openCliente(item: ClienteParaVerificarResponseModel) {
		this.biCommonService.openCliente(item.matricula);
	}

	private _loadData() {
		this.tableState.patchState({ loading: true });
		this.admCoreApiClienteService
			.clientesParaVerificar({
				filtroBI: this.filters,
				verificado: this.verificado,
				page: this.page,
				size: this.size,
				orderBy: this.orderBy,
				orderDirection: this.orderDirection,
			})
			.pipe(takeUntil(this._destroy$))
			.subscribe({
				next: (
					response: ApiResponseList<ClienteParaVerificarResponseModel>
				) => {
					this.totalItems = response.totalElements;
					this.tableState.patchState({
						data: response.content,
						loading: false,
						pageSize: this.size,
						currentPage: this.page,
						totalItems: this.totalItems,
						orderBy: this.orderBy,
						orderDirection: this.orderDirection,
					});
				},
				error: (error) => {
					if (error && error.error.meta) {
						if (error.error.meta) {
							if (error.error.meta.message) {
								this.toastrService.error(error.error.meta.message);
							} else {
								this.toastrService.error("Ocorreu um erro desconhecido!");
							}
						}
					}
					this.tableState.patchState({ loading: false });
				},
			});
	}

	private _tableStateUpdated() {
		this.tableState.update$
			.pipe(takeUntil(this._destroy$))
			.subscribe((value) => {
				this.orderBy = value.orderBy;
				this.orderDirection = value.orderDirection;

				this._loadData();
			});
	}
}
