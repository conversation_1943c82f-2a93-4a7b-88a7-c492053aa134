import { BiSharedModule } from "@adm/modules/bi/bi-shared/bi-shared.module";
import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import {
	BiCardMetasFinanceirasComponent,
	ValorMetaPorIndexPipe,
} from "./bi-card-metas-financeiras.component";
import { BiMetasFinanceirasFilterComponent } from "./bi-metas-financeiras-filter/bi-metas-financeiras-filter.component";

@NgModule({
	declarations: [
		BiCardMetasFinanceirasComponent,
		BiMetasFinanceirasFilterComponent,
		ValorMetaPorIndexPipe,
	],
	imports: [CommonModule, ReactiveFormsModule, BiSharedModule],
	exports: [BiCardMetasFinanceirasComponent],
	entryComponents: [BiMetasFinanceirasFilterComponent],
})
export class BiCardMetasFinanceirasModule {}
