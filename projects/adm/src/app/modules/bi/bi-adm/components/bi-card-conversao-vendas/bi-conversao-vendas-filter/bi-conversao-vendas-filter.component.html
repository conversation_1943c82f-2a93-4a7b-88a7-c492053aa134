<adm-bi-side-filter-base-content
	(closeSidenav)="onClose()"
	(filter)="onFilter()"
	(clear)="onClear()">
	<form [formGroup]="form">
		<ds3-form-field>
			<ds3-field-label>Data da conversão</ds3-field-label>
			<ds3-input-date
				ds3Input
				[control]="form.controls['data']"></ds3-input-date>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>BV's</ds3-field-label>
			<ds3-chips-list>
				<ds3-chips
					(selectionChange)="selectTipoBv($event, 1, 'bvMatricula')"
					[isActive]="form.controls['bvMatricula'].value">
					Matrículas
				</ds3-chips>
				<ds3-chips
					(selectionChange)="selectTipoBv($event, 2, 'bvRematricula')"
					[isActive]="form.controls['bvRematricula'].value">
					Rematrículas
				</ds3-chips>
				<ds3-chips
					(selectionChange)="selectTipoBv($event, 3, 'bvRetorno')"
					[isActive]="form.controls['bvRetorno'].value">
					Retorno
				</ds3-chips>
			</ds3-chips-list>
		</ds3-form-field>
		<ds3-diviser></ds3-diviser>
		<ds3-form-field>
			<ds3-field-label>Tipo de contrato</ds3-field-label>
			<ds3-chips-list>
				<ds3-chips
					(selectionChange)="
						selectTipoContrato($event, 1, 'tipoContratoEspontaneo')
					"
					[isActive]="form.controls['tipoContratoEspontaneo'].value">
					Espontâneo
				</ds3-chips>
				<ds3-chips
					(selectionChange)="
						selectTipoContrato($event, 2, 'tipoContratoAgendado')
					"
					[isActive]="form.controls['tipoContratoAgendado'].value">
					Agendado
				</ds3-chips>
			</ds3-chips-list>
		</ds3-form-field>
		<ds3-diviser></ds3-diviser>
		<ds3-form-field>
			<ds3-field-label>Origem</ds3-field-label>
			<ds3-chips-list>
				<ds3-chips
					(selectionChange)="
						selectOrigemSistema($event, '1,17', 'origemSistema')
					"
					[isActive]="form.controls['origemSistema'].value">
					Origem sistema
				</ds3-chips>
				<ds3-chips
					(selectionChange)="selectOrigemSistema($event, '9', 'origemSite')"
					[isActive]="form.controls['origemSite'].value">
					Origem do site
				</ds3-chips>
			</ds3-chips-list>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Plano Bolsa</ds3-field-label>
			<ds3-chips
				(selectionChange)="onChipSelected($event, 'considerarPlanoBolsa')"
				[isActive]="form.controls['considerarPlanoBolsa'].value">
				Considerar plano bolsa
			</ds3-chips>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>WellHub</ds3-field-label>
			<ds3-chips
				(selectionChange)="onChipSelected($event, 'desconsiderarGympass')"
				[isActive]="form.controls['desconsiderarGympass'].value">
				Desconsiderar alunos wellHub
			</ds3-chips>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Evento</ds3-field-label>
			<ds3-select-multi
				ds3Input
				formControlName="eventos"
				(search)="loadEventos($event)"
				nameKey="descricao"
				valueKey="codigo"
				[useValueAsObject]="true"
				[options]="eventos"></ds3-select-multi>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Grupo de colaboradores</ds3-field-label>
			<ds3-select-multi
				ds3Input
				formControlName="gruposColaboradores"
				nameKey="descricao"
				valueKey="codigo"
				[useValueAsObject]="true"
				[options]="gruposColaboradores"></ds3-select-multi>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Colaboradores</ds3-field-label>
			<ds3-select-multi
				ds3Input
				formControlName="colaboradores"
				nameKey="label"
				valueKey="value"
				[useValueAsObject]="true"
				[options]="optionsColaboradores"></ds3-select-multi>
		</ds3-form-field>
	</form>
</adm-bi-side-filter-base-content>
