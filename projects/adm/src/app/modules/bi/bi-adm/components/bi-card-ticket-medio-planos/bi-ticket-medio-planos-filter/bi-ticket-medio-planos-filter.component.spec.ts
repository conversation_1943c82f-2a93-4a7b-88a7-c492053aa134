import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiTicketMedioPlanosFilterComponent } from "./bi-ticket-medio-planos-filter.component";

describe("BiTicketMedioPlanosFilterComponent", () => {
	let component: BiTicketMedioPlanosFilterComponent;
	let fixture: ComponentFixture<BiTicketMedioPlanosFilterComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiTicketMedioPlanosFilterComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiTicketMedioPlanosFilterComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
