import { BiGlobalFilter } from "@adm/modules/bi/bi-shared/models/bi-global.filter";
import { BiSidenavRef } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "adm-bi-ticket-medio-planos-filter",
	templateUrl: "./bi-ticket-medio-planos-filter.component.html",
	styleUrls: ["./bi-ticket-medio-planos-filter.component.scss"],
})
export class BiTicketMedioPlanosFilterComponent implements OnInit {
	globalFilter: BiGlobalFilter;

	form: FormGroup = new FormGroup({
		data: new FormControl(),
		fonteReceitaDespesa: new FormControl(),
		incluirBolsas: new FormControl(),
		considerarDependentes: new FormControl(),
	});

	fonteReceitaDespesaOptions = [
		{ label: "Demonstrativo financeiro", value: 1 },
		{ label: "D.R.E financeiro", value: 2 },
	];

	filters: {
		data: Date;
		fonteReceitaDespesa: { label: string; value: number } | number;
		incluirBolsas: boolean;
		considerarDependentes: boolean;
	};

	constructor(
		private biSidenavRef: BiSidenavRef<BiTicketMedioPlanosFilterComponent>
	) {}

	ngOnInit() {
		this._populateByGlobalFilter();
		this._populateFormByFilters();
	}

	private _populateByGlobalFilter() {
		if (this.globalFilter) {
			this.form.get("data").setValue(this.globalFilter.dataGeral);
		}
	}

	private _populateFormByFilters() {
		if (this.filters) {
			const patchValue: any = {};

			patchValue.data = this.filters.data;

			if (this.filters.fonteReceitaDespesa) {
				patchValue.fontReceitaDespesa =
					typeof this.filters.fonteReceitaDespesa === "number"
						? this.filters.fonteReceitaDespesa
						: this.filters.fonteReceitaDespesa.value;
			}

			patchValue.incluirBolsas = this.filters.incluirBolsas;
			patchValue.considerarDependentes = this.filters.considerarDependentes;

			this.form.patchValue(patchValue);
		}
	}

	onClose() {
		this.biSidenavRef.close();
	}

	onFilter() {
		if (!this.filters) {
			this.filters = this.form.value;
		} else {
			Object.assign(this.filters, this.form.value);
		}

		if (typeof this.filters.fonteReceitaDespesa === "number") {
			this.filters.fonteReceitaDespesa = this.fonteReceitaDespesaOptions.find(
				(v) => v.value === this.filters.fonteReceitaDespesa
			);
		}

		this.biSidenavRef.close({
			filters: this.filters,
		});
	}

	onClear() {
		this.form.reset({});
		this._populateByGlobalFilter();
		this.biSidenavRef.close({ filters: this.form.value });
	}

	onChipSelected(selected: boolean, controlName: string) {
		this.form.get(controlName).setValue(selected);
	}
}
