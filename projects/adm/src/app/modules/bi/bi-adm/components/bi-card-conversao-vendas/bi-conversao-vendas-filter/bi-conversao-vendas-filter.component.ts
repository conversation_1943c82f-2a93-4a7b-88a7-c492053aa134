import { BiGlobalFilter } from "@adm/modules/bi/bi-shared/models/bi-global.filter";
import { BiSidenavRef } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import {
	AdmCoreApiEventoService,
	AdmCoreApiGrupoColaboradorService,
	ApiResponseList,
	Colaborador,
	Evento,
	GrupoColaborador,
	GrupoColaboradorParticipante,
} from "adm-core-api";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";

@Component({
	selector: "adm-bi-conversao-vendas-filter",
	templateUrl: "./bi-conversao-vendas-filter.component.html",
	styleUrls: ["./bi-conversao-vendas-filter.component.scss"],
})
export class BiConversaoVendasFilterComponent implements OnInit, OnDestroy {
	globalFilter: BiGlobalFilter;

	form: FormGroup = new FormGroup({
		data: new FormControl(),
		bvMatricula: new FormControl(), // 1
		bvRematricula: new FormControl(), // 2
		bvRetorno: new FormControl(), // 3
		origemSistema: new FormControl(), // 1,17
		origemSite: new FormControl(), // 7
		tipoContratoEspontaneo: new FormControl(), // 1
		tipoContratoAgendado: new FormControl(), // 2
		gruposColaboradores: new FormControl(),
		colaboradores: new FormControl(),
		eventos: new FormControl(),
		considerarPlanoBolsa: new FormControl(),
		desconsiderarGympass: new FormControl(),
	});

	filters: {
		data: Date;
		gruposColaboradores: Array<number>;
		colaboradores: Array<number | Colaborador>;
		colaboradoresObj: Array<{ label: string; value: any }>;
		gruposColaboradoresObj: Array<GrupoColaborador>;
		listaOrigemSistema: string;
		origemSistema: Array<string>;
		listaEvento: string;
		eventos: Array<Evento>;
		tipoBV: Array<number>;
		tipoContrato: Array<number>;
		considerarPlanoBolsa?: boolean;
		desconsiderarGympass?: boolean;
	} = {
		data: undefined,
		gruposColaboradores: new Array<number>(),
		colaboradores: new Array<number | Colaborador>(),
		colaboradoresObj: new Array<{ label: string; value: any }>(),
		gruposColaboradoresObj: new Array<GrupoColaborador>(),
		listaOrigemSistema: "",
		origemSistema: new Array<string>(),
		listaEvento: "",
		eventos: new Array<Evento>(),
		tipoBV: new Array<number>(),
		tipoContrato: new Array<number>(),
	};

	gruposColaboradores: Array<GrupoColaborador> = new Array<GrupoColaborador>();
	optionsColaboradores: Array<{ value: any; label: string }> = new Array<{
		value: any;
		label: string;
	}>();
	eventos: Array<Evento> = new Array<Evento>();

	private _destroyed$: Subject<void> = new Subject<void>();

	private _arrayOrigemSistema: Array<string> = new Array<string>();
	private _listaOrigemSistema: string;
	private _tipoContrato: Array<number> = new Array<number>();
	private _tipoBV: Array<number> = new Array<number>();

	constructor(
		private admCoreApiGrupoColaboradorService: AdmCoreApiGrupoColaboradorService,
		private admCoreApiEventoService: AdmCoreApiEventoService,
		private biSidenavRef: BiSidenavRef<BiConversaoVendasFilterComponent>,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this._populateByGlobalFilter();
		this._loadgruposColaboradores();
		this.loadEventos();
		this._populateFormByFilters();
		this._listenValueChanges();
	}

	ngOnDestroy() {
		this._destroyed$.next();
	}

	private _populateByGlobalFilter() {
		if (this.globalFilter) {
			this.form.get("data").setValue(this.globalFilter.dataGeral);
		}
		this.form.get("data").valueChanges.subscribe((v) => {
			this.loadEventos();
		});
	}

	private _loadgruposColaboradores() {
		this.admCoreApiGrupoColaboradorService
			.findByEmpresaLogada()
			.subscribe((response: ApiResponseList<GrupoColaborador>) => {
				this.gruposColaboradores = response.content;
				this.gruposColaboradores = this.gruposColaboradores.map((v) => {
					if (v.descricao === "SEM GRUPO") {
						v.codigo = -1;
					}
					return v;
				});
				const colaboradores = [];
				const grupoColaboradores: Array<GrupoColaboradorParticipante> = [];
				const colaboradoresDistinct: Map<number, Colaborador> = new Map<
					number,
					Colaborador
				>();
				this.gruposColaboradores.forEach((gc) =>
					grupoColaboradores.push(...gc.grupoColaboradorParticipantes)
				);
				grupoColaboradores.forEach((v) => {
					if (!colaboradoresDistinct.has(v.codigo)) {
						colaboradoresDistinct.set(
							v.colaboradorParticipante.codigo,
							v.colaboradorParticipante
						);
					}
				});
				colaboradoresDistinct.forEach((v) => {
					colaboradores.push({ value: v.codigo, label: v.pessoa.nome });
				});
				colaboradores.forEach((c) => this.optionsColaboradores.push(c));
				this.optionsColaboradores = this.optionsColaboradores.sort((c1, c2) => {
					if (c1.label > c2.label) {
						return 1;
					} else if (c1.label < c2.label) {
						return -1;
					} else {
						return 0;
					}
				});

				if (this.filters && this.filters.colaboradores) {
					const cols = this.filters.colaboradores.map((v) =>
						typeof v === "number" ? { value: v } : v
					);
					this.form.get("colaboradores").setValue(cols);
				}
				this.cd.detectChanges();
			});
	}

	loadEventos(searchEvent?: { term: string }) {
		let term = "";
		if (searchEvent) {
			term = searchEvent.term;
		}
		this.admCoreApiEventoService
			.obterEventosByDate(this.form.get("data").value.getTime(), term)
			.subscribe((response: ApiResponseList<Evento>) => {
				this.eventos = response.content;
			});
	}

	private _populateFormByFilters() {
		if (this.filters) {
			const patchValue: any = {};

			patchValue.data = this.filters.data;

			if (this.filters.tipoBV) {
				patchValue.bvMatricula = this.filters.tipoBV.includes(1); // 1
				patchValue.bvRematricula = this.filters.tipoBV.includes(2); // 2
				patchValue.bvRetorno = this.filters.tipoBV.includes(3); // 3
				this._tipoBV = [...this.filters.tipoBV];
			}

			if (this.filters.listaOrigemSistema) {
				patchValue.origemSistema =
					this.filters.listaOrigemSistema.includes("1,17"); // 1,17
				patchValue.origemSite = this.filters.listaOrigemSistema.includes("9"); // 9
				this._listaOrigemSistema = this.filters.listaOrigemSistema;
				this._arrayOrigemSistema = [...this.filters.origemSistema];
			}

			if (this.filters.tipoContrato) {
				patchValue.tipoContratoEspontaneo =
					this.filters.tipoContrato.includes(1); // 1
				patchValue.tipoContratoAgendado = this.filters.tipoContrato.includes(2); // 2
				this._tipoContrato = [...this.filters.tipoContrato];
			}

			if (this.filters.gruposColaboradores) {
				patchValue.gruposColaboradores = this.filters.gruposColaboradores;
			}

			if (this.filters.eventos) {
				patchValue.eventos = this.filters.eventos;
			}

			patchValue.considerarPlanoBolsa = this.filters.considerarPlanoBolsa;
			patchValue.desconsiderarGympass = this.filters.desconsiderarGympass;

			this.form.patchValue(patchValue);
		}
	}

	onClose() {
		this.biSidenavRef.close();
	}

	onFilter() {
		this.biSidenavRef.close({
			filters: {
				data: this.form.get("data").value,
				colaboradores: this.form.get("colaboradores").value,
				gruposColaboradores: this.form.get("gruposColaboradores").value,
				tipoBV: this._tipoBV,
				origemSistema: this._arrayOrigemSistema,
				listaOrigemSistema: this._listaOrigemSistema,
				eventos: this.form.get("eventos").value,
				tipoContrato: this._tipoContrato,
				considerarPlanoBolsa: this.form.get("considerarPlanoBolsa").value,
				desconsiderarGympass: this.form.get("desconsiderarGympass").value,
			},
		});
	}

	onClear() {
		this.form.reset({});
		this._populateByGlobalFilter();
		this.biSidenavRef.close({ filters: this.form.value });
	}

	selectTipoBv(selected: boolean, tipoBv: number, control: string) {
		const value = selected;
		if (!this._tipoBV) {
			this._tipoBV = new Array<number>();
		}
		if (selected) {
			this._tipoBV.push(tipoBv);
		} else {
			const i = this._tipoBV.findIndex((v) => v === tipoBv);
			if (i !== -1) {
				this._tipoBV.splice(i, 1);
			}
		}

		this.form.get(control).setValue(value);
	}

	selectTipoContrato(selected: boolean, tipoContrato: number, control: string) {
		const value = selected;
		if (!this._tipoContrato) {
			this._tipoContrato = new Array<number>();
		}
		if (selected) {
			this._tipoContrato.push(tipoContrato);
		} else {
			const i = this._tipoContrato.findIndex((v) => v === tipoContrato);
			if (i !== -1) {
				this._tipoContrato.splice(i, 1);
			}
		}

		this.form.get(control).setValue(value);
	}

	selectOrigemSistema(
		selected: boolean,
		codigoOrigemSistema: string,
		control: string
	) {
		const value = selected;
		if (!this._arrayOrigemSistema) {
			this._arrayOrigemSistema = new Array<string>();
		}
		if (selected) {
			this._arrayOrigemSistema.push(codigoOrigemSistema);
		} else {
			const i = this._arrayOrigemSistema.findIndex(
				(v) => v === codigoOrigemSistema
			);
			if (i !== -1) {
				this._arrayOrigemSistema.splice(i, 1);
			}
		}

		if (this._arrayOrigemSistema.length > 0) {
			this._listaOrigemSistema = this._arrayOrigemSistema.join(",");
		} else {
			this._listaOrigemSistema = undefined;
		}

		this.form.get(control).setValue(value);
	}

	private _listenValueChanges() {
		this._listenGrupoColaboradorChanges();
	}

	private _listenGrupoColaboradorChanges() {
		this.form
			.get("gruposColaboradores")
			.valueChanges.pipe(takeUntil(this._destroyed$))
			.subscribe((value) => {
				const grupoColaboradorSelecionado = this.gruposColaboradores.filter(
					(gc) => value.map((v) => v.codigo).includes(gc.codigo)
				);

				if (grupoColaboradorSelecionado) {
					const colaboradores = new Array<{ value: number }>();
					const colaboradoresDistinct: Map<number, Colaborador> = new Map<
						number,
						Colaborador
					>();
					const grupoColaboradores: Array<GrupoColaboradorParticipante> = [];
					grupoColaboradorSelecionado.forEach((gcs) => {
						grupoColaboradores.push(...gcs.grupoColaboradorParticipantes);
					});
					grupoColaboradores.forEach((v) => {
						if (!colaboradoresDistinct.has(v.codigo)) {
							colaboradoresDistinct.set(
								v.colaboradorParticipante.codigo,
								v.colaboradorParticipante
							);
						}
					});
					colaboradoresDistinct.forEach((v) => {
						colaboradores.push({ value: v.codigo });
					});

					this.form.get("colaboradores").setValue(colaboradores);
					this.cd.detectChanges();
				}
			});
	}

	onChipSelected(selected: boolean, controlName: string) {
		this.form.get(controlName).setValue(selected);
	}
}
