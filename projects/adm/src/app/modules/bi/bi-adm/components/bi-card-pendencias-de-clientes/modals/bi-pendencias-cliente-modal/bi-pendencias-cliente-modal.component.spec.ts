import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiPendenciasClienteModalComponent } from "./bi-pendencias-cliente-modal.component";

describe("BiPendenciasClienteModalComponent", () => {
	let component: BiPendenciasClienteModalComponent;
	let fixture: ComponentFixture<BiPendenciasClienteModalComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiPendenciasClienteModalComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiPendenciasClienteModalComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
