<div class="bi-card controle-de-operacoes-de-excecoes">
	<div class="bi-card-header">
		<div class="bi-card-header-label">
			<span class="bi-card-header-label-title">
				Metas financeiras de vendas
			</span>
			<a
				ds3-icon-button
				class="bi-card-header-label-button"
				size="sm"
				(click)="abrirAjuda()">
				<i class="pct pct-help-circle"></i>
			</a>
		</div>
		<div class="bi-card-header-actions">
			<button ds3-outlined-button (click)="cadastrarMeta()" size="sm">
				<i class="pct pct-plus" style="margin-right: 8px"></i>
				<span>Cadastrar Meta</span>
			</button>
			<button
				ds3-outlined-button
				(click)="loadData(true)"
				class="pct pct-refresh-cw"
				size="sm"></button>
			<button
				ds3-outlined-button
				(click)="openSideNav()"
				class="pct pct-filter"
				size="sm"></button>
		</div>
	</div>
	<ds3-diviser></ds3-diviser>
	<div class="bi-card-content">
		<div class="bi-card-content-filters">
			<span class="bi-card-content-filters-title">Filtros aplicados:</span>
			<ds3-status
				color="outlined"
				*ngIf="filtros?.empresaNome || filtros?.empresas">
				<ng-container
					*ngIf="
						filtros?.empresaNome && !filtros?.empresas;
						else empresasOneRef
					">
					{{ filtros.empresaNome }}
				</ng-container>
				<ng-template #empresasOneRef>
					<ng-container
						*ngIf="
							filtros?.empresas && filtros?.empresas.length === 1;
							else empresasMultiRef
						">
						{{ filtros?.empresas[0]?.nome }}
					</ng-container>
				</ng-template>
				<ng-template #empresasMultiRef>
					<ng-container *ngIf="filtros?.empresas.length > 1">
						<span
							[ds3Tooltip]="tooltipEmpresasRef"
							tooltipIndicator="top-center"
							tooltipPosition="bottom">
							{{ filtros?.empresas?.length }} empresas selecionadas
						</span>
					</ng-container>
				</ng-template>
			</ds3-status>
			<ng-template #tooltipEmpresasRef>
				<div *ngFor="let empresa of filtros?.empresas; trackBy: trackByIndex">
					{{ empresa.nome }}
				</div>
			</ng-template>
			<ds3-status color="outlined" *ngIf="filtros?.data">
				{{ filtros.dataInicio | date : "shortDate" }} -
				{{ filtros.data | date : "shortDate" }}
			</ds3-status>
			<ds3-status color="outlined" *ngIf="filtros?.meta">
				{{ filtros?.meta?.descricao }}
			</ds3-status>
			<ds3-status
				color="outlined"
				*ngIf="
					filtros?.gruposColaboradores &&
					filtros?.gruposColaboradores?.length !== 0
				">
				<span
					[ds3Tooltip]="tooltipGruposColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.gruposColaboradores?.length }}
					{{
						filtros?.gruposColaboradores?.length > 1
							? "grupos de colaboradores selecionados"
							: "grupo de colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipGruposColaboradoresRef>
				<div
					*ngFor="
						let grupoColaborador of filtros?.gruposColaboradoresObj;
						trackBy: trackByIndex
					">
					{{ grupoColaborador.descricao }}
				</div>
			</ng-template>
			<ds3-status
				color="outlined"
				*ngIf="filtros?.colaboradores && filtros?.colaboradores?.length !== 0">
				<span
					[ds3Tooltip]="tooltipColaboradoresRef"
					tooltipIndicator="top-center"
					tooltipPosition="bottom">
					{{ filtros?.colaboradores?.length }}
					{{
						filtros?.colaboradores?.length > 1
							? "colaboradores selecionados"
							: "colaborador selecionado"
					}}
				</span>
			</ds3-status>
			<ng-template #tooltipColaboradoresRef>
				<div
					*ngFor="
						let colaborador of filtros?.colaboradoresObj;
						trackBy: trackByIndex
					">
					{{ colaborador.label }}
				</div>
			</ng-template>
		</div>
		<div
			class="bi-card-content-section"
			*ngIf="tabData && !loading && !semMetaMes">
			<div
				class="bi-card-content-section-content"
				*ngFor="
					let section of tabData?.sections;
					index as i;
					trackBy: trackByIndex
				">
				<ng-container *ngIf="section.infoItens">
					<ds3-bi-info [infoData]="section?.infoItens"></ds3-bi-info>
				</ng-container>
			</div>
			<div class="bi-card-content-chart" *ngIf="barData">
				<ds3-bar-line
					[barData]="barData"
					[textClickable]="false"></ds3-bar-line>
			</div>
			<div class="bi-card-content-actions">
				<div class="bi-card-content-actions-start">
					<div class="bi-meta-fin-meta-batida" *ngIf="infoMetaBatida">
						<i class="pct pct-rocket"></i>
						<span class="typography-body-2">{{ infoMetaBatida }}</span>
					</div>
				</div>
				<button
					ds3-text-button
					class="bi-card-content-actions-end"
					(click)="verDetalhesMeta()">
					Ver detalhes da meta
				</button>
			</div>
		</div>
		<div *ngIf="semMetaMes && !loading" class="div-empty">
			<div class="d-flex flex-column align-items-center">
				<img
					class="icon-empty"
					src="pacto-ui/images/empty-state-notas-fiscais.svg" />
				<div class="titulo-empty mt-2 body-text-empty mt-3 mb-3">
					Nenhuma meta referente ao mês!
				</div>
				<div class="text-empty">
					Você pode realizar o cadastro de uma nova meta ou consultar metas
					anteriores
				</div>
			</div>
		</div>
		<div class="bi-card-content-loading" *ngIf="loading">
			<img
				src="pacto-ui/images/gif/loading-pacto.gif"
				alt="Loading pacto"
				width="80" />
		</div>
	</div>
</div>

<ng-template #metaUmTooltip>
	<div class="bi-card-meta-fin-tooltip">
		<div class="bi-card-meta-fin-tooltip-title">Meta 01</div>

		<div class="bi-card-meta-fin-tooltip-value">
			<!--			Valor experado: {{ (metaFinRespModel?.metas[0]?.valores | valorMetaPorIndex : 0 : metaFinRespModel?.metas : metaFinRespModel.faturamentoRecebido).valorEsperado }}-->
		</div>

		<div class="bi-card-meta-fin-tooltip-value">
			<!--			Valor realizado: {{ (metaFinRespModel?.metas[0]?.valores | valorMetaPorIndex : 0 : metaFinRespModel?.metas : metaFinRespModel.faturamentoRecebido).valorRealizado }}-->
		</div>
	</div>
</ng-template>

<ng-template #metaDoisTooltip>
	<div class="bi-card-meta-fin-tooltip">
		<div class="bi-card-meta-fin-tooltip-title">Meta 02</div>

		<div class="bi-card-meta-fin-tooltip-value">
			<!--			Valor experado: {{ metaFinRespModel?.metas[1]?.valor }}-->
		</div>

		<div class="bi-card-meta-fin-tooltip-value">
			<!--			Valor realizado: {{ metaFinRespModel?.metas[1]?.faturamentoRecebido }}-->
		</div>
	</div>
</ng-template>

<ng-template #metaTresTooltip>
	<div class="bi-card-meta-fin-tooltip">
		<div class="bi-card-meta-fin-tooltip-title">Meta 03</div>

		<div class="bi-card-meta-fin-tooltip-value">
			<!--			Valor experado: {{ metaFinRespModel?.metas[2]?.valor }}-->
		</div>

		<div class="bi-card-meta-fin-tooltip-value">
			<!--			Valor realizado: {{ metaFinRespModel?.metas[2]?.faturamentoRecebido }}-->
		</div>
	</div>
</ng-template>

<ng-template #metaQuatroTooltip>
	<div class="bi-card-meta-fin-tooltip">
		<div class="bi-card-meta-fin-tooltip-title">Meta 04</div>

		<div class="bi-card-meta-fin-tooltip-value">
			<!--			Valor experado: {{ metaFinRespModel?.metas[3]?.valor }}-->
		</div>

		<div class="bi-card-meta-fin-tooltip-value">
			<!--			Valor realizado: {{ metaFinRespModel?.metas[3]?.faturamentoRecebido }}-->
		</div>
	</div>
</ng-template>

<ng-template #metaCincoTooltip>
	<div class="bi-card-meta-fin-tooltip">
		<div class="bi-card-meta-fin-tooltip-title">Meta 05</div>

		<div class="bi-card-meta-fin-tooltip-value">
			<!--			Valor experado: {{ metaFinRespModel?.metas[4]?.valor }}-->
		</div>

		<div class="bi-card-meta-fin-tooltip-value">
			<!--			Valor realizado: {{ metaFinRespModel?.metas[4]?.faturamentoRecebido }}-->
		</div>
	</div>
</ng-template>

<pacto-traducoes-xingling #traducoes></pacto-traducoes-xingling>
