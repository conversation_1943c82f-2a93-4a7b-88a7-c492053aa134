<adm-bi-side-filter-base-content
	(closeSidenav)="onClose()"
	(filter)="onFilter()"
	(clear)="onClear()">
	<form [formGroup]="form">
		<ds3-form-field>
			<ds3-field-label>Data</ds3-field-label>
			<ds3-input-date
				ds3Input
				[control]="form.controls['data']"></ds3-input-date>
		</ds3-form-field>
		<ds3-form-field>
			<ds3-field-label>Check-in</ds3-field-label>
			<ds3-chips
				(selectionChange)="selectConsiderarAcessoCheckin($event)"
				[isActive]="form.controls['acessoCheckinGympass'].value">
				Considerar acesso pelo check-in
			</ds3-chips>
		</ds3-form-field>
		<ds3-diviser></ds3-diviser>
		<ds3-form-field>
			<ds3-field-label>Integradores de acesso</ds3-field-label>
			<ds3-chips-list>
				<ds3-chips
					(selectionChange)="selectIntegradorWellhub($event)"
					[isActive]="form.controls['tipoIntegradorAcessoWellhub'].value">
					Wellhub
				</ds3-chips>
				<ds3-chips
					(selectionChange)="selectIntegradorTotalpass($event)"
					[isActive]="form.controls['tipoIntegradorAcessoTotalpass'].value">
					TotalPass
				</ds3-chips>
			</ds3-chips-list>
		</ds3-form-field>
		<ds3-diviser></ds3-diviser>
	</form>
</adm-bi-side-filter-base-content>
