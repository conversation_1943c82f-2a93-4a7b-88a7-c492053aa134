import { TabData } from "@adm/modules/bi/bi-adm/bi-adm.model";
import { BiMetasFinanceirasFilterComponent } from "@adm/modules/bi/bi-adm/components/bi-card-metas-financeiras/bi-metas-financeiras-filter/bi-metas-financeiras-filter.component";
import { BiCardWithModalsBase } from "@adm/modules/bi/bi-shared/base/bi-card/bi-card-with-modals-base";
import { ConhecimentoEnum } from "@adm/modules/bi/bi-shared/models/bi-mdl-ajuda.model";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BiMdlAjudaService } from "@adm/modules/bi/bi-shared/services/bi-mdl-ajuda.service";
import { BiSidenavService } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { CurrencyPipe, DatePipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	OnDestroy,
	OnInit,
	Pipe,
	PipeTransform,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { Router } from "@angular/router";
import {
	BiMsApiMetaFinanceiraService,
	FaturamentoPeriodoModel,
	FiltroMetaFinanceira,
	MetaFinanceiraEmpresaModel,
	MetaFinanceiraResponseModel,
} from "bi-ms-api";
import { ToastrService } from "ngx-toastr";
import { takeUntil } from "rxjs/operators";
import { BarCorFundo, BarCorLinha, Ds3BarData } from "ui-kit";

@Pipe({
	name: "valorMetaPorIndex",
})
export class ValorMetaPorIndexPipe implements PipeTransform {
	transform(
		indexValorMeta: any,
		metas: Array<MetaFinanceiraEmpresaModel>,
		faturamento: number
	): any {
		if (!metas) {
			return undefined;
		}
		if (!indexValorMeta) {
			return undefined;
		}
		const meta = metas[0];
		const valorMeta = meta.valores[indexValorMeta];

		return {
			valorEsperado: valorMeta.valor,
			valorRealizado:
				faturamento > valorMeta.valor
					? faturamento
					: valorMeta.valor - faturamento,
		};
	}
}

@Component({
	selector: "adm-bi-card-metas-financeiras",
	templateUrl: "./bi-card-metas-financeiras.component.html",
	styleUrls: ["./bi-card-metas-financeiras.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BiCardMetasFinanceirasComponent
	extends BiCardWithModalsBase
	implements OnInit, AfterViewInit, OnDestroy
{
	loading: boolean;
	semMetaMes: boolean;
	tabData: TabData;
	barData: Ds3BarData;
	infoMetaBatida: string;
	metaFinRespModel: MetaFinanceiraResponseModel;

	@ViewChild("metaUmTooltip", { static: false })
	metaUmTooltip: TemplateRef<any>;

	@ViewChild("metaDoisTooltip", { static: false })
	metaDoisTooltip: TemplateRef<any>;

	@ViewChild("metaTresTooltip", { static: false })
	metaTresTooltip: TemplateRef<any>;

	@ViewChild("metaQuatroTooltip", { static: false })
	metaQuatroTooltip: TemplateRef<any>;

	@ViewChild("metaCincoTooltip", { static: false })
	metaCincoTooltip: TemplateRef<any>;

	constructor(
		protected breakpointObserver: BreakpointObserver,
		protected cd: ChangeDetectorRef,
		protected currencyPipe: CurrencyPipe,
		protected datePipe: DatePipe,
		protected dialog: MatDialog,
		protected router: Router,
		protected biCommonService: BiCommonService,
		private bi: BiMsApiMetaFinanceiraService,
		private toastrService: ToastrService,
		private biSidenavService: BiSidenavService,
		private biMdlAjudaService: BiMdlAjudaService
	) {
		super(
			breakpointObserver,
			cd,
			currencyPipe,
			dialog,
			router,
			biCommonService
		);
	}

	ngOnInit() {
		super.init();
		this.listenReloadAllBIs();
	}

	ngAfterViewInit() {
		this.loadData(this.reloadFull);
	}

	ngOnDestroy() {
		super.destroy();
	}

	abrirAjuda() {
		this.biMdlAjudaService.openMdl({
			title: "Metas financeiras",
			url: "https://pactosolucoes.com.br/ajuda/conhecimento/bi-metas-financeiras-de-venda-adm/",
			module: ConhecimentoEnum.ADM,
		});
	}

	loadData(reloadFull?: boolean) {
		if (this.withFakeData) {
			this._populateTabData();
			this.cd.detectChanges();
		} else {
			this._loadDataApi(reloadFull);
		}
	}

	openSideNav() {
		const ref = this.biSidenavService.open(BiMetasFinanceirasFilterComponent, {
			globalFilter: this.globalFilterForm.value,
			filters: this.filtros,
		});

		ref.afterClosed.pipe(takeUntil(this.destroy$)).subscribe((data) => {
			if (data && data.filters) {
				Object.assign(this.filtros, data.filters);
				this.loadData(true);
			}
		});
	}

	private _loadDataApi(reloadFull?: boolean) {
		this.loading = true;
		this.cd.detectChanges();

		const inicio = new Date(
			this.filtros.data.getFullYear(),
			this.filtros.data.getMonth(),
			1,
			0,
			0,
			0,
			0
		);
		this.filtros.dataInicio = inicio;
		this.bi
			.list({
				filtroMetaFinanceira: {
					dataInicial: this.filtros.dataInicio.getTime(),
					dataFinal: this.filtros.data.getTime(),
					empresa: this.filtros.empresa,
					incluirMetas: true,
					matricula: true,
					rematricula: true,
					renovacao: true,
					consultorAtual: false,
					metaFinanceiraPorFaturamento: true,
					consultarConfigFinFaturamentoPorPeriodo: true,
					colaboradores: this.filtros.colaboradores,
					tipoFormaPagamentoSigla: "CC",
					tipoProduto: "CH",
				} as FiltroMetaFinanceira,
				reloadFull,
			})
			.pipe(takeUntil(this.destroy$))
			.subscribe({
				next: (v: MetaFinanceiraResponseModel) => {
					if (v.metas.length === 0) {
						this.semMetaMes = true;
					} else {
						this.semMetaMes = false;
						this._populateTabData(v);
					}
					this.loading = false;
					this.cd.detectChanges();
				},
				error: (httpResponseError) => {
					const message =
						httpResponseError &&
						httpResponseError.error &&
						httpResponseError.error.meta &&
						httpResponseError.error.meta.message;

					this.toastrService.error(message || "Ocorreu um erro desconhecido!");
					this.loading = false;
					this.cd.detectChanges();
				},
			});
	}

	private _populateTabData(v?: MetaFinanceiraResponseModel) {
		if (!v) {
			v = {
				faturamentoRecebido: 0,
				faturamentoDevolvido: 0,
				faturamentoLiquido: 0,
				faturamentoRecebidoPorDia: {},
				faturamentoRecebidoSimplificado: "",
				valorMetaAtingida: 0,
				metas: new Array<MetaFinanceiraEmpresaModel>(),
				corMetaAtingida: "",
				bateuTodasAsMetas: false,
				quantidadeMetasAtingidas: 0,
				faturamentoPeriodo: new Array<FaturamentoPeriodoModel>(),
				totalFaturamentoPeriodo: 0,
			};
		}
		this.metaFinRespModel = v;
		this.tabData = {
			sections: [
				{
					infoItens: [
						{
							info: {
								value: v.faturamentoRecebido,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: true,
								isPercentage: false,
								state: "disable",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								text: "Total realizado",
							},
						},
					],
				},
				{
					infoItens: [
						{
							info: {
								value: 0,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: true,
								isPercentage: false,
								state: "disable",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								text: "Metas do dia",
							},
						},
						{
							info: {
								value: 0,
								size: "24px",
								afterIcon: undefined,
								beforeIcon: undefined,
								isMonetary: true,
								isPercentage: false,
								state: "disable",
							},
							auxiliary: undefined,
							textButton: undefined,
							overline: {
								avatarImage: undefined,
								dotHexColor: undefined,
								text: "Realizado do dia",
							},
						},
					],
				},
			],
		};

		this._populateBarLine(v);
	}

	private _populateBarLine(v: MetaFinanceiraResponseModel) {
		const arrayCores = [
			{
				corLinha: BarCorLinha.info,
				corFundo: BarCorFundo.info_bg,
			},
			{
				corLinha: BarCorLinha.purple,
				corFundo: BarCorFundo.purple_bg,
			},
			{
				corLinha: BarCorLinha.green,
				corFundo: BarCorFundo.green_bg,
			},
			{
				corLinha: BarCorLinha.yellow,
				corFundo: BarCorFundo.yellow_bg,
			},
			{
				corLinha: BarCorLinha.lightblue,
				corFundo: BarCorFundo.lightblue_bg,
			},
		];
		this.barData = {
			nome: "Meta do mês",
			porcentagem: 0,
			etapas: [
				{
					nome: "Meta 1",
					corLinha: arrayCores[0].corLinha,
					corFundo: arrayCores[0].corFundo,
					// tooltip: this.metaUmTooltip,
				},
				{
					nome: "Meta 2",
					corLinha: arrayCores[1].corLinha,
					corFundo: arrayCores[1].corFundo,
					// tooltip: this.metaDoisTooltip,
				},
				{
					nome: "Meta 3",
					corLinha: arrayCores[2].corLinha,
					corFundo: arrayCores[2].corFundo,
					// tooltip: this.metaTresTooltip,
				},
				{
					nome: "Meta 4",
					corLinha: arrayCores[3].corLinha,
					corFundo: arrayCores[3].corFundo,
					// tooltip: this.metaQuatroTooltip,
				},
				{
					nome: "Meta 5",
					corLinha: arrayCores[4].corLinha,
					corFundo: arrayCores[4].corFundo,
					// tooltip: this.metaCincoTooltip,
				},
			],
			mostraPorcentagem: true,
			porcentagemComoInfo: false,
			info: "R$ 00,00",
		};

		if (v.metas) {
			this.barData.etapas = [];
			v.metas.forEach((meta: MetaFinanceiraEmpresaModel, index: number) => {
				this.barData.nome = meta.descricao;
				if (meta.valores) {
					meta.valores.forEach((valorMeta, indexValorMeta) => {
						if (valorMeta.valor > 0) {
							this.barData.etapas.push({
								nome: `Meta 0${index + 1}`,
								corLinha: arrayCores[indexValorMeta].corLinha,
								corFundo: arrayCores[indexValorMeta].corFundo,
							});

							if (valorMeta.bateuMeta) {
								if (indexValorMeta === 0) {
									this.infoMetaBatida = "Primeira";
								}
								if (indexValorMeta === 1) {
									this.infoMetaBatida = "Segunda";
								}
								if (indexValorMeta === 2) {
									this.infoMetaBatida = "Terceira";
								}
								if (indexValorMeta === 3) {
									this.infoMetaBatida = "Quarta";
								}
								if (indexValorMeta === 4) {
									this.infoMetaBatida = "Quinta";
								}
								this.infoMetaBatida += " batida";
							}
						}
					});

					const valoresMetaNaoZerados = meta.valores.filter(
						(valorMeta) => valorMeta.valor > 0
					);
					if (valoresMetaNaoZerados && valoresMetaNaoZerados.length > 0) {
						const indexValorMetaBase = valoresMetaNaoZerados.findIndex(
							(valorMeta) => valorMeta.valor >= v.faturamentoRecebido
						);
						const ultimoValor =
							valoresMetaNaoZerados[valoresMetaNaoZerados.length - 1].valor;
						if (indexValorMetaBase !== -1) {
							const valorMetaBase = valoresMetaNaoZerados[indexValorMetaBase];
							this.barData.porcentagem = this.roundTo(
								((v.faturamentoRecebido / valorMetaBase.valor) * 100) /
									(valoresMetaNaoZerados.length - indexValorMetaBase),
								2
							);
						} else {
							this.barData.porcentagem = this.roundTo(
								(v.faturamentoRecebido / ultimoValor) * 100,
								2
							);
						}
						this.barData.info = this.currencyPipe.transform(ultimoValor, "BRL");
					}
				}
			});
		}
	}

	cadastrarMeta() {}

	trackByIndex(index, item) {
		return index;
	}

	verDetalhesMeta() {}

	roundTo(num: number, decimals: number): number {
		return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals);
	}
}
