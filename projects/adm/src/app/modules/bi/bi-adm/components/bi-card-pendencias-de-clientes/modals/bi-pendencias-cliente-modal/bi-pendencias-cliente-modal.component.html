<adm-bi-modal-content
	(close)="onClose()"
	(pageChange)="onPageChange($event)"
	(pageSizeChange)="onPageSizeChange($event)"
	(search)="quickSearch($event)"
	[colunasShare]="colunasShare"
	[filters]="filters"
	[modalTitle]="modalTitle"
	[pageSize]="size"
	[page]="page"
	[shareUrl]="shareUrl"
	[totalItems]="totalItems">
	<ds3-table>
		<table [stateManager]="tableState" ds3DataTable>
			<ng-container *ngFor="let column of columns">
				<ng-container [ds3TableColumn]="column.id">
					<th *ds3TableHeaderCell>{{ column.label }}</th>
					<td *ds3TableCell="let item" class="nomeEntidade">
						<button
							(click)="column.onClick(item)"
							ds3-text-button
							*ngIf="column.clickable; else noClickable">
							<ng-container *ngIf="column.titlecase">
								{{ item | objectValue : column.attribute | titlecase }}
							</ng-container>
							<ng-container *ngIf="column.date">
								{{
									item
										| objectValue : column.attribute
										| date : "dd/MM/yyyy" : "UTC"
								}}
							</ng-container>
							<ng-container *ngIf="column.celulaSituacaoCliente">
								<ds3-status [color]="item?.corSituacaoCliente">
									{{ item | objectValue : column.attribute }}
								</ds3-status>
							</ng-container>
							<ng-container *ngIf="column.datetime">
								{{
									item
										| objectValue : column.attribute
										| date : "dd/MM/yyyy HH:mm:ss" : "UTC"
								}}
							</ng-container>
							<ng-container *ngIf="column.money">
								{{ item | objectValue : column.attribute | currency : "BRL" }}
							</ng-container>
							<ng-container *ngIf="column.decimal">
								{{ item | objectValue : column.attribute | number : "1.2-2" }}
							</ng-container>
							<ng-container *ngIf="column.textColumn">
								{{ item | objectValue : column.attribute }}
							</ng-container>
						</button>

						<ng-template #noClickable>
							<ng-container *ngIf="column.titlecase">
								{{ item | objectValue : column.attribute | titlecase }}
							</ng-container>
							<ng-container *ngIf="column.date">
								{{
									item
										| objectValue : column.attribute
										| date : "dd/MM/yyyy" : "UTC"
								}}
							</ng-container>
							<ng-container *ngIf="column.celulaSituacaoCliente">
								<ds3-status [color]="item?.corSituacaoCliente">
									{{ item | objectValue : column.attribute }}
								</ds3-status>
							</ng-container>
							<ng-container *ngIf="column.datetime">
								{{
									item
										| objectValue : column.attribute
										| date : "dd/MM/yyyy HH:mm:ss" : "UTC"
								}}
							</ng-container>
							<ng-container *ngIf="column.decimal">
								{{ item | objectValue : column.attribute | number : "1.2-2" }}
							</ng-container>
							<ng-container *ngIf="column.money">
								{{ item | objectValue : column.attribute | currency : "BRL" }}
							</ng-container>
							<ng-container *ngIf="column.textColumn">
								{{
									item | objectValue : column.attribute : column.valueTransform
								}}
							</ng-container>
						</ng-template>
					</td>
				</ng-container>
			</ng-container>

			<tr *ds3TableRow></tr>

			<button
				(click)="triggerSortToggle()"
				*ds3TableSortControl="
					let direction = direction;
					let triggerSortToggle = triggerSortToggle
				"
				class="sort-control"
				ds3-icon-button>
				<i
					[ngClass]="{
						'pct-drop-down': direction === null,
						'pct-caret-up': direction === 'ASC',
						'pct-caret-down': direction === 'DESC'
					}"
					class="pct"></i>
			</button>

			<tr *ds3TableEmptyRow class="ds3-table-empty">
				<td>
					<h2>Nenhum item encontrado</h2>
					<p>
						Nenhum item encontrado no período, tente realizar uma nova busca.
					</p>
				</td>
			</tr>

			<tbody *ds3TableLoading>
				<tr>
					<td>
						<div class="bi-modal-table-loader" role="status">
							<img
								alt="Loading pacto"
								src="pacto-ui/images/gif/loading-pacto.gif" />
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</ds3-table>
</adm-bi-modal-content>
