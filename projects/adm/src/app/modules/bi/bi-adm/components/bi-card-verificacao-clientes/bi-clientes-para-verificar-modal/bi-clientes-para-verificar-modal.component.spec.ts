import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiClientesParaVerificarModalComponent } from "./bi-clientes-para-verificar-modal.component";

describe("BiClientesParaVerificarModalComponent", () => {
	let component: BiClientesParaVerificarModalComponent;
	let fixture: ComponentFixture<BiClientesParaVerificarModalComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiClientesParaVerificarModalComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiClientesParaVerificarModalComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
