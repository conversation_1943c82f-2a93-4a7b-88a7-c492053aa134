import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiCardMovientacaoContratosFilterComponent } from "./bi-card-movientacao-contratos-filter.component";

describe("BiCardMovientacaoContratosFilterComponent", () => {
	let component: BiCardMovientacaoContratosFilterComponent;
	let fixture: ComponentFixture<BiCardMovientacaoContratosFilterComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiCardMovientacaoContratosFilterComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(
			BiCardMovientacaoContratosFilterComponent
		);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
