import { ChangeDetector<PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import {
	AdmCoreApiGrupoColaboradorService,
	ApiResponseList,
	Colaborador,
	GrupoColaborador,
	GrupoColaboradorParticipante,
	MetaFinanceiraEmpresaFiltroModel,
	MetaFinanceiraEmpresaModel,
	MetaFinanceiraEmpresaService,
} from "adm-core-api";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { EmpresaFinanceiro, SessionService } from "sdk";
import { BiGlobalFilter } from "../../../../bi-shared/models/bi-global.filter";
import { BiSidenavRef } from "../../../../bi-shared/services/bi-sidenav/bi-sidenav.service";

@Component({
	selector: "adm-bi-metas-financeiras-filter",
	templateUrl: "./bi-metas-financeiras-filter.component.html",
	styleUrls: ["./bi-metas-financeiras-filter.component.scss"],
})
export class BiMetasFinanceirasFilterComponent implements OnInit, OnDestroy {
	globalFilter: BiGlobalFilter;

	form: FormGroup = new FormGroup({
		data: new FormControl(),
		meta: new FormControl(),
		gruposColaboradores: new FormControl(),
		colaboradores: new FormControl(),
	});

	filters: {
		data?: Date;
		empresa?: number;
		empresas?: Array<EmpresaFinanceiro> | Array<number>;
		meta?: MetaFinanceiraEmpresaModel | number;
		gruposColaboradores?: Array<number>;
		colaboradores?: Array<number>;
		colaboradoresObj?: Array<{ label: string; value: any }>;
		gruposColaboradoresObj?: Array<GrupoColaborador>;
		quickSearchValue?: string;
	} = {};

	gruposColaboradores: Array<GrupoColaborador> = new Array<GrupoColaborador>();
	optionsColaboradores: Array<{ value: any; label: string }> = new Array<{
		value: any;
		label: string;
	}>();
	optionsMetas: Array<MetaFinanceiraEmpresaModel> =
		new Array<MetaFinanceiraEmpresaModel>();

	private _destroyed$: Subject<void> = new Subject<void>();

	constructor(
		private admCoreApiGrupoColaboradorService: AdmCoreApiGrupoColaboradorService,
		private biSidenavRef: BiSidenavRef<BiMetasFinanceirasFilterComponent>,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private metaFinanceiraEmpresaService: MetaFinanceiraEmpresaService
	) {}

	ngOnInit() {
		this._loadgruposColaboradores();
		this.loadMetaFinanceira();
		this._populateByGlobalFilter();
		this._populateFormByFilters();
		this._listenValueChanges();
	}

	ngOnDestroy() {
		this._destroyed$.next();
	}

	private _listenValueChanges() {
		this.form.get("data").valueChanges.subscribe((v) => {
			this.filters.data = v;
			this.loadMetaFinanceira();
		});
		this._listenGrupoColaboradorChanges();
	}

	private _populateByGlobalFilter() {
		if (this.globalFilter) {
			this.form.get("data").setValue(this.globalFilter.dataGeral);
		}
	}

	private _populateFormByFilters() {
		if (this.filters) {
			this.form.patchValue({
				data: this.filters.data,
				gruposColaboradores: this.filters.gruposColaboradores,
				meta:
					this.filters.meta && typeof this.filters.meta !== "number"
						? this.filters.meta.codigo
						: this.filters.meta,
			});
		}
	}

	private _loadgruposColaboradores() {
		this.admCoreApiGrupoColaboradorService
			.findByEmpresaLogada()
			.subscribe((response: ApiResponseList<GrupoColaborador>) => {
				this.gruposColaboradores = response.content;
				this.gruposColaboradores = this.gruposColaboradores.map((v) => {
					if (v.descricao === "SEM GRUPO") {
						v.codigo = -1;
					}
					return v;
				});
				const colaboradores = [];
				const grupoColaboradores: Array<GrupoColaboradorParticipante> = [];
				const colaboradoresDistinct: Map<number, Colaborador> = new Map<
					number,
					Colaborador
				>();
				this.gruposColaboradores.forEach((gc) =>
					grupoColaboradores.push(...gc.grupoColaboradorParticipantes)
				);
				grupoColaboradores.forEach((v) => {
					if (!colaboradoresDistinct.has(v.codigo)) {
						colaboradoresDistinct.set(
							v.colaboradorParticipante.codigo,
							v.colaboradorParticipante
						);
					}
				});
				colaboradoresDistinct.forEach((v) => {
					colaboradores.push({ value: v.codigo, label: v.pessoa.nome });
				});
				colaboradores.forEach((c) => this.optionsColaboradores.push(c));
				this.optionsColaboradores = this.optionsColaboradores.sort((c1, c2) => {
					if (c1.label > c2.label) {
						return 1;
					} else if (c1.label < c2.label) {
						return -1;
					} else {
						return 0;
					}
				});

				if (this.filters && this.filters.colaboradores) {
					this.form.get("colaboradores").setValue(this.filters.colaboradores);
				}
				this.cd.detectChanges();
			});
	}

	loadMetaFinanceira(searchEvent?: { term: string }) {
		const filters: MetaFinanceiraEmpresaFiltroModel =
			{} as MetaFinanceiraEmpresaFiltroModel;

		if (this.filters) {
			if (!this.filters.data) {
				this.filters.data = new Date();
			}
			filters.mes = this.filters.data.getUTCMonth() + 1;
			filters.ano = this.filters.data.getUTCFullYear();

			if (!this.filters.empresas) {
				this.filters.empresas = this.sessionService.empresas.filter(
					(empresa) => empresa.codigo === this.filters.empresa
				);
			}

			filters.empresas = (
				this.filters.empresas as Array<EmpresaFinanceiro>
			).map((v) => v.codigo);

			if (searchEvent && searchEvent.term) {
				this.filters.quickSearchValue = searchEvent.term;
			}

			filters.quickSearchValue = this.filters.quickSearchValue;
		}

		this.metaFinanceiraEmpresaService
			.findByEmpresasMesAno({
				filters,
				pageInfo: { page: 0, size: 50 },
			})
			.subscribe({
				next: (response) => {
					this.optionsMetas = response.content;
					this.cd.detectChanges();
				},
			});
	}

	onClose() {
		this.biSidenavRef.close();
	}

	onFilter() {
		if (!this.filters) {
			this.filters = this.form.value;
		} else {
			Object.assign(this.filters, this.form.value);
		}
		this._setObjectValuesForFilters();
		let meta: any = this.filters.meta;
		if (typeof this.filters.meta === "number") {
			const metaFinded = this.optionsMetas.find(
				(m) => m.codigo === this.filters.meta
			);
			if (metaFinded) {
				meta = metaFinded;
			}
		}
		this.biSidenavRef.close({
			filters: {
				...this.filters,
				meta,
				colaboradoresNomes: this.optionsColaboradores
					.filter(
						(v) =>
							this.form.value.colaboradores &&
							this.form.value.colaboradores.includes(v.value)
					)
					.map((v) => v.label),
			},
		});
	}

	private _setObjectValuesForFilters() {
		if (this.filters) {
			if (this.filters.colaboradores) {
				const filterOptions = this.optionsColaboradores.filter((v) =>
					this.filters.colaboradores.includes(v.value)
				);

				if (filterOptions) {
					this.filters.colaboradoresObj = filterOptions;
				}
			}
			if (this.filters.gruposColaboradores) {
				const filterOptions = this.gruposColaboradores.filter((v) =>
					this.filters.gruposColaboradores.includes(v.codigo)
				);

				if (filterOptions) {
					this.filters.gruposColaboradoresObj = filterOptions;
				}
			}
		}
	}

	onClear() {
		this.form.reset({});
		this._populateByGlobalFilter();
		this.biSidenavRef.close({ filters: this.form.value });
	}

	private _listenGrupoColaboradorChanges() {
		this.form
			.get("gruposColaboradores")
			.valueChanges.pipe(takeUntil(this._destroyed$))
			.subscribe((value) => {
				const grupoColaboradorSelecionado = this.gruposColaboradores.filter(
					(gc) => value.includes(gc.codigo)
				);

				if (grupoColaboradorSelecionado) {
					const colaboradores = new Array<number>();
					const colaboradoresDistinct: Map<number, Colaborador> = new Map<
						number,
						Colaborador
					>();
					const grupoColaboradores: Array<GrupoColaboradorParticipante> = [];
					grupoColaboradorSelecionado.forEach((gcs) => {
						grupoColaboradores.push(...gcs.grupoColaboradorParticipantes);
					});
					grupoColaboradores.forEach((v) => {
						if (!colaboradoresDistinct.has(v.codigo)) {
							colaboradoresDistinct.set(
								v.colaboradorParticipante.codigo,
								v.colaboradorParticipante
							);
						}
					});
					colaboradoresDistinct.forEach((v) => {
						colaboradores.push(v.codigo);
					});

					this.form.get("colaboradores").setValue(colaboradores);
					this.cd.detectChanges();
				}
			});
	}
}
