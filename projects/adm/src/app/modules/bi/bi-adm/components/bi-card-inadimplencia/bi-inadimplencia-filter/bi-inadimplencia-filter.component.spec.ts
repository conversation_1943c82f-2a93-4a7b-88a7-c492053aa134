import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiInadimplenciaFilterComponent } from "./bi-inadimplencia-filter.component";

describe("BiInadimplenciaFilterComponent", () => {
	let component: BiInadimplenciaFilterComponent;
	let fixture: ComponentFixture<BiInadimplenciaFilterComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiInadimplenciaFilterComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiInadimplenciaFilterComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
