import { BiGlobalFilter } from "@adm/modules/bi/bi-shared/models/bi-global.filter";
import { BiSidenavRef } from "@adm/modules/bi/bi-shared/services/bi-sidenav/bi-sidenav.service";
import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import {
	isTipoIntegradorGympass,
	isTipoIntegradorTotalpass,
	TipoIntegradorAcesso,
} from "bi-ms-api";

@Component({
	selector: "adm-bi-card-integradores-de-acesso-filter",
	templateUrl: "./bi-card-integradores-de-acesso-filter.component.html",
	styleUrls: ["./bi-card-integradores-de-acesso-filter.component.scss"],
})
export class BiCardIntegradoresDeAcessoFilterComponent implements OnInit {
	globalFilter: BiGlobalFilter;

	form: FormGroup = new FormGroup({
		data: new FormControl(),
		acessoCheckinGympass: new FormControl(),
		tipoIntegradorAcessoWellhub: new FormControl(),
		tipoIntegradorAcessoTotalpass: new FormControl(),
	});

	filters: {
		data: Date;
		acessoCheckinGympass: boolean;
		tipoIntegradorAcesso: number;
	};

	constructor(
		private biSidenavRef: BiSidenavRef<BiCardIntegradoresDeAcessoFilterComponent>
	) {}

	ngOnInit() {
		this._populateByGlobalFilter();
		this._populateFormByFilters();
	}

	private _populateByGlobalFilter() {
		if (this.globalFilter) {
			this.form.get("data").setValue(this.globalFilter.dataGeral);
		}
	}

	private _populateFormByFilters() {
		if (this.filters) {
			this.form.patchValue({
				data: this.filters.data,
				acessoCheckinGympass: this.filters.acessoCheckinGympass,
				tipoIntegradorAcessoWellhub: isTipoIntegradorGympass(
					this.filters.tipoIntegradorAcesso
				),
				tipoIntegradorAcessoTotalpass: isTipoIntegradorTotalpass(
					this.filters.tipoIntegradorAcesso
				),
			});
		}
	}

	onClose() {
		this.biSidenavRef.close();
	}

	onFilter() {
		if (!this.filters) {
			this.filters = this.form.value;
		} else {
			Object.assign(this.filters, this.form.value);
		}
		let tipoIntegradorAcesso = TipoIntegradorAcesso.TODOS;
		if (
			this.form.get("tipoIntegradorAcessoWellhub").value &&
			this.form.get("tipoIntegradorAcessoTotalpass").value
		) {
			tipoIntegradorAcesso = TipoIntegradorAcesso.TODOS;
		} else if (this.form.get("tipoIntegradorAcessoWellhub").value) {
			tipoIntegradorAcesso = TipoIntegradorAcesso.GYMPASS;
		} else if (this.form.get("tipoIntegradorAcessoTotalpass").value) {
			tipoIntegradorAcesso = TipoIntegradorAcesso.TOTALPASS;
		}
		const dataInicio = new Date(this.filters.data);
		dataInicio.setDate(1);
		this.biSidenavRef.close({
			filters: {
				dataInicio,
				dataFim: this.filters.data,
				acessoCheckinGympass: this.filters.acessoCheckinGympass,
				tipoIntegradorAcesso,
			},
		});
	}

	onClear() {
		this.form.reset({});
		this._populateByGlobalFilter();
		this.biSidenavRef.close({ filters: this.form.value });
	}

	selectConsiderarAcessoCheckin(selected: boolean) {
		this.form.get("acessoCheckinGympass").setValue(selected);
	}

	selectIntegradorWellhub(selected: boolean) {
		let value = null;
		if (selected) {
			value = TipoIntegradorAcesso.GYMPASS;
		}
		this.form.get("tipoIntegradorAcessoWellhub").setValue(value);
	}

	selectIntegradorTotalpass(selected: boolean) {
		let value = null;
		if (selected) {
			value = TipoIntegradorAcesso.TOTALPASS;
		}
		this.form.get("tipoIntegradorAcessoTotalpass").setValue(value);
	}
}
