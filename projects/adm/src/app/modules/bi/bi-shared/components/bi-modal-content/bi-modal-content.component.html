<div class="modal-title">
	<div class="modal-title-content">
		<h2 class="pct-title4">
			{{ modalTitle }}
		</h2>
		<button ds3-icon-button (click)="close.emit()">
			<i class="pct pct-x"></i>
		</button>
	</div>
	<ds3-diviser></ds3-diviser>
</div>
<div class="modal-content">
	<div class="ds3-table-head">
		<form [formGroup]="searchForm" (submit)="onSearch()">
			<div class="ds3-table-search" *ngIf="showSearch">
				<div [ds3Tooltip]="hintQuickSearch">
					<ds3-form-field>
						<input
							ds3Input
							autofocus
							placeholder="Busca rápida"
							formControlName="search" />
						<i ds3Prefix class="pct pct-search"></i>
					</ds3-form-field>
				</div>

				<div class="ds3-table-search-button">
					<button ds3-flat-button type="submit">Consultar</button>
				</div>
			</div>
		</form>

		<div class="ds3-table-actions" *ngIf="showShare">
			<pacto-share-button
				[columns]="colunasShare"
				[telaId]="telaId"
				[table]="true"
				[titulo]="modalTitle"
				[filtros]="filterConfigs"
				[endpoint]="shareUrl"></pacto-share-button>
		</div>
	</div>
	<div class="modal-middle-content" #middleContent>
		<ng-content></ng-content>
	</div>
	<ds3-diviser *ngIf="withPaginatorDiviser"></ds3-diviser>
	<div
		class="ds3-table-footer"
		[class.with-paginator-diviser]="withPaginatorDiviser"
		*ngIf="showPaginator">
		<ds3-pagination
			[length]="totalItems"
			[pageSize]="pageSize"
			[pageIndex]="page"
			[pageSizeOptions]="pageSizeOptions"
			(pageSizeChange)="pageSizeChange.emit($event)"
			(pageChange)="pageChange.emit($event)"></ds3-pagination>
	</div>
</div>

<ng-template #hintQuickSearch>
	<div>
		<strong>
			A pesquisa será realizada utilizando todas as colunas da tabela!
		</strong>
	</div>
	<br />
	<div>
		<strong>
			Para valores monetários utilize somente números e "." para
			<br />
			separar as casas decimais!
		</strong>
	</div>
	<br />
	<div>
		<strong>Após digitar pressione ENTER</strong>
	</div>
</ng-template>
