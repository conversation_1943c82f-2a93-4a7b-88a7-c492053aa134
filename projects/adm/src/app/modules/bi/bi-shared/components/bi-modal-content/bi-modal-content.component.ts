import {
	AfterContentInit,
	AfterViewInit,
	Component,
	ContentChild,
	ElementRef,
	EventEmitter,
	Input,
	OnChanges,
	OnDestroy,
	OnInit,
	Output,
	SimpleChanges,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { debounceTime, distinctUntilChanged, takeUntil } from "rxjs/operators";
import { Ds3TableComponent, PactoDataGridColumnConfig } from "ui-kit";
import { BreakpointObserver } from "@angular/cdk/layout";
import { Subject } from "rxjs";
import { ToastrService } from "ngx-toastr";

@Component({
	selector: "adm-bi-modal-content",
	templateUrl: "./bi-modal-content.component.html",
	styleUrls: ["./bi-modal-content.component.scss"],
	host: {
		"(window:resize)": "onWindowResize()",
	},
})
export class BiModalContentComponent
	implements OnInit, OnChanges, OnDestroy, AfterContentInit, AfterViewInit
{
	@Input() modalTitle: string;
	@Input() page: number = 0;
	@Input() pageSize: number = 50;
	@Input() totalItems: number = 0;
	@Input() pageSizeOptions = [50, 100, 150];
	@Input() colunasShare: Array<PactoDataGridColumnConfig>;
	@Input() shareUrl: string;
	@Input() filters;
	@Input() showSearch = true;
	@Input() showShare = true;
	@Input() showPaginator = true;
	@Input() withPaginatorDiviser = false;
	@Input() telaId: string;

	@Output() close: EventEmitter<any> = new EventEmitter();
	@Output() pageChange: EventEmitter<any> = new EventEmitter();
	@Output() pageSizeChange: EventEmitter<any> = new EventEmitter();
	@Output() search: EventEmitter<any> = new EventEmitter();

	@ContentChild(Ds3TableComponent, { static: false })
	tableComponent: Ds3TableComponent<any>;

	@ViewChild("middleContent", { static: false })
	middleContent: ElementRef;

	searchForm: FormGroup = new FormGroup({
		search: new FormControl(),
	});
	filterConfigs = {
		filters: {},
		configs: {},
	};
	private _destroy$: Subject<void> = new Subject();
	private firstValueWithPaginatorDiviser: boolean = false;

	constructor(
		private breakpointObserver: BreakpointObserver,
		private notificationService: ToastrService
	) {}

	ngOnInit() {
		if (this.filters) {
			this.filterConfigs.filters = this.filters;
		}
		if (this.modalTitle) {
			this.modalTitle = this.modalTitle.trim();
			if (!this.telaId) {
				this.telaId = this.stringToKebab(this.modalTitle);
			}
		}
	}

	ngAfterViewInit() {
		this._calculateMiddleContentHeight();
	}

	ngAfterContentInit() {
		this.onWindowResize();
	}

	private _calculateMiddleContentHeight() {
		if (this.middleContent) {
			const hostHeight = window.innerHeight;
			this.middleContent.nativeElement.style.maxHeight = `${
				hostHeight - 260
			}px`;
		}
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes.filters && !changes.filters.firstChange) {
			this.filterConfigs.filters = changes.filters.currentValue;
		}

		if (
			changes.withPaginatorDiviser &&
			changes.withPaginatorDiviser.firstChange
		) {
			this.firstValueWithPaginatorDiviser =
				changes.withPaginatorDiviser.currentValue;
		}
	}

	ngOnDestroy() {
		this._destroy$.next();
	}

	onSearch() {
		this.page = 0;
		this.search.emit(this.searchForm.value.search);
	}

	onWindowResize() {
		// Necessário devido a um timeout que foi implementado na table
		setTimeout(() => {
			if (this.tableComponent && !this.firstValueWithPaginatorDiviser) {
				const isScrolling: boolean =
					this.tableComponent.elementRef.nativeElement.classList.contains(
						"ds3-h-scroll"
					);
				this.withPaginatorDiviser = isScrolling;
			}
			this._calculateMiddleContentHeight();
		}, 300);
	}

	stringToKebab(str: string): string {
		return str
			.normalize("NFD")
			.replace(/[\u0300-\u036f]/g, "") // Remove acentos
			.replace(/[^\w\s-]/g, "") // Remove símbolos, preserva espaços e hífens
			.replace(/\s+/g, "-") // Substitui espaços por hífen
			.replace(/-+/g, "-") // Remove hífens duplicados
			.replace(/^-|-$/g, "") // Remove hífens no início ou fim
			.replace(/-.*$/, "") // 🔥 Remove o último hífen e tudo depois dele
			.toLowerCase();
	}
}
