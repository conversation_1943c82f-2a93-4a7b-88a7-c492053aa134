import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiSideFilterBaseContentComponent } from "./bi-side-filter-base-content.component";

describe("BiSideFilterBaseContentComponent", () => {
	let component: BiSideFilterBaseContentComponent;
	let fixture: ComponentFixture<BiSideFilterBaseContentComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiSideFilterBaseContentComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiSideFilterBaseContentComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
