import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiSideFilterComponent } from "./bi-side-filter.component";

describe("BiSideFilterComponent", () => {
	let component: BiSideFilterComponent;
	let fixture: ComponentFixture<BiSideFilterComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiSideFilterComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiSideFilterComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
