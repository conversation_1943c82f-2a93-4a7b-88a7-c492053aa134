import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";

@Component({
	selector: "adm-bi-side-filter-base-content",
	templateUrl: "./bi-side-filter-base-content.component.html",
	styleUrls: ["./bi-side-filter-base-content.component.scss"],
})
export class BiSideFilterBaseContentComponent implements OnInit {
	@Input() title: string = "Filtros";

	@Output() filter: EventEmitter<any> = new EventEmitter<any>();
	@Output() clear: EventEmitter<any> = new EventEmitter<any>();
	@Output() closeSidenav: EventEmitter<any> = new EventEmitter<any>();

	constructor() {}

	ngOnInit() {}
}
