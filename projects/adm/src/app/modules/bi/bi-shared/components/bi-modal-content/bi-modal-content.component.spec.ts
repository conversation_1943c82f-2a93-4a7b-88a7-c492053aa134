import { async, ComponentFixture, TestBed } from "@angular/core/testing";

import { BiModalContentComponent } from "./bi-modal-content.component";

describe("BiModalContentComponent", () => {
	let component: BiModalContentComponent;
	let fixture: ComponentFixture<BiModalContentComponent>;

	beforeEach(async(() => {
		TestBed.configureTestingModule({
			declarations: [BiModalContentComponent],
		}).compileComponents();
	}));

	beforeEach(() => {
		fixture = TestBed.createComponent(BiModalContentComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it("should create", () => {
		expect(component).toBeTruthy();
	});
});
