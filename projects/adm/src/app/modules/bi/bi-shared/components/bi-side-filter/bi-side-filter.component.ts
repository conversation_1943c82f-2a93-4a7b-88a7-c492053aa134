import {
	AfterViewInit,
	Component,
	OnInit,
	ViewChild,
	ViewContainerRef,
} from "@angular/core";
import { MatSidenav } from "@angular/material/sidenav";
import { BiSidenavService } from "../../services/bi-sidenav/bi-sidenav.service";

@Component({
	selector: "adm-bi-side-filter",
	templateUrl: "./bi-side-filter.component.html",
	styleUrls: ["./bi-side-filter.component.scss"],
})
export class BiSideFilterComponent implements AfterViewInit {
	@ViewChild("sidenav", { static: false }) sidenav!: MatSidenav;
	@ViewChild("contentContainer", { static: false, read: ViewContainerRef })
	viewContainerRef!: ViewContainerRef;

	constructor(private sidenavService: BiSidenavService) {}

	ngAfterViewInit() {
		this.sidenavService.setSidenav(this.sidenav, this.viewContainerRef);
	}

	close() {
		this.sidenav.close();
	}
}
