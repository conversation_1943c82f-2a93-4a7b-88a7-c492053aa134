@import "dist/ui-kit/assets/ds3/typography/mixins";

:host {
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;

	.bi-side-filter-header {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		background: var(--color-background-plane-2);
		border-bottom: 1px solid var(--color-support-gray-3);
		padding: 16px 16px 7px 16px;

		.bi-side-filter-title {
			@include apply-typography-style("title", 4);
			color: var(--color-typography-default-title);
		}
		button {
			margin-right: 8px;
		}
	}

	.bi-side-filter-content {
		height: 100%;
		padding: 8px 16px;
		margin-top: 16px;
		gap: 16px;
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		overflow-x: hidden;
	}

	.bi-side-filter-actions {
		background-color: var(--color-background-plane-1);
		display: grid;
		gap: 16px;
		grid-template-columns: 1fr 1fr;
		padding: 8px 16px;
	}
}
