import { Injectable } from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { LayoutNavigationService, PlataformModuleConfig } from "pacto-layout";
import { AdmCoreApiNegociacaoService } from "adm-core-api";
import { BehaviorSubject, Observable, Subject } from "rxjs";
import { BiSharedModule } from "../../bi-shared.module";

export interface GlobalFilter {
	empresa: number;
	empresaNome: string;
	dataGeral: Date;
	colaboradores: Array<number>;
	colaboradoresObj: Array<{ label: string; value: number }>;
	nomesColaboradores: Array<string>;
}

@Injectable({
	providedIn: BiSharedModule,
})
export class BiCommonService {
	private _updatedGlobalFilter: BehaviorSubject<GlobalFilter> =
		new BehaviorSubject<GlobalFilter>(null);
	private _reloadAllBI: Subject<boolean> = new Subject<boolean>();

	constructor(
		private admCoreApiNegociacaoService: AdmCoreApiNegociacaoService,
		private navigationService: LayoutNavigationService,
		private toastrService: ToastrService
	) {}

	openCliente(matricula) {
		if (!matricula) {
			this.toastrService.error("Matrícula do cliente não encontrada!");
			return;
		}
		this.admCoreApiNegociacaoService.recursoHabilitado("TELA_ALUNO").subscribe({
			next: async (response) => {
				let urlCliente: string;

				if (response) {
					urlCliente = this.navigationService.createNovaPlataformaUrl(
						PlataformModuleConfig.TREINO.idUppercase,
						"/pessoas/perfil-v2/" + matricula
					);
				} else {
					urlCliente = await this.urlLegado(matricula);
				}
				window.open(urlCliente, "_blank");
			},
			error: (err) => {
				console.log(err);
			},
		});
	}

	private async urlLegado(matricula: string) {
		const urlInfo = await (
			this.navigationService.redirectToModule(
				PlataformModuleConfig.ADM_LEGADO,
				{
					id: "admCliente",
					permitido: true,
					route: {
						queryParams: {
							urlRedirect: "uriCliente",
							matriculaCliente: matricula,
							openInNewTab: false,
						},
					},
				}
			) as Observable<{ url?: string; popupInfo?: any }>
		).toPromise();
		return urlInfo.url;
	}

	corDs3StatusCliente(situacaoCliente: string): string {
		switch (situacaoCliente) {
			case "AE":
				return "ae";
			case "AT":
				return "at";
			case "AV":
				return "av";
			case "NO":
				return "no";
			case "CR":
				return "cr";
			case "DE":
				return "con-de";
			case "CA":
				return "ca";
			case "VE":
				return "av";
			case "DI":
				return "di";
			case "IN":
				return "in";
			case "VI":
				return "vi";
			case "TP":
				return "tp";
			case "TV":
				return "tv";
			case "TR":
				return "tr";
			case "GY":
				return "gy";
			case "GD":
				return "disabled";
			case "PE":
				return "pe";
			case "DEP":
				return "dp";
			default:
				return "disabled";
		}
	}

	corDs3StatusContrato(situacaoContrato: string): string {
		switch (situacaoContrato) {
			case "AE":
				return "ae";
			case "AT":
				return "at";
			case "AV":
				return "av";
			case "NO":
				return "no";
			case "CR":
				return "cr";
			case "DE":
				return "con-de";
			case "CA":
				return "ca";
			case "VE":
				return "av";
			case "DI":
				return "di";
			case "IN":
				return "in";
			case "VI":
				return "vi";
			case "TP":
				return "tp";
			case "TV":
				return "tv";
			case "TR":
				return "tr";
			case "GY":
				return "gy";
			case "GD":
				return "disabled";
			case "PE":
				return "pe";
			case "DEP":
				return "dp";
			default:
				return "disabled";
		}
	}

	get updatedGlobalFilter$() {
		return this._updatedGlobalFilter.asObservable();
	}

	set updatedGlobalFilter(globalValue: GlobalFilter) {
		this._updatedGlobalFilter.next(globalValue);
	}

	get reloadAllBI$() {
		return this._reloadAllBI.asObservable();
	}

	set reloadAllBI(reloadAllBI: boolean) {
		this._reloadAllBI.next(reloadAllBI);
	}

	getTickAmount(min: number, max: number, defaultStepSize: number = 2): number {
		const range = max - min;

		let stepSize = defaultStepSize; // Default

		if (range <= 1) {
			stepSize = 0.1;
		} else if (range < 4) {
			stepSize = 0.2;
		} else if (range < 7) {
			stepSize = 0.5;
		}

		return stepSize;
	}
}
