import { ComponentType } from "@angular/cdk/portal";
import {
	ComponentFactoryResolver,
	EventEmitter,
	Injectable,
	Injector,
	ViewContainerRef,
} from "@angular/core";
import { MatSidenav } from "@angular/material/sidenav";

@Injectable()
export class BiSidenavService {
	private sidenav!: MatSidenav;
	private viewContainerRef!: ViewContainerRef;

	constructor(
		private resolver: ComponentFactoryResolver,
		private injector: Injector
	) {}

	setSidenav(sidenav: MatSidenav, viewContainerRef: ViewContainerRef) {
		this.sidenav = sidenav;
		this.viewContainerRef = viewContainerRef;
	}

	open<T = unknown, D = any>(component: ComponentType<T>, data?: D) {
		if (!this.sidenav || !this.viewContainerRef) {
			console.error("Sidenav is not initialized");
			return;
		}

		const biSidenavRef = new BiSidenavRef<T>(
			this.sidenav,
			this.viewContainerRef,
			this.resolver,
			this.injector,
			component,
			data
		);
		biSidenavRef.open();
		return biSidenavRef;
	}
}

export class BiSidenavRef<T> {
	private _afterClosed: EventEmitter<any> = new EventEmitter();
	private sidenav!: MatSidenav;
	private viewContainerRef!: ViewContainerRef;
	private resolver: ComponentFactoryResolver;
	private injector: Injector;
	private component: ComponentType<T>;
	private data: any;

	get afterClosed() {
		return this._afterClosed;
	}

	constructor(
		sidenav: MatSidenav,
		viewContainerRef: ViewContainerRef,
		resolver: ComponentFactoryResolver,
		injector: Injector,
		component: ComponentType<T>,
		data?: any
	) {
		this.sidenav = sidenav;
		this.viewContainerRef = viewContainerRef;
		this.resolver = resolver;
		this.injector = injector;
		this.component = component;
		this.data = data;
	}

	open() {
		if (!this.sidenav || !this.viewContainerRef) {
			console.error("Sidenav is not initialized");
			return;
		}

		this.viewContainerRef.clear();
		const factory = this.resolver.resolveComponentFactory(this.component);
		const componentRef = this.viewContainerRef.createComponent(
			factory,
			0,
			Injector.create({
				providers: [{ provide: BiSidenavRef, useValue: this }],
				parent: this.injector,
			})
		);

		if (this.data) {
			Object.assign(componentRef.instance, this.data);
		}

		this.sidenav.open();
	}

	close(data?: any) {
		this.viewContainerRef.clear();
		this.sidenav.close();
		this._afterClosed.emit(data);
	}
}
