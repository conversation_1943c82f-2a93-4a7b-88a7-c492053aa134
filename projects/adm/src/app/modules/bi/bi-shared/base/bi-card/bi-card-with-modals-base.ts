import { ListItem } from "@adm/modules/bi/bi-adm/bi-adm.model";
import { BiCommonService } from "@adm/modules/bi/bi-shared/services/bi-common/bi-common.service";
import { BreakpointObserver } from "@angular/cdk/layout";
import { ComponentType } from "@angular/cdk/portal";
import { CurrencyPipe } from "@angular/common";
import { ChangeDetectorRef, Input, ViewChild } from "@angular/core";
import { FormGroup } from "@angular/forms";
import { MatDialog, MatDialogConfig, MatDialogRef } from "@angular/material";
import { Router } from "@angular/router";
import { ConfiguracaoBi } from "bi-ms-api";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { InfoData, TraducoesXinglingComponent } from "ui-kit";

interface BiCardWithModalsBaseOptions {
	dataInicioPrimeiroDiaMes?: boolean;
	dataFimUltimoDiaMes?: boolean;
}

export abstract class BiCardWithModalsBase {
	private _dialogMediaQuery = "(max-width: 720px)";
	private _actualDialogConfig: MatDialogConfig;
	private _dialogRef: MatDialogRef<any, any>;
	private _isInitialized: boolean;
	isAllEmpty: boolean;

	@Input() withFakeData: boolean = false;
	@Input() globalFilterForm: FormGroup;
	@Input() configBi: Array<ConfiguracaoBi>;
	@Input() reloadFull: boolean;

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	filtros: any = {};
	private _destroy$: Subject<void> = new Subject();

	destroy() {
		this._destroy$.next();
	}

	get destroy$() {
		return this._destroy$;
	}

	constructor(
		protected breakpointObserver: BreakpointObserver,
		protected cd: ChangeDetectorRef,
		protected currencyPipe: CurrencyPipe,
		protected dialog: MatDialog,
		protected router: Router,
		protected biCommonService: BiCommonService
	) {}

	protected init(options?: BiCardWithModalsBaseOptions) {
		this._isInitialized = true;
		this.populateFiltersByFilterForm(options);
		this.breakpointObserverListen();
	}

	private _modalTitleItem(item: ListItem): string {
		let title = item.text;

		if (item.monetary) {
			title += ` - ${this.currencyPipe.transform(item.value, "BRL")}`;
			return title;
		}

		if (item.percentage) {
			title += `${item.value}%`;
			return title;
		}

		return `${title} - ${item.value}`;
	}

	openModalItem(item: ListItem) {
		if (!item.modalConfig) {
			return;
		}

		if (!item.modalConfig.componentType) {
			return;
		}

		if (!item.modalConfig.config) {
			item.modalConfig.config = {};
		}
		const modalConfig = item.modalConfig.config;

		if (!modalConfig.data) {
			modalConfig.data = {};
		}

		if (!modalConfig.data.filters) {
			modalConfig.data.filters = {};
		}

		modalConfig.data.modalTitle = this._modalTitleItem(item);
		modalConfig.data.filters.inicio = this.filtros.dataInicio;
		modalConfig.data.filters.fim = this.filtros.data;
		modalConfig.data.filters.dataAlteracaoInicial = this.filtros.dataInicio;
		modalConfig.data.filters.dataAlteracaoFinal = this.filtros.data;
		modalConfig.data.filters.empresa = this.filtros.empresa;
		modalConfig.data.filters.codigoEmpresa = this.filtros.empresa;
		modalConfig.data.filters.nomeEmpresa = this.filtros.empresaNome;
		modalConfig.data.filters.colaboradores = this.filtros.colaboradores;
		modalConfig.data.filters.quickSearchValue = "";
		modalConfig.autoFocus = false;

		if (!modalConfig.width) {
			modalConfig.width = "1000px";
		}

		if (!modalConfig.maxWidth) {
			modalConfig.maxWidth = "100%";
		}

		this._openModal(item.modalConfig.componentType, modalConfig);
	}

	openModalInfo(item: { partClicked: string; infoData: InfoData }) {
		if (!item) {
			return;
		}
		if (!item.infoData) {
			return;
		}
		const infoData = item.infoData;

		if (infoData.overline) {
			if (infoData.overline.link) {
				this.router.navigate([infoData.overline.link]);
				return;
			}
		}

		if (!infoData.modalConfig) {
			return;
		}

		if (!infoData.modalConfig.componentType) {
			return;
		}

		if (!infoData.modalConfig.config) {
			infoData.modalConfig.config = {};
		}
		let modalConfig = infoData.modalConfig.config;

		if (item.partClicked === "middleLegend") {
			if (infoData.middleLegend) {
				if (!infoData.middleLegend.modalConfig) {
					return;
				}
				modalConfig = infoData.middleLegend.modalConfig.config;
			}
		}

		if (!modalConfig.data) {
			modalConfig.data = {};
		}

		if (!modalConfig.data.filters) {
			modalConfig.data.filters = {};
		}

		if (!modalConfig.data.modalTitle) {
			modalConfig.data.modalTitle = infoData.overline.text;
		}

		modalConfig.data.filters.inicio = this.filtros.dataInicio;
		modalConfig.data.filters.fim = this.filtros.data;
		modalConfig.data.filters.dataAlteracaoInicial = this.filtros.dataInicio;
		modalConfig.data.filters.dataAlteracaoFinal = this.filtros.data;
		modalConfig.data.filters.empresa = this.filtros.empresa;
		modalConfig.data.filters.codigoEmpresa = this.filtros.empresa;
		modalConfig.data.filters.nomeEmpresa = this.filtros.empresaNome;
		modalConfig.data.filters.colaboradores = this.filtros.colaboradores;
		modalConfig.data.filters.quickSearchValue = "";
		modalConfig.autoFocus = false;

		if (!modalConfig.width) {
			modalConfig.width = "1000px";
		}

		if (!modalConfig.maxWidth) {
			modalConfig.maxWidth = "100%";
		}

		let componentType = infoData.modalConfig.componentType;

		if (item.partClicked === "middleLegend") {
			if (infoData.middleLegend && infoData.middleLegend.modalConfig) {
				componentType = infoData.middleLegend.modalConfig.componentType;
			}
		}

		if (componentType) {
			this._openModal(componentType, modalConfig);
		}
	}

	private _openModal(
		componentRef: ComponentType<any>,
		config?: MatDialogConfig
	) {
		this._dialogRef = this.dialog.open(componentRef, {
			...config,
			maxWidth: "100%",
		});
		this._actualDialogConfig = config;

		this._updateDialogSize(
			this.breakpointObserver.isMatched(this._dialogMediaQuery)
		);
	}

	private breakpointObserverListen() {
		this.breakpointObserver
			.observe(this._dialogMediaQuery)
			.pipe(takeUntil(this.destroy$))
			.subscribe((result) => {
				this._updateDialogSize(result.matches);
			});
	}

	private _updateDialogSize(matches: boolean) {
		if (this._dialogRef) {
			if (matches) {
				this._dialogRef.updateSize("100%", "fit-content");
				this._dialogRef.updatePosition();
			} else {
				this._dialogRef.updateSize(
					this._actualDialogConfig.width,
					this._actualDialogConfig.height
				);
			}
			this.cd.detectChanges();
		}
	}

	private populateFiltersByFilterForm(options?: BiCardWithModalsBaseOptions) {
		if (this.globalFilterForm) {
			this.populateFiltersByInitialValues(undefined, options);
		}
		if (this.biCommonService) {
			this.biCommonService.updatedGlobalFilter$
				.pipe(takeUntil(this.destroy$))
				.subscribe((v) => {
					if (this._isInitialized && v) {
						this.populateFiltersByInitialValues(v, options);
					}
				});
		}
	}

	protected listenReloadAllBIs() {
		if (!this.biCommonService) {
			return;
		}
		this.biCommonService.reloadAllBI$
			.pipe(takeUntil(this.destroy$))
			.subscribe((v) => {
				if (v) {
					this.afterReloadAllBIs();
					this.loadData(true);
				}
			});
	}

	protected populateFiltersByInitialValues(
		v?: any,
		options?: BiCardWithModalsBaseOptions
	) {
		const initialValue = v || this.globalFilterForm.value;
		this.filtros.data = new Date();
		if (initialValue.dataGeral) {
			const dataGeral = new Date(initialValue.dataGeral);
			const dataInicio = new Date(dataGeral);

			if (
				!options ||
				(options &&
					(options.dataInicioPrimeiroDiaMes === undefined ||
						options.dataInicioPrimeiroDiaMes))
			) {
				dataInicio.setDate(1);
				dataInicio.setHours(0, 0, 0, 0);
			}
			if (options && options.dataFimUltimoDiaMes) {
				dataGeral.setMonth(dataGeral.getMonth() + 1);
				dataGeral.setDate(0);
			}
			this.filtros.dataInicio = dataInicio;
			this.filtros.data = dataGeral;
		}
		this.filtros.empresa = initialValue.empresa;
		this.filtros.empresaNome = initialValue.empresaNome || "";
		this.filtros.colaboradores = initialValue.colaboradores || [];
		this.filtros.colaboradoresObj = initialValue.colaboradoresObj || [];
		this.filtros.colaboradoresNomes = initialValue.nomesColaboradores || [];
	}

	abstract loadData(reloadFull?: boolean): void;

	afterReloadAllBIs() {}
}
