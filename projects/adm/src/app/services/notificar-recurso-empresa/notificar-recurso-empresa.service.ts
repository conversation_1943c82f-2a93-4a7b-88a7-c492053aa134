import { Injectable } from "@angular/core";
import { NotificacaoApiService, RecursoEmpresaMenu } from "notificacao-api";
import { PlataformaModulo, SessionService } from "sdk";

@Injectable({
	providedIn: "root",
})
export class NotificarRecursoEmpresaService {
	constructor(
		private notificacaoApiService: NotificacaoApiService,
		private sessionService: SessionService
	) {}

	notifyRecursoEmpresaNavigation(nomeRecurso: string) {
		const recursoEmpresaMenu: RecursoEmpresaMenu = {
			identificadorMenu: nomeRecurso,
			codigoEmpresa: Number(this.sessionService.empresaId),
			chave: this.sessionService.chave,
			nomeUsuario: this.sessionService.loggedUser.nome,
			nomeEmpresa: this.sessionService.currentEmpresa.nome,
			modulo: PlataformaModulo.ZW,
		};
		return this.notificacaoApiService.notifyNavigation(recursoEmpresaMenu);
	}
}
