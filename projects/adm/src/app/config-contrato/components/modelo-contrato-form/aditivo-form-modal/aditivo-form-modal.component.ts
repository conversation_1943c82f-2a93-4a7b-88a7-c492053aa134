import { Component, Input, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Aditivo, CadastroAuxApiAditivoService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import Quill from "quill";
import BlotFormatter from "quill-blot-formatter/dist/BlotFormatter";

Quill.register("modules/blotFormatter", BlotFormatter);
Quill.import("attributors/style/size");

@Component({
	selector: "adm-aditivo-form-modal",
	templateUrl: "./aditivo-form-modal.component.html",
	styleUrls: ["./aditivo-form-modal.component.scss"],
})
export class AditivoFormModalComponent implements OnInit {
	@Input()
	public aditivo: Aditivo;

	@Input()
	public isLog = false;

	@Input()
	public idModeloContrato: number;

	loading = false;

	modules = {};
	fontSizeArr = [
		"8px",
		"9px",
		"10px",
		"11px",
		"12px",
		"14px",
		"16px",
		"18px",
		"20px",
		"24px",
		"32px",
		"36px",
		"42px",
		"54px",
		"68px",
		"84px",
		"98px",
	];
	Size = Quill.import("attributors/style/size");

	constructor(
		private activeModal: NgbActiveModal,
		private notify: SnotifyService,
		private aditivoService: CadastroAuxApiAditivoService
	) {
		this.modules = {
			blotFormatter: {},
			toolbar: {
				container: [
					["bold", "italic", "underline"],
					[{ list: "ordered" }, { list: "bullet" }],
					[{ script: "sub" }, { script: "super" }],
					[{ indent: "-1" }, { indent: "+1" }],
					[{ size: this.fontSizeArr }],
					[{ header: [1, 2, 3, 4, 5, 6, false] }],
					[{ color: [] }, { background: [] }],
					[{ align: [] }],
					["clean"],
					["link", "image"],
				],
			},
		};
	}

	formGroup: FormGroup = new FormGroup({
		codigo: new FormControl(null),
		nome: new FormControl(null, [Validators.required]),
		descricao: new FormControl(null, [Validators.required]),
		responsavel: new FormControl(null),
		dataCriacao: new FormControl(null),
		dataProcessamento: new FormControl(null),
	});

	ngOnInit() {
		this.loadSettingsQill();

		if (this.aditivo) {
			this.formGroup.controls["codigo"].setValue(this.aditivo.codigo);
			this.formGroup.controls["nome"].setValue(this.aditivo.nome);
			this.formGroup.controls["descricao"].setValue(this.aditivo.descricao);

			if (this.isLog) {
				this.formGroup.disable();

				if (this.aditivo.responsavel != null) {
					this.formGroup.controls["responsavel"].setValue(
						this.aditivo.responsavel.username
					);
				}
				this.formGroup.controls["dataCriacao"].setValue(
					this.aditivo.dataCriacao
				);
				this.formGroup.controls["dataProcessamento"].setValue(
					this.aditivo.dataProcessamento
				);
			}
		}
	}

	loadSettingsQill() {
		this.Size.whitelist = this.fontSizeArr;
		Quill.register(this.Size, true);

		const Block = Quill.import("blots/block");
		Block.tagName = "DIV";
		Quill.register(Block, true);
	}

	salvar() {
		if (!this.formGroup.valid) {
			this.notify.warning("Existem campos não preenchidos!");
			return;
		}

		const data: Aditivo = {
			...this.formGroup.getRawValue(),
			idModeloContrato: Number(this.idModeloContrato),
		};

		this.aditivoService.save(data).subscribe({
			next: () => {
				if (data.codigo) {
					this.notify.success("Aditivo alterado com sucesso!");
				} else {
					this.notify.success("Aditivo cadastrado com sucesso!");
				}

				this.activeModal.close();
			},
			error: (error) => {
				this.notify.error("Erro ao salvar aditivo. Tente novamente.");
				console.error("Erro ao salvar aditivo:", error);
			},
		});
	}

	cancelar() {
		this.activeModal.close();
	}

	getEditorTextoStyles() {
		return this.isLog ? { "margin-bottom": "15px" } : {};
	}
}
