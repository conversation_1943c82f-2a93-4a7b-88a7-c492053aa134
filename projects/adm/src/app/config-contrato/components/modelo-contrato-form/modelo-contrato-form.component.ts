import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import {
	CadastroAuxApiAditivoService,
	CadastroAuxApiModeloContratoService,
	marcadoresCliente,
	marcadoresContrato,
	marcadoresEmpresa,
	marcadoresItensVenda,
	marcadoresModalidade,
	marcadoresMovParcela,
	marcadoresPacote,
	marcadoresPacoteVenda,
	marcadoresPlano,
	marcadoresTurma,
	marcadoresUsuario,
	marcadoresVenda,
	ModeloContrato,
} from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";

import { AdmRestService } from "@adm/adm-rest.service";
import { AditivoFormModalComponent } from "@adm/config-contrato/components/modelo-contrato-form/aditivo-form-modal/aditivo-form-modal.component";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import Quill from "quill";
import BlotFormatter from "quill-blot-formatter/dist/BlotFormatter";
import { Api, PerfilAcessoRecurso, SessionService } from "sdk";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { PerfilAcessoRecursoNome } from "../../../perfil-acesso/perfil-acesso-recurso.model";
import moment from "moment";

Quill.register("modules/blotFormatter", BlotFormatter);
Quill.import("attributors/style/size");

@Component({
	selector: "adm-modelo-contrato-form",
	templateUrl: "./modelo-contrato-form.component.html",
	styleUrls: ["./modelo-contrato-form.component.scss"],
})
export class ModeloContratoFormComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("columnNome", { static: true }) columnNome: TemplateRef<any>;
	@ViewChild("columnDataCriacao", { static: true })
	columnDataCriacao: TemplateRef<any>;
	@ViewChild("columnDataProcessamento", { static: true })
	columnDataProcessamento: TemplateRef<any>;
	@ViewChild("columnAcoes", { static: true }) columnAcoes: TemplateRef<any>;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("aplicarContratosCol", { static: true })
	aplicarContratosCol: TemplateRef<any>;
	@ViewChild("tableAditivos", { static: false }) tableData: RelatorioComponent;
	codigoControl: FormControl = new FormControl();
	formGroup: FormGroup = new FormGroup({
		codigo: new FormControl(),
		descricao: new FormControl(null, Validators.required),
		tipoContrato: new FormControl(null, Validators.required),
		situacao: new FormControl(null, Validators.required),
		dataDefinicao: new FormControl(null, Validators.required),
		responsavel: new FormControl(),
		texto: new FormControl(""),
	});

	table: PactoDataGridConfig;

	buscaRapidaFC = new FormControl();
	marcadoresFC = new FormControl();
	selecionarTodosFC = new FormControl();

	modeloContrato: ModeloContrato = new ModeloContrato();

	tipos: Array<any>;
	situacao: Array<any>;
	marcadores: Array<any>;
	id;
	textoAcaoInforme: string;

	marcadorApresentado: any = null;
	marcadorSelecionado: any = null;
	marcadoresCliente = marcadoresCliente;
	marcadoresContrato = marcadoresContrato;
	marcadoresPlano = marcadoresPlano;
	marcadoresModalidade = marcadoresModalidade;
	marcadoresTurma = marcadoresTurma;
	marcadoresMovParcela = marcadoresMovParcela;
	marcadoresPacote = marcadoresPacote;
	marcadoresUsuario = marcadoresUsuario;
	marcadoresEmpresa = marcadoresEmpresa;
	marcadoresVenda = marcadoresVenda;
	marcadoresItensVenda = marcadoresItensVenda;
	marcadoresPacoteVenda = marcadoresPacoteVenda;
	listaMarcadoresComBackground: any = [];
	isTermoResponsabilidade: boolean;
	permitirReplicarModeloContrato: boolean;
	title: string;

	showMarcadores = false;
	permissaoModeloContrato: PerfilAcessoRecurso;

	modules = {};
	fontSizeArr = [
		"8px",
		"9px",
		"10px",
		"11px",
		"12px",
		"14px",
		"16px",
		"18px",
		"20px",
		"24px",
		"32px",
		"36px",
		"42px",
		"54px",
		"68px",
		"84px",
		"98px",
	];
	Size = Quill.import("attributors/style/size");

	constructor(
		private route: Router,
		private activatedRoute: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private notificationService: SnotifyService,
		private modeloContratoService: CadastroAuxApiModeloContratoService,
		private sessionService: SessionService,
		private admRest: AdmRestService,
		private modalService: ModalService,
		private aditivoService: CadastroAuxApiAditivoService,
		private notify: SnotifyService
	) {
		this.permissaoModeloContrato = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.CONTRATO
		);

		this.modules = {
			blotFormatter: {},
			toolbar: {
				container: [
					["bold", "italic", "underline"],
					[{ list: "ordered" }, { list: "bullet" }],
					[{ script: "sub" }, { script: "super" }],
					[{ indent: "-1" }, { indent: "+1" }],
					[{ size: this.fontSizeArr }],
					[{ header: [1, 2, 3, 4, 5, 6, false] }],
					[{ color: [] }, { background: [] }],
					[{ align: [] }],
					["clean"],
					["link", "image"],
				],
			},
		};
	}

	get valid() {
		return this.formGroup.valid;
	}

	ngOnInit() {
		this.initConfiguracaoSistema();
		this.loadAcao();
		this.initArrayTipos();
		this.initArraySituacao();
		this.initArrayMarcadores("todos");
		this.initAditivosTable();

		this.Size.whitelist = this.fontSizeArr;
		Quill.register(this.Size, true);

		const Block = Quill.import("blots/block");
		Block.tagName = "DIV";
		Quill.register(Block, true);

		this.formGroup.get("codigo").disable();
		this.formGroup.get("responsavel").disable();
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		if (
			this.formGroup.get("situacao").value === null ||
			this.formGroup.get("situacao").value === undefined
		) {
			this.formGroup.get("situacao").setValue("AT");
		}
	}

	initConfiguracaoSistema() {
		if (
			this.activatedRoute.snapshot.url.some(
				(item) => item.path === "novo-modelo-contrato"
			)
		) {
			this.permitirReplicarModeloContrato = false;
			this.title = "Novo Modelo Contrato";
		} else {
			this.title = "Editar Modelo Contrato";
			this.modeloContratoService.configuracaoSistemaModeloContrato().subscribe({
				next: (response) => {
					const { permitirReplicarModeloContratoRedeEmpresa } =
						response.content || false;
					this.permitirReplicarModeloContrato =
						permitirReplicarModeloContratoRedeEmpresa;
				},
				error: (err) => {
					console.error(
						"Erro ao pesquisar o parâmetro da replicação do modelo de contrato:",
						err
					);
				},
			});
		}
	}

	ngAfterViewInit() {
		this.marcadoresCliente = marcadoresCliente.map(
			({ tag, nome, translationId }) => ({
				tag,
				nome,
				translationId,
				translation: this.traducao.getLabel(translationId),
			})
		);
		this.marcadoresContrato = marcadoresContrato.map(
			({ tag, nome, translationId }) => ({
				tag,
				nome,
				translationId,
				translation: this.traducao.getLabel(translationId),
			})
		);
		this.marcadoresPlano = marcadoresPlano.map(
			({ tag, nome, translationId }) => ({
				tag,
				nome,
				translationId,
				translation: this.traducao.getLabel(translationId),
			})
		);
		this.marcadoresModalidade = marcadoresModalidade.map(
			({ tag, nome, translationId }) => ({
				tag,
				nome,
				translationId,
				translation: this.traducao.getLabel(translationId),
			})
		);
		this.marcadoresTurma = marcadoresTurma.map(
			({ tag, nome, translationId }) => ({
				tag,
				nome,
				translationId,
				translation: this.traducao.getLabel(translationId),
			})
		);
		this.marcadoresMovParcela = marcadoresMovParcela.map(
			({ tag, nome, translationId }) => ({
				tag,
				nome,
				translationId,
				translation: this.traducao.getLabel(translationId),
			})
		);
		this.marcadoresPacote = marcadoresPacote.map(
			({ tag, nome, translationId }) => ({
				tag,
				nome,
				translationId,
				translation: this.traducao.getLabel(translationId),
			})
		);
		this.marcadoresUsuario = marcadoresUsuario.map(
			({ tag, nome, translationId }) => ({
				tag,
				nome,
				translationId,
				translation: this.traducao.getLabel(translationId),
			})
		);
		this.marcadoresEmpresa = marcadoresEmpresa.map(
			({ tag, nome, translationId }) => ({
				tag,
				nome,
				translationId,
				translation: this.traducao.getLabel(translationId),
			})
		);
		this.marcadoresVenda = marcadoresVenda.map(
			({ tag, nome, translationId }) => ({
				tag,
				nome,
				translationId,
				translation: this.traducao.getLabel(translationId),
			})
		);
		this.marcadoresItensVenda = marcadoresItensVenda.map(
			({ tag, nome, translationId }) => ({
				tag,
				nome,
				translationId,
				translation: this.traducao.getLabel(translationId),
			})
		);
		this.marcadoresPacoteVenda = marcadoresPacoteVenda.map(
			({ tag, nome, translationId }) => ({
				tag,
				nome,
				translationId,
				translation: this.traducao.getLabel(translationId),
			})
		);

		if (this.id > 0) {
			this.modeloContratoService.find(this.id).subscribe((response) => {
				if (
					response.content.texto &&
					response.content.texto !== undefined &&
					response.content.texto.includes("Untitled document")
				) {
					response.content.texto = response.content.texto.replace(
						"Untitled document",
						""
					);
				}
				this.modeloContrato = response.content;
				this.ajustarHtmlGeradoPelaTelaAntiga();
				this.atualizarEstiloTextoParaQuill();
				this.formGroup.patchValue({
					codigo: this.modeloContrato.codigo,
					descricao: this.modeloContrato.descricao,
					tipoContrato: this.modeloContrato.tipoContrato,
					situacao: this.modeloContrato.situacao,
					dataDefinicao: this.modeloContrato.dataDefinicao,
					responsavel: this.modeloContrato.responsavelDefinicao.nome,
					texto: this.modeloContrato.texto,
				});
				if (
					this.formGroup.get("tipoContrato").value === "PL" ||
					this.formGroup.get("tipoContrato").value === "SE" ||
					this.formGroup.get("tipoContrato").value === "CC" ||
					this.formGroup.get("tipoContrato").value === "TR"
				) {
					this.showMarcadores = true;
					this.initArrayMarcadores(
						this.formGroup.get("tipoContrato").value === "PL"
							? "todos"
							: this.formGroup.get("tipoContrato").value
					);
				}
				this.cd.detectChanges();
			});
		} else {
			this.modeloContrato.responsavelDefinicao = {
				codigo: this.sessionService.loggedUser.id,
				nome: this.sessionService.loggedUser.nome,
			};
			this.formGroup
				.get("responsavel")
				.setValue(this.sessionService.loggedUser.nome);
			this.cd.detectChanges();
		}
	}

	validarPermissoes(): boolean {
		let permitir = false;
		if (
			this.formGroup.get("codigo").value === undefined ||
			this.formGroup.get("codigo").value === null
		) {
			if (
				this.permissaoModeloContrato === undefined ||
				!this.permissaoModeloContrato.incluir
			) {
				this.notificationService.warning(
					this.traducao.getLabel("msg-usuario-sem-permissao-cadastrar")
				);
			} else {
				permitir = true;
			}
		} else if (this.formGroup.get("codigo").value) {
			if (
				this.permissaoModeloContrato === undefined ||
				!this.permissaoModeloContrato.editar
			) {
				this.notificationService.warning(
					this.traducao.getLabel("msg-usuario-sem-permissao-editar")
				);
			} else {
				permitir = true;
			}
		}
		return permitir;
	}

	loadAcao() {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		this.textoAcaoInforme = this.id !== null ? "editar" : "cadastrar";
	}

	initArrayTipos() {
		setTimeout(() => {
			console.log("Status -> ZW-UI Ok");
			this.modeloContratoService
				.isTermoResponsabilidade()
				.subscribe((isTermoResponsabilidade) => {
					const tipoArrayTranslate = [];
					tipoArrayTranslate.push({
						id: "PL",
						label: this.traducao.getLabel("PL"),
					});
					tipoArrayTranslate.push({
						id: "SE",
						label: this.traducao.getLabel("SE"),
					});
					tipoArrayTranslate.push({
						id: "AM",
						label: this.traducao.getLabel("AM"),
					});
					tipoArrayTranslate.push({
						id: "VO",
						label: this.traducao.getLabel("VO"),
					});
					tipoArrayTranslate.push({
						id: "LP",
						label: this.traducao.getLabel("LP"),
					});
					tipoArrayTranslate.push({
						id: "CC",
						label: this.traducao.getLabel("CC"),
					});
					console.log(
						"O termo de responsabilidade é: " + isTermoResponsabilidade.content
					);
					if (isTermoResponsabilidade.content) {
						tipoArrayTranslate.push({
							id: "TR",
							label: "Termo de Responsabilidade",
						});
					}
					this.tipos = tipoArrayTranslate;
					this.cd.detectChanges();
				});
		});
	}

	initArraySituacao() {
		setTimeout(() => {
			const situacaoArrayTranslate = [];
			situacaoArrayTranslate.push({
				id: "AT",
				label: this.traducao.getLabel("AT"),
			});
			situacaoArrayTranslate.push({
				id: "IN",
				label: this.traducao.getLabel("IN"),
			});
			this.situacao = situacaoArrayTranslate;
			this.cd.detectChanges();
		});
	}

	initArrayMarcadores(tipo) {
		switch (tipo) {
			case "todos": {
				setTimeout(() => {
					const marcadorArrayTranslate = [];
					marcadorArrayTranslate.push({
						id: null,
						label: "",
					});
					marcadorArrayTranslate.push({
						id: "0",
						label: this.traducao.getLabel("cliente"),
					});
					marcadorArrayTranslate.push({
						id: "1",
						label: this.traducao.getLabel("contrato"),
					});
					marcadorArrayTranslate.push({
						id: "2",
						label: this.traducao.getLabel("plano"),
					});
					marcadorArrayTranslate.push({
						id: "3",
						label: this.traducao.getLabel("modalidade"),
					});
					marcadorArrayTranslate.push({
						id: "4",
						label: this.traducao.getLabel("turma"),
					});
					marcadorArrayTranslate.push({
						id: "5",
						label: this.traducao.getLabel("mov-parcela"),
					});
					marcadorArrayTranslate.push({
						id: "6",
						label: this.traducao.getLabel("pacote"),
					});
					marcadorArrayTranslate.push({
						id: "7",
						label: this.traducao.getLabel("usuario"),
					});
					marcadorArrayTranslate.push({
						id: "8",
						label: this.traducao.getLabel("empresa"),
					});
					marcadorArrayTranslate.push({
						id: "9",
						label: this.traducao.getLabel("venda"),
					});
					marcadorArrayTranslate.push({
						id: "10",
						label: this.traducao.getLabel("itens-venda"),
					});
					marcadorArrayTranslate.push({
						id: "11",
						label: this.traducao.getLabel("pacote-venda"),
					});
					this.marcadores = marcadorArrayTranslate;
					this.cd.detectChanges();
				});
				this.showMarcadores = true;
				break;
			}
			case "SE": {
				setTimeout(() => {
					const marcadorArrayTranslate = [];
					marcadorArrayTranslate.push({
						id: null,
						label: "",
					});
					marcadorArrayTranslate.push({
						id: "0",
						label: this.traducao.getLabel("cliente"),
					});
					marcadorArrayTranslate.push({
						id: "5",
						label: this.traducao.getLabel("mov-parcela"),
					});
					marcadorArrayTranslate.push({
						id: "7",
						label: this.traducao.getLabel("usuario"),
					});
					marcadorArrayTranslate.push({
						id: "8",
						label: this.traducao.getLabel("empresa"),
					});
					marcadorArrayTranslate.push({
						id: "9",
						label: this.traducao.getLabel("venda"),
					});
					marcadorArrayTranslate.push({
						id: "10",
						label: this.traducao.getLabel("itens-venda"),
					});
					marcadorArrayTranslate.push({
						id: "10",
						label: this.traducao.getLabel("pacote-venda"),
					});
					this.marcadores = marcadorArrayTranslate;
					this.cd.detectChanges();
				});
				this.showMarcadores = true;
				break;
			}
			case "CC": {
				setTimeout(() => {
					const marcadorArrayTranslate = [];
					marcadorArrayTranslate.push({
						id: null,
						label: "",
					});
					marcadorArrayTranslate.push({
						id: "0",
						label: this.traducao.getLabel("cliente"),
					});
					marcadorArrayTranslate.push({
						id: "5",
						label: this.traducao.getLabel("mov-parcela"),
					});
					marcadorArrayTranslate.push({
						id: "7",
						label: this.traducao.getLabel("usuario"),
					});
					marcadorArrayTranslate.push({
						id: "8",
						label: this.traducao.getLabel("empresa"),
					});
					marcadorArrayTranslate.push({
						id: "9",
						label: this.traducao.getLabel("venda"),
					});
					marcadorArrayTranslate.push({
						id: "10",
						label: this.traducao.getLabel("itens-venda"),
					});
					this.marcadores = marcadorArrayTranslate;
					this.cd.detectChanges();
				});
				this.showMarcadores = true;
				break;
			}
			case "TR": {
				setTimeout(() => {
					const marcadorArrayTranslate = [];
					marcadorArrayTranslate.push({
						id: null,
						label: "",
					});
					marcadorArrayTranslate.push({
						id: "0",
						label: this.traducao.getLabel("cliente"),
					});
					marcadorArrayTranslate.push({
						id: "7",
						label: this.traducao.getLabel("usuario"),
					});
					marcadorArrayTranslate.push({
						id: "8",
						label: this.traducao.getLabel("empresa"),
					});
					this.marcadores = marcadorArrayTranslate;
					this.cd.detectChanges();
				});
				this.showMarcadores = true;
				break;
			}
			default: {
				setTimeout(() => {
					const marcadorArrayTranslate = [];
					this.marcadores = marcadorArrayTranslate;
					this.cd.detectChanges();
				});
				this.showMarcadores = false;
				break;
			}
		}
	}

	voltarParaListagem() {
		this.route.navigate(["adm", "config-contrato", "modelo-contrato"]);
	}

	imprimir() {
		this.modeloContrato.texto = this.formGroup.get("texto").value;
		this.prepararHtmlTexto();
		this.validarTexto();
		const popupWindow = window.open(
			"",
			"_blank",
			"top=0,left=0,height=100%,width=auto"
		);
		popupWindow.document.open();
		popupWindow.document.write(`
            <html>
                <head>
                    <title>${this.traducao.getLabel(
											"contrato-prestacao-servico"
										)}</title>
                    <style></style>
                </head>
                <body onload="window.print();window.close()">
                    ${this.modeloContrato.texto}
                </body>
            </html>`);
		popupWindow.document.close();
	}

	salvar() {
		if (this.validarPermissoes()) {
			if (this.valid) {
				Object.keys(this.formGroup.getRawValue()).forEach((key) => {
					this.modeloContrato[key] = this.formGroup.getRawValue()[key];
				});
				this.validarTexto();
				this.prepararHtmlTexto();

				this.modeloContratoService.findAll().subscribe((planos) => {
					if (this.modeloContrato.tipoContrato === "TR") {
						planos.content.forEach((plano) => {
							if (plano.tipoContrato === "TR") {
								this.modeloContrato.codigo = plano.codigo;
							}
						});
					}
					this.modeloContrato.replicar = false;
					this.modeloContratoService.save(this.modeloContrato).subscribe(
						(response) => {
							this.notificationService.success(
								this.traducao.getLabel("saved-success")
							);
							this.voltarParaListagem();
						},
						(httpErrorResponse) => {
							const err = httpErrorResponse.error;
							if (err.meta && err.meta.messageValue) {
								this.notificationService.error(err.meta.messageValue);
							}
						}
					);
				});
			} else {
				this.notificationService.warning(
					this.traducao.getLabel("values-required")
				);
			}
		}
	}

	validarTexto() {
		// validações de alinhamento
		if (this.modeloContrato.texto.includes('class="ql-align-center"')) {
			this.modeloContrato.texto = this.modeloContrato.texto
				.split('class="ql-align-center"')
				.join('style="text-align: center;"');
		}
		if (this.modeloContrato.texto.includes('class="ql-align-right"')) {
			this.modeloContrato.texto = this.modeloContrato.texto
				.split('class="ql-align-right"')
				.join('style="text-align: right;"');
		}
		if (this.modeloContrato.texto.includes('class="ql-align-justify"')) {
			this.modeloContrato.texto = this.modeloContrato.texto
				.split('class="ql-align-justify"')
				.join('style="text-align: justify;"');
		}

		// validações de identação
		if (this.modeloContrato.texto.includes('class="ql-indent-1"')) {
			this.modeloContrato.texto = this.modeloContrato.texto
				.split('class="ql-indent-1"')
				.join('style="padding-left: 30px;"');
		}
		if (this.modeloContrato.texto.includes('class="ql-indent-2"')) {
			this.modeloContrato.texto = this.modeloContrato.texto
				.split('class="ql-indent-2"')
				.join('style="padding-left: 60px;"');
		}
		if (this.modeloContrato.texto.includes('class="ql-indent-3"')) {
			this.modeloContrato.texto = this.modeloContrato.texto
				.split('class="ql-indent-3"')
				.join('style="padding-left: 90px;"');
		}
		if (this.modeloContrato.texto.includes('class="ql-indent-4"')) {
			this.modeloContrato.texto = this.modeloContrato.texto
				.split('class="ql-indent-4"')
				.join('style="padding-left: 120px;"');
		}
		if (this.modeloContrato.texto.includes('class="ql-indent-5"')) {
			this.modeloContrato.texto = this.modeloContrato.texto
				.split('class="ql-indent-5"')
				.join('style="padding-left: 150px;"');
		}
		if (this.modeloContrato.texto.includes('class="ql-indent-6"')) {
			this.modeloContrato.texto = this.modeloContrato.texto
				.split('class="ql-indent-6"')
				.join('style="padding-left: 180px;"');
		}
	}

	atualizarEstiloTextoParaQuill() {
		if (this.modeloContrato.texto && this.modeloContrato.texto !== undefined) {
			if (this.modeloContrato.texto.includes('style="padding-left: 30px;"')) {
				this.modeloContrato.texto = this.modeloContrato.texto
					.split('style="padding-left: 30px;"')
					.join('class="ql-indent-1"');
			}
			if (this.modeloContrato.texto.includes('style="padding-left: 60px;"')) {
				this.modeloContrato.texto = this.modeloContrato.texto
					.split('style="padding-left: 60px;"')
					.join('class="ql-indent-2"');
			}
			if (this.modeloContrato.texto.includes('style="padding-left: 90px;"')) {
				this.modeloContrato.texto = this.modeloContrato.texto
					.split('style="padding-left: 90px;"')
					.join('class="ql-indent-3"');
			}
			if (this.modeloContrato.texto.includes('style="padding-left: 120px;"')) {
				this.modeloContrato.texto = this.modeloContrato.texto
					.split('style="padding-left: 120px;"')
					.join('class="ql-indent-4"');
			}
			if (this.modeloContrato.texto.includes('style="padding-left: 150px;"')) {
				this.modeloContrato.texto = this.modeloContrato.texto
					.split('style="padding-left: 150px;"')
					.join('class="ql-indent-5"');
			}
			if (this.modeloContrato.texto.includes('style="padding-left: 180px;"')) {
				this.modeloContrato.texto = this.modeloContrato.texto
					.split('style="padding-left: 180px;"')
					.join('class="ql-indent-6"');
			}
		}
	}

	prepararHtmlTexto() {
		if (
			!this.modeloContrato.texto.includes(
				'<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">'
			)
		) {
			const padraoTexto =
				'<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">\n' +
				"<html>\n" +
				"<head>\n" +
				'<meta charset="UTF-8"/>\n' +
				"<title></title>\n" +
				"</head>\n" +
				"<body>\n" +
				this.modeloContrato.texto.replace(/(\r\n|\n|\r)/gm, "") +
				"\n" +
				"</body>\n" +
				"</html>";

			this.modeloContrato.texto = padraoTexto;
		}
	}

	selecionarMarcador(item) {
		switch (item) {
			case "0": {
				this.marcadorSelecionado = this.marcadorApresentado =
					this.marcadoresCliente;
				break;
			}
			case "1": {
				this.marcadorSelecionado = this.marcadorApresentado =
					this.marcadoresContrato;
				break;
			}
			case "2": {
				this.marcadorSelecionado = this.marcadorApresentado =
					this.marcadoresPlano;
				break;
			}
			case "3": {
				this.marcadorSelecionado = this.marcadorApresentado =
					this.marcadoresModalidade;
				break;
			}
			case "4": {
				this.marcadorSelecionado = this.marcadorApresentado =
					this.marcadoresTurma;
				break;
			}
			case "5": {
				this.marcadorSelecionado = this.marcadorApresentado =
					this.marcadoresMovParcela;
				break;
			}
			case "6": {
				this.marcadorSelecionado = this.marcadorApresentado =
					this.marcadoresPacote;
				break;
			}
			case "7": {
				this.marcadorSelecionado = this.marcadorApresentado =
					this.marcadoresUsuario;
				break;
			}
			case "8": {
				this.marcadorSelecionado = this.marcadorApresentado =
					this.marcadoresEmpresa;
				break;
			}
			case "9": {
				this.marcadorSelecionado = this.marcadorApresentado =
					this.marcadoresVenda;
				break;
			}
			case "10": {
				this.marcadorSelecionado = this.marcadorApresentado =
					this.marcadoresItensVenda;
				break;
			}
			case "11": {
				this.marcadorSelecionado = this.marcadorApresentado =
					this.marcadoresPacoteVenda;
				break;
			}
			default: {
				this.marcadorSelecionado = this.marcadorApresentado = [];
				break;
			}
		}
		this.atualizarListaMarcadoresComBackground();
		this.cd.detectChanges();
	}

	atualizarListaMarcadoresComBackground() {
		// função responsável por montar lista com marcadores que terão o background destacado;
		let proximaFlag = 3;
		this.listaMarcadoresComBackground = [];
		for (let i = 0; i < this.marcadorApresentado.length; i++) {
			if (i <= proximaFlag) {
				this.listaMarcadoresComBackground.push(i);
			} else if (i === proximaFlag + 4) {
				proximaFlag += 8;
			}
		}
	}

	addMarcador(tag) {
		let texto = this.formGroup.get("texto").value;
		console.log(texto, tag);
		texto = this.isNullOrUndefined(texto) ? " " + tag : texto + " " + tag;
		this.formGroup.get("texto").setValue(texto);
	}

	isNullOrUndefined(value: any) {
		return value === null || value === undefined;
	}

	getClassMarcador(index) {
		return this.listaMarcadoresComBackground.find((x) => index === x) !==
			undefined
			? "marcador background-fafafa"
			: "marcador";
	}

	novoAditivo() {
		const modal = this.modalService.open(
			"Aditivo contratual",
			AditivoFormModalComponent,
			PactoModalSize.LARGE
		);

		modal.componentInstance.aditivo = null;
		modal.componentInstance.idModeloContrato = this.id;

		modal.result.then(() => {
			this.tableData.reloadData();
		});
	}

	editarAditivo(aditivo) {
		const modal = this.modalService.open(
			"Aditivo contratual",
			AditivoFormModalComponent,
			PactoModalSize.LARGE
		);

		modal.componentInstance.aditivo = aditivo;
		modal.componentInstance.idModeloContrato = this.id;

		modal.result.then(() => {
			this.tableData.reloadData();
		});
	}

	apagarAditivo(aditivo) {
		if (aditivo) {
			this.aditivoService.delete(aditivo.codigo).subscribe(
				() => {
					this.notify.success("Aditivo excluído com sucesso!");
					this.tableData.reloadData();
				},
				() => {
					this.notify.error("Erro ao excluir aditivo");
				}
			);
		}
	}

	logAditivo(aditivo) {
		const modal = this.modalService.open(
			"Aditivo contratual",
			AditivoFormModalComponent,
			PactoModalSize.LARGE
		);

		modal.componentInstance.aditivo = aditivo;
		modal.componentInstance.idModeloContrato = this.id;
		modal.componentInstance.isLog = true;
	}

	aplicarAditivo(aditivo) {
		if (aditivo) {
			this.aditivoService.aplicarAditivo(aditivo.codigo).subscribe(
				() => {
					this.notify.success("Aditivo aplicado com sucesso!");
					this.tableData.reloadData();
				},
				() => {
					this.notify.error("Erro ao aplicar aditivo");
				}
			);
		}
	}

	private initAditivosTable() {
		this.id = this.activatedRoute.snapshot.paramMap.get("id");
		if (!this.id) {
			return;
		}

		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl(
					`/aditivo/buscar-por-modelo-contrato/${this.id}`,
					false,
					Api.MSCADAUX
				),
				quickSearch: false,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: false,
				columns: [
					{
						nome: "nome",
						titulo: this.columnNome,
						visible: true,
						ordenavel: false,
					},
					{
						nome: "dataCriacao",
						titulo: this.columnDataCriacao,
						visible: true,
						ordenavel: false,
						styleClass: "center",
						valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
						date: true,
					},
					{
						nome: "dataProcessamento",
						titulo: this.columnDataProcessamento,
						visible: true,
						ordenavel: false,
						styleClass: "center",
						valueTransform: (v) =>
							v != null ? moment(v).format("DD/MM/YYYY") : "-",
						date: true,
					},
					{
						nome: "aplicadoContratosLancados",
						titulo: "",
						visible: true,
						ordenavel: false,
						styleClass: "center",
						celula: this.aplicarContratosCol,
					},
				],
				actions: [
					{
						nome: "action-edit",
						iconClass: "pct pct-edit cor-action-default-able04",
						showIconFn: (v) => !v.aplicadoContratosLancados,
						actionFn: (v) => this.editarAditivo(v.row),
					},
					{
						nome: "action-delete",
						iconClass: "pct pct-trash-2 cor-hellboy-pri",
						showIconFn: (v) => !v.aplicadoContratosLancados,
						actionFn: (v) => this.apagarAditivo(v.row),
					},
					{
						nome: "action-log",
						iconClass: "pct pct-list cor-action-default-able04",
						showIconFn: (v) => v.aplicadoContratosLancados,
						actionFn: (v) => this.logAditivo(v.row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}

	atualizarMarcadoresPorTipo(tipo) {
		this.initArrayMarcadores(tipo === "PL" ? "todos" : tipo);
		if (tipo === "PL" || tipo === "SE" || tipo === "CC") {
			this.selecionarMarcador("0");
		}
	}

	buscaRapidaMarcadores(chave) {
		if (chave.length >= 2) {
			this.marcadorApresentado = this.marcadorSelecionado.filter((marcador) =>
				marcador.nome.toLowerCase().includes(chave.toLowerCase())
			);
		} else if (chave.length === 0) {
			this.marcadorApresentado = this.marcadorSelecionado;
		}
		this.cd.detectChanges();
	}

	copiarValorMarcador(value: string) {
		if (navigator.clipboard) {
			navigator.clipboard
				.writeText(value)
				.then(() => {
					this.notificationService.success(
						this.traducao.getLabel("copiado-sucesso")
					);
				})
				.catch(() => {
					this.notificationService.error(
						this.traducao.getLabel("falha-ao-copiar")
					);
				});
		} else {
			this.notificationService.error(this.traducao.getLabel("falha-ao-copiar"));
		}
	}

	private ajustarHtmlGeradoPelaTelaAntiga() {
		if (this.modeloContrato.texto && this.modeloContrato.texto !== undefined) {
			// Substituir as nomeclaturas do font-size que eram utilizadas na tela antiga
			this.modeloContrato.texto = this.modeloContrato.texto.replace(
				new RegExp("font-size: xx-small", "g"),
				"font-size: 8px"
			);
			this.modeloContrato.texto = this.modeloContrato.texto.replace(
				new RegExp("font-size: x-small", "g"),
				"font-size: 10px"
			);
			this.modeloContrato.texto = this.modeloContrato.texto.replace(
				new RegExp("font-size: small", "g"),
				"font-size: 12px"
			);
			this.modeloContrato.texto = this.modeloContrato.texto.replace(
				new RegExp("font-size: medium", "g"),
				"14px"
			);
			this.modeloContrato.texto = this.modeloContrato.texto.replace(
				new RegExp("font-size: large", "g"),
				"font-size: 18px"
			);
			this.modeloContrato.texto = this.modeloContrato.texto.replace(
				new RegExp("font-size: x-large", "g"),
				"font-size: 24px"
			);
			this.modeloContrato.texto = this.modeloContrato.texto.replace(
				new RegExp("font-size: xx-large", "g"),
				"font-size: 36px"
			);

			// Substituir font-size:10.0pt por font-size: 10px;
			for (let i = 8; i <= 36; i++) {
				for (let j = 0; j < 10; j++) {
					this.modeloContrato.texto = this.modeloContrato.texto.replace(
						new RegExp(`font-size:${i}.${j}pt;`, "g"),
						`font-size: ${i}.${j}px;`
					);
					this.modeloContrato.texto = this.modeloContrato.texto.replace(
						new RegExp(`font-size: ${i}.${j}pt;`, "g"),
						`font-size: ${i}.${j}px;`
					);
				}
			}

			// Adicionar um font size padrão nas tags que não tem font size
			this.adicionarFontSize("span", "11px");
			this.adicionarFontSize("strong", "11px");

			this.modeloContrato.texto = this.modeloContrato.texto.replace(
				new RegExp("<p", "g"),
				"<div><br></div> <div"
			);
			this.modeloContrato.texto = this.modeloContrato.texto.replace(
				new RegExp("</p>", "g"),
				"</div>"
			);
		}
	}

	private adicionarFontSize(tagName: string, value: string) {
		const tagsHtml = new Array<any>();
		let count = 0;
		this.modeloContrato.texto.split(`<${tagName}`).forEach((t) => {
			if (count > 0) {
				const index = t.indexOf(">");
				if (index >= 0) {
					let tagHtml = `<${tagName}`;
					tagHtml += t.substring(0, index + 1);
					if (tagHtml.includes("style") && !tagHtml.includes("font-size:")) {
						const indexStyle = tagHtml.indexOf("style=");
						const tagAjustada =
							tagHtml.substring(0, indexStyle + 7) +
							`font-size: ${value};` +
							tagHtml.substring(indexStyle + 7);
						tagsHtml.push({ tag: tagHtml, tagAjustada });
					} else if (!tagHtml.includes("font-size")) {
						const tagAjustada =
							tagHtml.substring(0, tagName.length + 1) +
							` style="font-size: ${value};" ` +
							tagHtml.substring(tagName.length + 1);
						tagsHtml.push({ tag: tagHtml, tagAjustada });
					}
				}
			}
			count++;
		});

		tagsHtml.forEach((v) => {
			if (v.tag !== undefined && v.tagAjustada !== undefined) {
				this.modeloContrato.texto = this.modeloContrato.texto.replace(
					new RegExp(v.tag, "g"),
					v.tagAjustada
				);
			}
		});
	}
}
