import {
	AfterViewInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	CatTableEditableComponent,
	PactoDataGridConfig,
	TableData,
	TraducoesXinglingComponent,
} from "ui-kit";
import {
	ConvenioDesconto,
	PlanoApiConvenioDescontoPlanoConfiguracaoService,
} from "plano-api";
import { AdmRestService } from "@adm/adm-rest.service";
import { DecimalPipe } from "@angular/common";
import { SnotifyService } from "ng-snotify";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "adm-table-convenio-desconto-plano-config",
	templateUrl: "./table-convenio-desconto-plano-config.component.html",
	styleUrls: ["./table-convenio-desconto-plano-config.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableConvenioDescontoPlanoConfigComponent
	implements OnInit, AfterViewInit
{
	tableConvenioDescontoPlanoConfiguracao: PactoDataGridConfig;
	configuracoesDesconto: Array<any> = new Array<any>();

	@Input() convenioDescontoDTO: ConvenioDesconto;
	@Input() idConvenioDesconto: number;
	@Output() afterEditAdd: EventEmitter<any> = new EventEmitter<any>();
	@Output() isEditinOrAdding: EventEmitter<boolean> =
		new EventEmitter<boolean>();
	@ViewChild("columnPlano", { static: true })
	columnPlano: TemplateRef<any>;
	@ViewChild("columnDuracao", { static: true }) columnDuracao: TemplateRef<any>;
	@ViewChild("columnTipoDesconto", { static: true })
	columnTipoDesconto: TemplateRef<any>;
	columnDesconto: TemplateRef<any>;
	@ViewChild("columnValorDesconto", { static: true })
	columnValorDesconto: TemplateRef<any>;
	@ViewChild("columnPorcentagemDesconto", { static: true })
	columnPorcentagemDesconto: TemplateRef<any>;
	@ViewChild("tableConvenioDescontoComponent", { static: true })
	tableConvenioDescontoComponent: CatTableEditableComponent;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;

	tiposDesconto: any[];
	planos: any[];

	constructor(
		private admRest: AdmRestService,
		private cd: ChangeDetectorRef,
		private decimalPipe: DecimalPipe,
		private convenioDescontoConfiguracaoService: PlanoApiConvenioDescontoPlanoConfiguracaoService,
		private notificationService: SnotifyService
	) {}
	ngOnInit() {
		this.cd.detectChanges();
		this.convenioDescontoConfiguracaoService
			.getConfiguracaoByConvenioDescontoPlano(this.idConvenioDesconto)
			.subscribe((response) => {
				this.convenioDescontoDTO.convenioDescontoPlanoConfiguracaoList =
					response.content;
				this.convenioDescontoDTO.convenioDescontoPlanoConfiguracaoList.forEach(
					(control) => {
						control.tipoDesconto = this.tiposDesconto.find(
							(v) => v.codigo === control.tipoDesconto
						);
					}
				);
				this.configuracoesDesconto =
					this.convenioDescontoDTO.convenioDescontoPlanoConfiguracaoList;
				this.tableConvenioDescontoComponent.reloadData();
			});
	}

	ngAfterViewInit() {
		this.initPlanos();
		this.initTiposDesconto();
		this.initTableConvenioDescontoPlanoConfiguracao();
		this.cd.detectChanges();
	}

	initTableConvenioDescontoPlanoConfiguracao() {
		this.tableConvenioDescontoPlanoConfiguracao = new PactoDataGridConfig({
			dataAdapterFn: (serverData) => {
				return {
					content: this.configuracoesDesconto,
				};
			},
			pagination: false,
			formGroup: new FormGroup({
				plano: new FormControl(),
				duracao: new FormControl(),
				tipoDesconto: new FormControl(),
				valorDesconto: new FormControl(),
				porcentagemDesconto: new FormControl(""),
			}),
			beforeConfirm: (row, form, data, rowIndex) =>
				this.beforeConfirm(row, form, data, rowIndex),
			columns: [
				{
					nome: "plano",
					titulo: this.columnPlano,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					objectAttrLabelName: "label",
					idSelectKey: "codigo",
					labelSelectKey: "label",
					showAddSelectBtn: false,
					showSelectFilter: false,
					inputSelectData: this.planos,
					width: "250px",
				},
				{
					nome: "duracao",
					titulo: this.columnDuracao,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "number",
					minValue: 0,
					errorMessage: this.traducoes.getLabel("error-valor-obrigatorio"),
					width: "200px",
				},
				{
					nome: "tipoDesconto",
					titulo: this.columnTipoDesconto,
					visible: true,
					ordenavel: false,
					editable: true,
					inputType: "select",
					objectAttrLabelName: "label",
					idSelectKey: "codigo",
					labelSelectKey: "label",
					showAddSelectBtn: false,
					showSelectFilter: false,
					inputSelectData: this.tiposDesconto,
					width: "250px",
					selectOptionChange: (option, form, row) => {
						if (option && option.codigo === "VE") {
							form.get("porcentagemDesconto").setValue(0);
							form.get("valorDesconto").setValue(0);
							form.get("porcentagemDesconto").disable();
							form.get("valorDesconto").enable();
						} else {
							form.get("valorDesconto").setValue(0);
							form.get("porcentagemDesconto").setValue(0);
							form.get("porcentagemDesconto").enable();
							form.get("valorDesconto").disable();
						}
					},
				},
				{
					nome: "valorDesconto",
					titulo: this.columnValorDesconto,
					visible: true,
					valueTransform: (v) => {
						return this.decimalPipe.transform(
							parseFloat(v.toString().replace(",", ".")),
							"1.2-2"
						);
					},
					ordenavel: false,
					editable: true,
					inputType: "decimal",
					decimal: true,
					errorMessage: this.traducoes.getLabel("error-valor-obrigatorio"),
					isDisabled: (row) =>
						!row.tipoDesconto || row.tipoDesconto.codigo === "PD",
					width: "200px",
				},
				{
					nome: "porcentagemDesconto",
					titulo: this.columnPorcentagemDesconto,
					visible: true,
					maxValue: 100,
					valueTransform: (v) => {
						return this.decimalPipe.transform(
							parseFloat(v.toString().replace(",", ".")),
							"1.2-2"
						);
					},
					ordenavel: false,
					editable: true,
					inputType: "decimal",
					decimal: true,
					errorMessage: this.traducoes.getLabel("error-valor-obrigatorio"),
					isDisabled: (row) =>
						!row.tipoDesconto || row.tipoDesconto.codigo === "VE",
					width: "200px",
				},
			],
		});
		this.cd.detectChanges();
	}

	beforeConfirm(row, form, data, rowIndex): boolean {
		if (form.get("duracao").value === null || form.get("duracao").value <= 0) {
			this.notificationService.error(
				this.traducoes.getLabel("error-duracao-invalida-table")
			);
			return false;
		}
		if (row.tipoDesconto || form.get("tipoDesconto").value != null) {
			if (typeof form.get("tipoDesconto").value !== "string") {
				if (
					(form.get("tipoDesconto").value.codigo as string) === "VE" &&
					(form.get("valorDesconto").value === null ||
						form.get("valorDesconto").value === 0)
				) {
					this.notificationService.error(
						this.traducoes.getLabel("error-valor-desconto-invalido-table")
					);
					return false;
				}
				if (
					(form.get("tipoDesconto").value.codigo as string) === "PD" &&
					(form.get("porcentagemDesconto").value === null ||
						form.get("porcentagemDesconto").value === 0)
				) {
					this.notificationService.error(
						this.traducoes.getLabel("error-porcentagem-desconto-invalido-table")
					);
					return false;
				}
			} else {
				this.notificationService.error(
					this.traducoes.getLabel("error-tipo-desconto-invalido-table")
				);
				return false;
			}
		} else {
			this.notificationService.error(
				this.traducoes.getLabel("error-tipo-desconto-invalido-table")
			);
			return false;
		}
		const existsDuracao = data.find(
			(value, index) =>
				form.get("duracao").value === value.duracao && index !== rowIndex
		);
		if (existsDuracao) {
			this.notificationService.error(
				this.traducoes.getLabel("error-duracao-duplicado-table")
			);
			return false;
		}
		return true;
	}

	confirm(event) {
		this.convenioDescontoDTO.convenioDescontoPlanoConfiguracaoList =
			this.configuracoesDesconto;
		this.afterEditAdd.emit({ convenioDescontoDTO: this.convenioDescontoDTO });
	}

	delete(event) {
		if (this.configuracoesDesconto) {
			this.configuracoesDesconto.splice(event.index, 1);
			this.convenioDescontoDTO.convenioDescontoPlanoConfiguracaoList =
				this.configuracoesDesconto;
			this.afterEditAdd.emit({ convenioDescontoDTO: this.convenioDescontoDTO });
		}
	}

	isEditingOrAdding($event: boolean) {
		this.isEditinOrAdding.emit($event);
	}

	private initTiposDesconto() {
		const tipos = [];
		tipos.push({ codigo: "VE", label: this.traducoes.getLabel("VE") });
		tipos.push({ codigo: "PD", label: this.traducoes.getLabel("PD") });
		this.tiposDesconto = tipos;
		this.cd.detectChanges();
	}

	private initPlanos() {
		this.planos = [];
		this.convenioDescontoConfiguracaoService.getPlano().subscribe({
			next: (response) => {
				response.content.forEach((r) => {
					this.planos.push({ codigo: r.codigo, label: r.descricao });
				});
			},
			error: (error) => {
				this.notificationService.error("Falha ao carregar planos");
			},
			complete: () => {
				this.cd.detectChanges();
			},
		});
	}
}
