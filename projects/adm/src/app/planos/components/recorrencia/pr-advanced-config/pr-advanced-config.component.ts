import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { PlanoStateService } from "../../cadastrar-plano/plano-state.service";
import { PlanoCommonsService } from "../../../services/plano-commons.service";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { Plano, TipoPlano } from "../../../plano.model";
import {
	BUTTON_TYPE,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { AdmRestService } from "../../../../adm-rest.service";
import { PlanoConfiguracaoSistema } from "../../../services/plano-configuracao-sistema.model";
import Quill from "quill";
import BlotFormatter from "quill-blot-formatter/dist/BlotFormatter";
import { AdmCoreApiIntegracoesService } from "adm-core-api";
import { SessionService } from "@base-core/client/session.service";
import { Modulo } from "../../../../../../../../src/app/base/configuracoes/integracoes-v2/classes/modulos.enum";

Quill.register("modules/blotFormatter", BlotFormatter);
Quill.import("attributors/style/size");

@Component({
	selector: "adm-pr-advanced-config",
	templateUrl: "./pr-advanced-config.component.html",
	styleUrls: ["./pr-advanced-config.component.scss"],
})
export class PrAdvancedConfigComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@Input() plano: Plano;
	form: FormGroup;
	buttonType = BUTTON_TYPE;
	frequencias: Array<{ label: string; value: number }> = new Array<{
		label: string;
		value: number;
	}>();
	private inicioMinimoContratoValid = true;
	configuracaoSistema: PlanoConfiguracaoSistema;
	modules = {};
	fontSizeArr = ["8px", "9px", "10px", "11px", "12px", "14px", "16px"];
	Size = Quill.import("attributors/style/size");
	fogueteHabilitada: boolean = false;

	modeloContratoAddtionalFilters = {
		situacao: "AT",
		tipoContrato: "VO",
	};
	modeloContratoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	constructor(
		public dialog: NgbActiveModal,
		public admRestService: AdmRestService,
		public planoStateService: PlanoStateService,
		private planoCommonsService: PlanoCommonsService,
		private notificationService: SnotifyService,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private cd: ChangeDetectorRef,
		private sessaoService: SessionService
	) {
		this.modules = {
			blotFormatter: {},
			toolbar: {
				container: [
					["bold", "italic", "underline"],
					[{ list: "ordered" }, { list: "bullet" }],
					[{ script: "sub" }, { script: "super" }],
					[{ indent: "-1" }, { indent: "+1" }],
					[{ size: this.fontSizeArr }],
					[{ header: [1, 2, 3, 4, 5, 6, false] }],
					[{ align: [] }],
					["clean"],
				],
			},
		};
	}

	ngOnInit() {
		this.Size.whitelist = this.fontSizeArr;
		Quill.register(this.Size, true);

		const Block = Quill.import("blots/block");
		Block.tagName = "DIV";
		Quill.register(Block, true);

		this.plano = this.planoStateService.updatePlanoObj();
		this.form = this.planoCommonsService.formConfigBasicos(
			this.plano.tipoPlano
		);
		this.setFrequencias();
		this.planoCommonsService.updateformConfigBasicos(this.form, this.plano);
		this.form.get("apresentarVendaRapida").valueChanges.subscribe((value) => {
			if (value) {
				this.form.get("site").setValue(false);
				this.form.get("permitirVendaPlanoSiteNoBalcao").setValue(false);
				this.form.get("permitirCompartilharPLanoNoSite").setValue(false);
			}
		});
		this.form
			.get("bloquearRecompra")
			.setValue(this.plano.bloquearRecompra || false);
		this.form
			.get("contratosEncerramDia")
			.setValue(this.plano.contratosEncerramDia);
		this.form
			.get("enviarDependenteFoguete")
			.setValue(this.plano.enviarDependenteFoguete || false);
		if (this.plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA) {
			if (this.plano.site) {
				this.form.get("termoAceite").setValidators(Validators.required);
				this.form.get("termoAceite").updateValueAndValidity();
			} else {
				this.form.get("termoAceite").clearValidators();
				this.form.get("termoAceite").updateValueAndValidity();
				this.form.get("permitirVendaPlanoSiteNoBalcao").setValue(false);
				this.form.get("permitirCompartilharPLanoNoSite").setValue(false);
			}
			this.form.get("site").valueChanges.subscribe((value) => {
				if (value) {
					this.form.get("termoAceite").setValidators(Validators.required);
					this.form.get("termoAceite").updateValueAndValidity();
				} else {
					this.form.get("termoAceite").clearValidators();
					this.form.get("termoAceite").updateValueAndValidity();
					this.form.get("permitirVendaPlanoSiteNoBalcao").setValue(false);
					this.form.get("permitirCompartilharPLanoNoSite").setValue(false);
				}
			});

			this.form.get("site").valueChanges.subscribe((value) => {
				if (value) {
					this.form.get("apresentarVendaRapida").setValue(false);
				}
				this.plano.site = value;
			});

			this.form
				.get("permitirVendaPlanoSiteNoBalcao")
				.valueChanges.subscribe((value) => {
					if (value) {
						this.form.get("apresentarVendaRapida").setValue(false);
					}
					this.plano.permitirVendaPlanoSiteNoBalcao = value;
				});

			this.form
				.get("permitirCompartilharPLanoNoSite")
				.valueChanges.subscribe((value) => {
					this.plano.permitirCompartilharPLanoNoSite = value;
				});
		}
		this.form.get("inicioMinimoContrato").valueChanges.subscribe((value) => {
			const now = new Date();
			now.setHours(0, 0, 0, 0);
			if (value !== "" && value < now) {
				this.form.get("inicioMinimoContrato").setErrors({ incorrect: true });
			}
			this.inicioMinimoContratoValid = this.form.get(
				"inicioMinimoContrato"
			).valid;
		});

		this.planoCommonsService.initConfiguracaoSistema().subscribe((data) => {
			this.configuracaoSistema = data;
		});
		this.integracaoFogueteAtivada();
	}

	async setFrequencias() {
		this.frequencias = await this.planoCommonsService.initFrequencias(
			this.traducao
		);
		this.cd.detectChanges();
	}

	salvar() {
		if (this.inicioMinimoContratoValid) {
			Object.keys(this.form.controls).forEach((controlKey) => {
				this.plano[controlKey] = this.form.get(controlKey).value;
			});
			this.plano.bloquearRecompra =
				this.form.get("bloquearRecompra").value || false;
			this.plano.contratosEncerramDia = this.form.get(
				"contratosEncerramDia"
			).value;
			this.plano.enviarDependenteFoguete =
				this.form.get("enviarDependenteFoguete").value || false;
			this.planoStateService.updateState(this.plano);
			this.dialog.close();
		} else {
			this.notificationService.error(
				this.traducao.getLabel("error-data-retroativa-obrigatorio"),
				this.traducao.getLabel("error-location-vendas-online")
			);
		}
	}

	dateFilter(date: Date): boolean {
		const actualDate = new Date();
		actualDate.setHours(0, 0, 0, 0);
		return date.getTime() >= actualDate.getTime();
	}

	existeModalidadeTurmaSelecionada(): boolean {
		if (
			this.plano &&
			this.plano.modalidades &&
			(!this.plano.modalidadesAux || this.plano.modalidadesAux.length === 0)
		) {
			const index = this.plano.modalidades.findIndex(
				(pm) => pm.modalidade.utilizaTurma
			);
			return index >= 0;
		} else if (this.plano.modalidadesAux) {
			const index = this.plano.modalidadesAux.findIndex(
				(pm) => pm.modalidade.utilizaTurma
			);
			return index >= 0;
		}
		return false;
	}

	getPlanosPermitidos() {
		return (
			this.plano.tipoPlano === TipoPlano.PLANO_NORMAL ||
			this.plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA
		);
	}

	integracaoFogueteAtivada() {
		this.admCoreApiIntegracoes
			.getConfiguracoesIntegracoes(this.sessaoService.codigoEmpresa, Modulo.ADM)
			.subscribe((res) => {
				this.fogueteHabilitada =
					res &&
					res.configuracaoIntegracaoFoguete &&
					res.configuracaoIntegracaoFoguete.habilitada;
			});
	}
}
