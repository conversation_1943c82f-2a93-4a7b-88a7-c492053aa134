import { Injectable } from "@angular/core";
import { AdmRestService } from "@adm/adm-rest.service";
import { HttpClient } from "@angular/common/http";
import { Observable, throwError } from "rxjs";
import { map } from "rxjs/operators";

@Injectable({
	providedIn: "root",
})
export class AdicionarClienteService {
	empresaSelecionada: number;
	clienteSelecionado: any;
	boletim: any;
	filtro: any;

	constructor(private admRest: AdmRestService, private http: HttpClient) {}

	consultar(
		empresa: number,
		nome: string,
		documento: string,
		email: string,
		telefone: string
	): Observable<any> {
		if (!this.empresaSelecionada) {
			return throwError("Empresa não selecionada");
		}
		return this.http
			.get(this.admRest.buildFullUrlZwBack2("cadastro-cliente/consultar"), {
				headers: {
					empresaId: this.empresaSelecionada.toString(),
				},
				params: {
					filters: JSON.stringify({
						empresa,
						nome,
						documento,
						email,
						telefone,
					}),
				},
			})
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	validar(dados: any): Observable<any> {
		return this.http
			.post(
				this.admRest.buildFullUrlZwBack2("cadastro-cliente/validar"),
				dados,
				{
					headers: {
						empresaId: this.empresaSelecionada.toString(),
					},
				}
			)
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	validarExisteCliente(dados: any): Observable<any> {
		return this.http
			.post(
				this.admRest.buildFullUrlZwBack2(
					"cadastro-cliente/validar-existe-cliente"
				),
				dados,
				{
					headers: {
						empresaId: this.empresaSelecionada.toString(),
					},
				}
			)
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	gravar(dados: any): Observable<any> {
		return this.http
			.post(
				this.admRest.buildFullUrlZwBack2("cadastro-cliente/gravar"),
				dados,
				{
					headers: {
						empresaId: this.empresaSelecionada.toString(),
					},
				}
			)
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	validarTrocaEmpresa(dados: any): Observable<any> {
		return this.http
			.post(
				this.admRest.buildFullUrlZwBack2(
					"cadastro-cliente/validar-troca-empresa"
				),
				dados,
				{
					headers: {
						empresaId: this.empresaSelecionada.toString(),
					},
				}
			)
			.pipe(
				map((response: any) => {
					return response.content;
				})
			);
	}

	fotoTemporaria(dados: any): Observable<any> {
		return this.http
			.post(
				this.admRest.buildFullUrlZwBack2("cadastro-cliente/foto-temp"),
				dados,
				{
					headers: {
						empresaId: this.empresaSelecionada.toString(),
					},
				}
			)
			.pipe(
				map((response: any) => {
					return response;
				})
			);
	}

	produtosWellhub(): Observable<any> {
		return this.http
			.get(
				this.admRest.buildFullUrlZwBack2("cadastro-cliente/produtos-wellhub"),
				{
					headers: {
						empresaId: this.empresaSelecionada.toString(),
					},
				}
			)
			.pipe(
				map((response: any) => {
					return response;
				})
			);
	}
}
