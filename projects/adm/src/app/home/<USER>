import {
	ChangeDetectorRef,
	Component,
	Inject,
	LOCALE_ID,
	OnInit,
	Optional,
} from "@angular/core";
import { Subject } from "rxjs";
import { IpService } from "../services/ip.service";
import { HttpClient } from "@angular/common/http";
import { CampanhaService } from "marketing-api";
import { isNullOrUndefinedOrEmpty, SessionService } from "sdk";
import { switchMap } from "rxjs/operators";
import { ModalObrigatoriosCallerService } from "pacto-layout";
import { ActivatedRoute } from "@angular/router";
import { SessionService as SessionServiceBase } from "@base-core/client/session.service";
import { DomSanitizer, SafeResourceUrl } from "@angular/platform-browser";

enum TagsEnum {
	CAIXAS_PEQUENAS = "CAIXAS_PEQUENAS",
	CAIXAS_GRANDES = "CAIXAS_GRANDES",
	SLIDER = "SLIDER",
	SLIDER_LINK = "SLIDER_LINK",
	CONTEUDO_BLOG = "CONTEUDO_BLOG",
	IFRAME = "IFRAME",
}

@Component({
	selector: "adm-pacto-home",
	templateUrl: "./home.component.html",
	styleUrls: ["./home.component.scss"],
})
export class HomeComponent implements OnInit {
	itemsCampanha: Array<any> = new Array<any>();
	tagsEnum = TagsEnum;
	selectedItem = 0;
	_destroyedInterval$: Subject<null> = new Subject();
	isClubeDeBeneficios = false;

	constructor(
		private ipService: IpService,
		private httpClient: HttpClient,
		private campanhaService: CampanhaService,
		@Inject(LOCALE_ID) private locale,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private modaisObrigatoriosService: ModalObrigatoriosCallerService,
		private route: ActivatedRoute,
		private sessionServiceBase: SessionServiceBase,
		private sanitizer: DomSanitizer
	) {}

	ngOnInit() {
		this.route.params.subscribe((p) => {
			this.isClubeDeBeneficios = p.cb;
		});
		if (!this.isClubeDeBeneficios) {
			this.loadBanner();
		}
		this.modaisObrigatoriosService.modaisObrigatorios();
	}

	loadBanner() {
		this.ipService
			.getIp()
			.pipe(
				switchMap((ipNumber) => {
					return this.httpClient
						.get(`https://ipwho.is/${ipNumber}`, {
							params: { lang: "pt-BR" },
						})
						.pipe(
							switchMap((ipData) => {
								const currentEmpresa = this.sessionService.currentEmpresa;

								const chaveEmpresa = this.sessionService.chave;
								const redeEmpresa = this.getValueFromObject(
									"codigo",
									currentEmpresa
								);
								const loggedUser = this.getValueFromObject(
									"perfilUsuario",
									this.sessionService
								);
								const estado = this.getValueFromObject(
									"estado",
									currentEmpresa
								);
								const pais = this.getValueFromObject("pais", currentEmpresa);
								const idioma = this.getValueFromObject(
									"siglaNovaPlataforma",
									currentEmpresa
								);

								const campanhaparams = {
									redeEmpresa: redeEmpresa,
									chaveEmpresa: chaveEmpresa ? chaveEmpresa : null,
									siglaEstado: estado ? estado : ipData["region_code"],
									tipoPerfil: this.getValueFromObject("nome", loggedUser),
									nomePais: pais ? pais : ipData["country"],
									modulo: "NZW",
									linguagem: idioma
										? (idioma as string).toUpperCase()
										: this.locale,
									page: 0,
									size: 100,
									orderBy: "codigo",
									ativa: true,
								};

								return this.campanhaService.getCurrentCampanha(campanhaparams);
							})
						);
				})
			)
			.subscribe((data: any) => {
				if (data.result) {
					const campanhas = [];
					(data.result as Array<any>).forEach(
						(campanha: { itens: Array<any> }) => {
							campanha.itens.forEach((item) => {
								campanhas.push(item);
							});
						}
					);
					this.itemsCampanha = campanhas.filter(
						(campanha: any) =>
							campanha.tag === TagsEnum.SLIDER_LINK ||
							campanha.tag === TagsEnum.IFRAME
					);
				}
				if (this.itemsCampanha.length === 0) {
					this.itemsCampanha.unshift({
						tag: TagsEnum.SLIDER_LINK,
						urlImagem: "pacto-ui/images/banners/adm.png",
					});
				}
				this.cd.detectChanges();
			});
	}

	openLinkInNewTab(link: string) {
		window.open(link, "_blank");
	}

	getValueFromObject(fieldValue: string, obj: any): any {
		if (obj && obj[fieldValue]) {
			return obj[fieldValue];
		}

		return null;
	}

	existeClubeDeBeneficios(): boolean {
		return (
			this.sessionServiceBase.clubeDeBeneficiosMarketing &&
			!isNullOrUndefinedOrEmpty(
				this.sessionServiceBase.clubeDeBeneficiosMarketing.link
			)
		);
	}

	linkClubeDeBeneficios(): SafeResourceUrl {
		const link = this.existeClubeDeBeneficios()
			? this.sessionServiceBase.clubeDeBeneficiosMarketing.link
			: "";
		return this.sanitizer.bypassSecurityTrustResourceUrl(link);
	}
}
