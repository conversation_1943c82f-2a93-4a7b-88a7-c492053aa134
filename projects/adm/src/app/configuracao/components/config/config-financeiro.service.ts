import { EventEmitter, Injectable } from "@angular/core";
import { FormControl } from "@angular/forms";
import {
	ConfiguracaoFinanceiro,
	FinanceiroMsApiConfiguracaoService,
} from "financeiro-ms-api";
import { forkJoin } from "rxjs";
import { ClientDiscoveryService } from "sdk";
import { ConfigItemType } from "../inputs/form-configuracoes.model";
import { ConfigModuloSubGroup } from "./model/config-module-subgroup.model";
import { SubGrupoInputs } from "./model/sub-group-inputs.model";

@Injectable({
	providedIn: "root",
})
export class ConfigFinanceiroService {
	loaded: EventEmitter<boolean> = new EventEmitter();
	config: ConfiguracaoFinanceiro = {} as ConfiguracaoFinanceiro;
	detectChanges: EventEmitter<boolean> = new EventEmitter();

	formControlMovimentacaoAutomaticaRecebiveisConciliacao: FormControl =
		new FormControl();
	formControlContaMovimentacaoAutomaticaCredito: FormControl =
		new FormControl();
	formControlContaMovimentacaoAutomaticaDebito: FormControl = new FormControl();
	formControlFavorecidoMovimentacaoAutomaticaCredito: FormControl =
		new FormControl();
	formControlFavorecidoMovimentacaoAutomaticaDebito: FormControl =
		new FormControl();
	formControlDescricaoMovimentacaoAutomaticaCredito: FormControl =
		new FormControl();
	formControlDescricaoMovimentacaoAutomaticaDebito: FormControl =
		new FormControl();

	formControlCriarContaPagarAutomatico: FormControl = new FormControl();
	formControlContaCriarContaPagarAutomatico: FormControl = new FormControl();
	formControlPlanoContasLancamentoAutomaticoEntrada: FormControl =
		new FormControl();
	formControlCentroCustoLancamentoAutomaticoEntrada: FormControl =
		new FormControl();
	formControlPlanoContasLancamentoAutomaticoSaida: FormControl =
		new FormControl();
	formControlCentroCustoLancamentoAutomaticoSaida: FormControl =
		new FormControl();

	constructor(
		private discoveryService: ClientDiscoveryService,
		private financeiroApiService: FinanceiroMsApiConfiguracaoService
	) {
		forkJoin(this.financeiroApiService.consultar()).subscribe(
			([config]) => {
				this.config = config;
			},
			(error) =>
				console.error(
					"Falha ao consultar dados para montar o formulário do financeiro",
					error
				),
			() => {
				this.loaded.emit(true);
			}
		);
	}

	getInputs(): SubGrupoInputs[] {
		return [
			this.getInputsBasicos(),
			this.getInputOperadoraCartao(),
			this.getInputTaxasBoleto(),
			this.getInputTaxasCheques(),
			this.getInputTaxasPix(),
			this.getInputConciliacao(),
		];
	}

	getInputTaxasCheques(): SubGrupoInputs {
		const description =
			"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque efficitur nunc sit amet venenatis pellentesque. Quisque pharetra accumsan ex vitae laoreet.";

		return {
			subgrupo: ConfigModuloSubGroup.FIN_TAXAS_DEVOLUCAO_CHEQUE,
			inputs: [
				{
					title: "Devolução de cheques",
					type: ConfigItemType.GROUP,
					children: [
						{
							title:
								"Considerar cheques devolvidos no relatório de comissão para consultor",
							name: "adicionarDevolucaoRelatorioComissao",
							description:
								"Ao marcar esta opção, o valor referente aos cheques devolvidos será abatido no relatório de comissão do consultor como um valor negativo.",
							type: ConfigItemType.CHECKBOX,
						},
						{
							name: "planoContasDevolucao",
							title: "Plano de contas de cheques devolvidos (Despesa)",
							description:
								'Defina aqui o Plano de contas DESPESA para os cheques que serão devolvidos e movimentados para uma conta do tipo pendência. Obs: Para definir o Plano de Contas referente à RECEITA oriunda de cheques devolvidos, faça isso pelo rateio integração. Procure pelo produto: "PAGAMENTO DE CHEQUES DEVOLVIDOS"',
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/planoconta",
							placeholder: "Pesquise um plano de contas",
							labelKey: "descricaoCosnulta",
							idKey: "codigo",
							initOption: this.config.planoContasDevolucaoRelacionado,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "500",
									pesquisa,
								};
							},
						},
						{
							name: "centroCustoDevolucao",
							title: "Centro de custo de cheques devolvidos (Despesa)",
							description:
								'Defina aqui o Centro de custo DESPESA para os cheques que serão devolvidos e movimentados para uma conta do tipo pendência. Obs: Para definir o Centro de Custo referente à RECEITA oriunda de cheques devolvidos, faça isso pelo rateio integração. Procure pelo produto: "PAGAMENTO DE CHEQUES DEVOLVIDOS"',
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/centrocusto",
							placeholder: "Pesquise um plano de contas",
							labelKey: "descricaoCosnulta",
							idKey: "codigo",
							initOption: this.config.centroCustoDevolucaoRelacionado,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "500",
									pesquisa,
								};
							},
						},
						{
							name: "bloquearAlunoChequeDevolvido",
							title: "Bloquear aluno com cheque devolvido",
							description:
								'Marcar a opção "Bloquear aluno com cheque devolvido" significa que, caso um aluno tenha um cheque devolvido e ainda possua parcelas em aberto decorrentes dessa devolução, o acesso desse aluno à academia (ou qualquer outro serviço relacionado) será bloqueado.',
							type: ConfigItemType.CHECKBOX,
						},
						{
							name: "mensagemBloqueio",
							title: "Mensagem de bloqueio de catraca",
							description:
								'Ao marcar a opção "Bloquear aluno com cheque devolvido" significa que, se um aluno tiver um cheque devolvido e ainda tiver parcelas em aberto relacionadas a essa devolução, o acesso desse aluno à catraca será bloqueado.Essa funcionalidade é útil para garantir que alunos que tenham cheques devolvidos e não tenham regularizado sua situação financeira não possam acessar a academia até que a questão dos cheques devolvidos seja resolvida. Neste campo informe uma mensagem de bloqueio, para ser apresentando, quando o aluno (que tiver o cheque devolvido e parcela ainda no caixa em aberto) tente passar pela catraca.',
							type: ConfigItemType.TEXT,
						},
					],
				},
			],
		};
	}

	getInputTaxasBoleto(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.FIN_TAXA_BOLETO,
			inputs: [
				{
					title: "Taxas de Boleto",
					description:
						"Informe o Plano de Contas e o Centro de Custos do lançamento das taxas de Boleto:",
					type: ConfigItemType.GROUP,
					children: [
						{
							name: "planoContasTaxaBoleto",
							title: "Plano de Contas",
							description:
								"Informe o Plano de Contas do lançamento das taxas de Boleto",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/planoconta",
							placeholder: "Pesquise um plano de contas",
							labelKey: "descricaoCosnulta",
							idKey: "codigo",
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "500",
									pesquisa,
								};
							},
						},
						{
							name: "centroCustosTaxaBoleto",
							title: "Centro de Custo",
							description:
								"Informe o Centro de Custo do lançamento das taxas de Boleto",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/centrocusto",
							placeholder: "Pesquise um plano de contas",
							labelKey: "descricaoCosnulta",
							idKey: "codigo",
							initOption: this.config.centroCustosTaxaBoletoRelacionado,
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "500",
									pesquisa,
								};
							},
						},
					],
				},
			],
		};
	}

	getInputTaxasPix(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.FIN_TAXAS_PIX,
			inputs: [
				{
					title: "Taxas de PIX",
					description:
						"Informe o Plano de Contas e o Centro de Custos do lançamento das taxas de PIX:",
					type: ConfigItemType.GROUP,
					children: [
						{
							name: "planoContasTaxaPix",
							title: "Plano de Contas",
							description:
								"Informe o Plano de Contas do lançamento das taxas de PIX",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/planoconta",
							placeholder: "Pesquise um plano de contas",
							labelKey: "descricaoCosnulta",
							idKey: "codigo",
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "50",
									pesquisa,
								};
							},
						},
						{
							name: "centroCustoTaxaPix",
							title: "Centro de Custo",
							description:
								"Informe o Centro de Custo do lançamento das taxas de PIX",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/centrocusto",
							placeholder: "Pesquise um centro de custo",
							labelKey: "descricaoCosnulta",
							idKey: "codigo",
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "50",
									pesquisa,
								};
							},
						},
					],
				},
			],
		};
	}

	getInputConciliacao(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.FIN_CONCILIACAO,
			inputs: [
				{
					name: "alterarDtPgtoZwAutomaticamenteConc",
					title:
						"Permite conciliar data de pagamento do sistema pacto automaticamente (Cielo)",
					description:
						"Disponível somente para Conciliação Cielo Configuração criada para que no momento do processamento do extrato o sistema compare a data prevista de pagamento que veio no extrato com a data do pagamento no sistema pacto e se elas forem diferentes, o sistema automaticamente irá alterar a data do sistema pacto, fazendo com que fique conciliado sem a necessidade de operações manuais lá na tela de conciliação.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "movimentacaoAutomaticaRecebiveisConciliacao",
					title: "Habilitar Movimentação Automática da Conciliação",
					description:
						"Disponível para todos os tipos de convênio da conciliação Marque esta opção para habilitar o processo de movimentação automática de recebíveis da conciliação.Com essa opção marcada, os recebíveis da conciliação serão movimentadas diariamente de forma automática, criando movimentações conforme a 'Descrição', 'Favorecido' e 'Conta Bancária' configuradas nos campos disponíveis." +
						"Com essa opção desmarcada, continua sendo necessário realizar a movimentação manualmente lá na tela 'Gestão de Recebíveis' ou na própria tela de conciliação.",
					type: ConfigItemType.CHECKBOX,
					formControl:
						this.formControlMovimentacaoAutomaticaRecebiveisConciliacao,
					autoControlCurrentValue: true,
					children: [
						{
							title: "Descrição Movimentação (Débito)",
							name: "descricaoMovimentacaoAutomaticaDebito",
							type: ConfigItemType.TEXT,
							description: "",
							formControl:
								this.formControlDescricaoMovimentacaoAutomaticaDebito,
							autoControlCurrentValue: true,
						},
						{
							title: "Favorecido (Débito)",
							name: "favorecidoMovimentacaoAutomaticaDebito",
							description: "",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/pessoa",
							placeholder: "Pesquise um favorecido",
							labelKey: "nome",
							idKey: "codigo",
							initOption:
								this.config.favorecidoMovimentacaoAutomaticaDebitoRelacionado,
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "50",
									pesquisa,
								};
							},
							formControl:
								this.formControlFavorecidoMovimentacaoAutomaticaDebito,
							autoControlCurrentValue: true,
						},
						{
							title: "Conta Bancária (Débito)",
							name: "contaMovimentacaoAutomaticaDebito",
							description: "",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl + "/v1/conta",
							placeholder: "Pesquise uma conta",
							labelKey: "descricao",
							idKey: "codigo",
							initOption:
								this.config.contaMovimentacaoAutomaticaDebitoRelacionado,
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "50",
									pesquisa,
								};
							},
							formControl: this.formControlContaMovimentacaoAutomaticaDebito,
							autoControlCurrentValue: true,
						},
						{
							title: "Descrição Movimentação (Crédito)",
							name: "descricaoMovimentacaoAutomaticaCredito",
							description: "",
							type: ConfigItemType.TEXT,
							formControl:
								this.formControlDescricaoMovimentacaoAutomaticaCredito,
							autoControlCurrentValue: true,
						},
						{
							title: "Favorecido (Crédito)",
							name: "favorecidoMovimentacaoAutomaticaCredito",
							description: "",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/pessoa",
							placeholder: "Pesquise um favorecido",
							labelKey: "nome",
							idKey: "codigo",
							initOption:
								this.config.favorecidoMovimentacaoAutomaticaCreditoRelacionado,
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "50",
									pesquisa,
								};
							},
							formControl:
								this.formControlFavorecidoMovimentacaoAutomaticaCredito,
							autoControlCurrentValue: true,
						},
						{
							title: "Conta Bancária (Crédito)",
							name: "contaMovimentacaoAutomaticaCredito",
							description: "",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl + "/v1/conta",
							placeholder: "Pesquise uma conta",
							labelKey: "descricao",
							idKey: "codigo",
							initOption:
								this.config.contaMovimentacaoAutomaticaCreditoRelacionado,
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "50",
									pesquisa,
								};
							},
							formControl: this.formControlContaMovimentacaoAutomaticaCredito,
							autoControlCurrentValue: true,
						},
					],
				},
				{
					name: "criarContaPagarAutomatico",
					title: "Criar conta a pagar automaticamente (Conciliação Stone)",
					description:
						'A opção "Criar conta a pagar automaticamente (Conciliação Stone)" refere-se a uma funcionalidade que automatiza a criação de uma conta a pagar quando a conciliação com a Stone (uma empresa de pagamentos e soluções financeiras) informa um cancelamento de transação.',
					type: ConfigItemType.CHECKBOX,
					formControl: this.formControlCriarContaPagarAutomatico,
					autoControlCurrentValue: true,
					children: [
						{
							title: "Conta que será vinculado a conta que foi criada",
							name: "contaCriarContaPagarAutomatico",
							description:
								"Neste campo será vinculada a conta a pagar que foi criada.",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl + "/v1/conta",
							placeholder: "Pesquise uma conta",
							labelKey: "descricao",
							idKey: "codigo",
							initOption: this.config.contaCriarContaPagarAutomaticoRelacionado,
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "50",
									pesquisa,
								};
							},
							formControl: this.formControlContaCriarContaPagarAutomatico,
							autoControlCurrentValue: true,
						},
						{
							title: "Plano de contas (Saída)",
							name: "planoContasLancamentoAutomaticoSaida",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/planoconta",
							placeholder: "Pesquise um plano de contas",
							labelKey: "descricaoCosnulta",
							idKey: "codigo",
							initOption:
								this.config.planoContasLancamentoAutomaticoSaidaRelacionado,
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "50",
									pesquisa,
								};
							},
							formControl: this.formControlPlanoContasLancamentoAutomaticoSaida,
							autoControlCurrentValue: true,
						},
						{
							title: "Centro de custo (Saída)",
							name: "centroCustoLancamentoAutomaticoSaida",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/centrocusto",
							placeholder: "Pesquise um centro de custo",
							labelKey: "descricaoCosnulta",
							idKey: "codigo",
							initOption:
								this.config.centroCustoLancamentoAutomaticoSaidaRelacionado,
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "50",
									pesquisa,
								};
							},
							formControl: this.formControlCentroCustoLancamentoAutomaticoSaida,
							autoControlCurrentValue: true,
						},
						{
							title: "Plano de contas (Entrada)",
							name: "planoContasLancamentoAutomaticoEntrada",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/planoconta",
							placeholder: "Pesquise um plano de contas",
							labelKey: "descricaoCosnulta",
							idKey: "codigo",
							initOption:
								this.config.planoContasLancamentoAutomaticoEntradaRelacionado,
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "50",
									pesquisa,
								};
							},
							formControl:
								this.formControlPlanoContasLancamentoAutomaticoEntrada,
							autoControlCurrentValue: true,
						},
						{
							title: "Centro de custo (Entrada)",
							name: "centroCustoLancamentoAutomaticoEntrada",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/centrocusto",
							placeholder: "Pesquise um centro de custo",
							labelKey: "descricaoCosnulta",
							idKey: "codigo",
							initOption:
								this.config.centroCustoLancamentoAutomaticoEntradaRelacionado,
							responseParser: (result: any) => result.content,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "50",
									pesquisa,
								};
							},
							formControl:
								this.formControlCentroCustoLancamentoAutomaticoEntrada,
							autoControlCurrentValue: true,
						},
					],
				},
			],
		};
	}

	getInputOperadoraCartao(): SubGrupoInputs {
		return {
			subgrupo: ConfigModuloSubGroup.FIN_TAXA_OPERADORA_CARTAO,
			inputs: [
				{
					title: "Taxas da operadora de cartão",
					description:
						"Informe o Plano de Contas e o Centro de Custos do lançamento das taxas de Operadora de Cartão",
					type: ConfigItemType.GROUP,
					children: [
						{
							name: "planocontastaxa",
							title: "Plano de Contas",
							description:
								"Informe o Plano de Contas do lançamento das taxas de Operadora de Cartão",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/planoconta",
							placeholder: "Pesquise um plano de contas",
							labelKey: "descricaoCosnulta",
							idKey: "codigo",
							initOption: this.config.planoContaTaxaRelacionado,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "20",
									pesquisa,
								};
							},
						},
						{
							name: "centrocustostaxa",
							title: "Centro de Custo",
							description:
								"Informe o Centro de Custo do lançamento das taxas de Operadora de Cartão",
							type: ConfigItemType.SELECTFILTER,
							endpointUrl:
								this.discoveryService.getUrlMap().financeiroMsUrl +
								"/v1/centrocusto",
							placeholder: "Pesquise um plano de contas",
							labelKey: "descricaoCosnulta",
							idKey: "codigo",
							initOption: this.config.centroCustoTaxaRelacionado,
							paramBuilder: (pesquisa: string) => {
								return {
									page: "0",
									size: "500",
									pesquisa,
								};
							},
						},
					],
				},
			],
		};
	}

	getInputsBasicos(): SubGrupoInputs {
		const description =
			"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque efficitur nunc sit amet venenatis pellentesque. Quisque pharetra accumsan ex vitae laoreet.";

		return {
			subgrupo: ConfigModuloSubGroup.FIN_DADOS_BASICOS,
			inputs: [
				{
					name: "usarCentralEventos",
					title: "Usar Central de Eventos no Financeiro",
					description:
						'Ao marcar a opção "Usar Central de Eventos no Financeiro", você está indicando que deseja incorporar receitas e despesas do módulo Central de Eventos ao setor financeiro do seu sistema. Isso implica que as transações financeiras relacionadas a eventos serão contabilizadas e gerenciadas diretamente no seu módulo financeiro, permitindo um controle mais abrangente e integrado de suas finanças relacionadas a eventos. Atenção: se você não utiliza a Central de Eventos, deixe esta opção desmarcada.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitirContaOutraUnidade",
					title: "Permitir selecionar conta de qualquer unidade",
					description:
						'Ao marcar a opção "Permitir selecionar conta de qualquer unidade", você está indicando que deseja permitir a seleção de contas de outras unidades ou empresas no momento de abrir o caixa financeiro. Isso implica que os usuários poderão escolher contas de diferentes unidades, mesmo que não pertençam à unidade em que estão trabalhando no momento.Essa funcionalidade pode oferecer flexibilidade e conveniência, especialmente em ambientes onde há uma necessidade de gerenciar transações financeiras entre diferentes unidades ou onde os usuários precisam acessar informações financeiras de unidades diferentes.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "especificarCompetencia",
					title: "Especificar Competências",
					description:
						'Ao marcar a opção "Especificar Competências" no contexto do Demonstrativo Financeiro e DRE Financeiro, você está ativando a funcionalidade que permite especificar competências relacionadas às finanças. Essas competências podem ser classificadas em "Quitadas" e "Não Quitadas".',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "fecharCaixaAutomaticamente",
					title: "Fechar automaticamente caixas abertos",
					description:
						'A opção "Fechar automaticamente caixas abertos" tem um efeito prático importante em relação à gestão financeira da empresa. Ao ativá-la, o sistema irá automaticamente fechar os caixas financeiros que estiverem abertos. Esse fechamento automático é executado durante a madrugada do dia subsequente à data de abertura do caixa.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "habilitarExportacaoAlterdata",
					title: "Habilitar exportação para sistema contábil AlterData",
					description:
						'A opção "Habilitar exportação para sistema contábil AlterData" é uma funcionalidade que se integra ao sistema contábil AlterData, permitindo exportar dados financeiros relevantes diretamente para esse sistema. Quando ativada, ela desbloqueia campos adicionais relacionados ao mercado contábil nas seções de contas a pagar e a receber.Atenção: se você não utiliza o sistema AlterData, deixe esta opção desmarcada.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "cnpjObrigatorioFornecedor",
					title: "Obrigatório informar CPF/CNPJ no cadastro de Fornecedor",
					description:
						'A opção "Obrigatório informar CPF/CNPJ no cadastro de Fornecedor" refere-se à configuração que torna obrigatório o fornecimento do número de CPF ou CNPJ durante o cadastro de um fornecedor no sistema. Quando essa opção está marcada, o usuário é obrigado a inserir o CPF ou CNPJ do fornecedor para concluir o processo de cadastro.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "contaPagarCompraEstoque",
					title:
						"Criar conta a pagar no financeiro ao lançar compra no estoque",
					description:
						'A opção "Criar conta a pagar no financeiro ao lançar compra no estoque" permite automatizar a criação de contas a pagar no módulo financeiro do sistema sempre que uma compra de produtos para o estoque é lançada. Ao marcar essa opção, o sistema gera automaticamente uma entrada de conta a pagar correspondente à compra realizada, ajudando na organização e no controle financeiro da empresa.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "contaPagarCompraEstoque",
					title: "Permite Pagar/Receber contas de colaboradores inativos",
					description:
						'A opção "Permite Pagar/Receber contas de colaboradores inativos" é uma funcionalidade que permite que contas a pagar ou a receber sejam lançadas no módulo financeiro para colaboradores que estão inativos. Isso pode ser útil em determinadas situações em que a empresa precisa registrar transações financeiras relacionadas a colaboradores que já não estão ativos na organização.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "apresentarValorPago",
					title: "Apresentar valor original da conta antes da quitação",
					description:
						'A opção "Apresentar valor original da conta antes da quitação" refere-se a uma funcionalidade que exibe o valor original de uma conta antes de ela ser quitada. Muitas vezes, durante o processo de quitação, pode haver alguma alteração no valor, seja por descontos, ajustes ou outras razões. Essa opção permite que o sistema exiba o valor original da conta antes dessas modificações.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "permitirTipoPlanoContaFilho",
					title: "Permitir informar tipo de plano de conta filho",
					description:
						'A opção "Permitir informar tipo de plano de conta filho" refere-se a uma funcionalidade que permite especificar o tipo de conta (entrada ou saída) para os planos de contas filhos, independentemente do tipo definido no plano de contas pai.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "metaFinanceiraPorFaturamento",
					title: "Trabalhar com meta financeira no formato faturamento",
					description:
						'Trabalhar com meta financeira no formato faturamento" refere-se ao recurso BI meta financeira. Ao habilitar, o faturamento em aberto também entra nos totalizadores.',
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "ordemCompraEstoque",
					title: "Habilitar ordem de compra ao lançar compra estoque",
					description:
						"Marque esta opção somente se deseja habilitar o fluxo de ordem de compra ao lançar uma compra de estoque fazendo com que seja necessario autorizar ou negar.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "habilitarAlteracaoValorPrevisto",
					title:
						"Alterar o valor previsto quando editar o valor da Conta a Pagar/Receber",
					description:
						"Marque esta opção se deseja habilitar que o campo valor previsto nas contas a pagar/receber sejam atualizados ao alterar o valor original da conta!",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "informarFavorecidoAoRealizarMovimentacaoRecebiveis",
					title: "Informar favorecido ao realizar a movimentação de recebíveis",
					description:
						"Ao habilitar essa configuração será possível escolher o favorecido na movimentação de recebíveis.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "buscarFornecedorTodasUnidades",
					title: "Buscar fornecedores de todas unidades (multiempresa)",
					description:
						"Ao habilitar essa configuração, quando for consultar o nome de um fornecedor no contas a pagar/receber ou na movimentação de recebíveis " +
						"então o sistema irá consultar os fornecedores independente da unidade logada, trazendo de várias unidades e possibilitando selecionar.",
					type: ConfigItemType.CHECKBOX,
				},
				{
					name: "obrigarPreenchimentoManualDtCompetencia",
					title: "Obrigar preenchimento manual da data de competência",
					description:
						"Ao habilitar essa configuração, o sistema não preencherá automaticamente o campo data de competência nos lançamentos de contas a pagar e a receber.",
					type: ConfigItemType.CHECKBOX,
				},
			],
		};
	}
}
