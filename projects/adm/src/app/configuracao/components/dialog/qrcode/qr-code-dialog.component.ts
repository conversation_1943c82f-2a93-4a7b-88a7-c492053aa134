import {
	OnInit,
	Component,
	ElementRef,
	Input,
	ViewChild,
	ChangeDetectorRef,
	<PERSON><PERSON><PERSON><PERSON>,
	AfterViewInit,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { CrmApiConfigIAService } from "crm-api";
import { SnotifyService } from "ng-snotify";
import { BehaviorSubject, forkJoin, of } from "rxjs";
import { catchError, switchMap } from "rxjs/operators";
import { SessionService } from "sdk";
import { LoaderService } from "ui-kit";

@Component({
	selector: "pacto-qr-code-dialog-component",
	templateUrl: "./qr-code-dialog-component.html",
	styleUrls: ["./qr-code-dialog-component.scss"],
})
export class QrCodeDialogComponent implements OnInit, AfterViewInit, OnDestroy {
	@Input() title: string;
	@Input() body: string;
	@Input() bodyRef: ElementRef;
	@ViewChild("actionDefault", { static: true }) actionDefault;
	@Input() actionLabel;
	@Input() cancelLabel;
	@Input() showCancelButton;
	@Input() idInstancia: string;
	@Input() token: string;
	@Input() codigoEmpresa: any;

	textoApresentacao: string = "";

	qrCodeAcesso: any;
	qrCode: boolean;
	formControl: FormControl;
	timer: number = 60;
	status: string;
	textoInstancia: string = "Criando conexão com whatsapp ... ";
	private intervalId: any;
	whatsappBusiness: boolean = false;
	loading = false;
	formControlIsDevice = new FormControl(false);
	formControlWhatsAppBusiness: FormControl = new FormControl(false);
	flagNovaInstancia: boolean = true;

	private statusSubject$ = new BehaviorSubject<string>("");
	status$ = this.statusSubject$.asObservable();
	previousValue: string = "";

	telefone: string;
	imgUrl: string;
	nome: string;
	originalDevice: string;
	imageStatic: string =
		"https://i.pinimg.com/1200x/2c/47/d5/2c47d5dd5b532f83bb55c4cd6f5bd1ef.jpg";

	constructor(
		public dialog: NgbActiveModal,
		public notificationService: SnotifyService,
		private apiAIconfigService: CrmApiConfigIAService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private loaderService: LoaderService
	) {}

	set statusConnected(value: string) {
		this.previousValue = this.statusSubject$.value;
		this.statusSubject$.next(value);
	}

	get statusConnected(): string {
		return this.statusSubject$.value;
	}

	get getPreviousValue() {
		return this.previousValue;
	}

	ngOnInit(): void {
		if (this.hasInstancia()) {
			this.updateQrCode();
		}

		this.formControlWhatsAppBusiness.valueChanges.subscribe((value) => {
			this.whatsappBusiness = value;
		});

		this.subscribeToDeviceChanges();

		this.status$.subscribe(() => {
			if (
				this.previousValue !== "" &&
				this.getPreviousValue !== this.status &&
				this.instanciaConectada()
			) {
				this.statusConexaoChanged();
			}
		});
	}

	ngAfterViewInit(): void {
		(async () => {
			this.intervalId = setInterval(async () => {
				try {
					await this.updateQrCode();
				} catch (e) {
					console.error(e);
					this.notificationService.error("Erro ao atualizar QRCode");
				}
			}, 12000);
		})();
	}

	ngOnDestroy(): void {
		if (this.intervalId) {
			clearInterval(this.intervalId);
		}
	}

	criarInstancia() {
		if (!this.idInstancia || !this.token) {
			this.whatsappBusiness = !!this.whatsappBusiness;

			const payload = {
				nomeEmpresa: this.sessionService.currentEmpresa.nome,
				whatsappBusiness: this.whatsappBusiness,
				isDevice: this.formControlIsDevice.value,
				flagNovaInstancia: this.flagNovaInstancia,
				codigoEmpresa: this.codigoEmpresa,
			};

			this.apiAIconfigService.criarInstancia(payload).subscribe({
				next: (res) => {
					if (res) {
						this.idInstancia = res.id;
						this.token = res.token;
						this.notificationService.success("Nova Instancia criada!");
						this.textoInstancia = "Instância criada com sucesso!";
						this.updateQrCode();
					}
				},
				error: (e) => {
					console.error(e);
					this.notificationService.error("Erro ao criar nova Instancia");
					this.textoInstancia = "Erro ao criar nova Instancia";
				},
			});
		}
	}

	subscribeToDeviceChanges() {
		this.formControlIsDevice.valueChanges.subscribe(() => {
			const payload = {
				nomeEmpresa: this.sessionService.currentEmpresa.nome,
				whatsappBusiness: this.whatsappBusiness,
				isDevice: this.formControlIsDevice.value,
				flagNovaInstancia: true,
				codigoEmpresa: this.codigoEmpresa,
			};

			this.apiAIconfigService.criarInstancia(payload).subscribe((res) => {
				this.idInstancia = res.id;
				this.token = res.token;
				this.updateQrCode();
				this.cd.detectChanges();
			});
		});
	}

	desconectarInstancia() {
		this.apiAIconfigService.desconectarInstancia(this.codigoEmpresa).subscribe(
			(res) => {
				if (res) {
					this.notificationService.success("Instancia desconectada");
					this.updateQrCode();
				}
			},
			(e) => {
				console.error(e);
				this.notificationService.error(
					"Não foi possível desconectar o whatsapp, Verifique com o suporte"
				);
			}
		);
	}

	private updateQrCode(): void {
		if (!this.hasInstancia()) {
			return;
		}
		this.apiAIconfigService
			.getQrCode(this.idInstancia, this.token, this.formControlIsDevice.value)
			.pipe(
				catchError((error) => {
					console.error("Erro ao obter QR Code:", error);
					this.notificationService.error(
						"Erro ao gerar QRCode: Instância não encontrada."
					);
					return of(null);
				}),
				switchMap((qrCodeResponse) => {
					this.qrCodeAcesso = qrCodeResponse.imageContent || null;
					this.qrCode = qrCodeResponse.qrCode || false;
					this.status = qrCodeResponse.status || null;
					this.statusConnected = qrCodeResponse.status || null;
					this.textoApresentacao = qrCodeResponse.qrCode
						? "Leia o QRCode no seu dispositivo móvel"
						: "";

					if (this.qrCode !== false || this.status !== "connected") {
						return of({ qrCodeResponse, statusResponse: null });
					}

					return forkJoin({
						qrCodeResponse: of(qrCodeResponse),
						statusResponse: this.apiAIconfigService
							.obterWhatsappConectado(this.idInstancia, this.token)
							.pipe(
								catchError((error) => {
									console.warn("Erro ao obter status do WhatsApp:", error);
									return of(null);
								})
							),
					});
				})
			)
			.subscribe({
				next: ({ qrCodeResponse, statusResponse }) => {
					if (statusResponse) {
						this.telefone = statusResponse.phone;
						this.nome = statusResponse.name;
						this.imgUrl =
							!statusResponse.imgUrl || statusResponse.imgUrl === "null"
								? this.imageStatic
								: statusResponse.imgUrl;
						this.originalDevice = statusResponse.originalDevice;
					}

					// Atualiza a tela
					this.startTimer();
					this.cd.detectChanges();
				},
				error: (error) => {
					console.error("Erro inesperado:", error);
					this.notificationService.error("Erro ao processar requisições.");
				},
			});
	}

	startTimer() {
		const intervalId = setInterval(() => {
			this.timer = this.timer - 1;
			if (this.timer === 0) {
				clearInterval(intervalId);
				this.close("");
			}
			this.cd.detectChanges();
		}, 60000);
	}

	dismiss() {
		this.dialog.dismiss({
			token: this.token,
			idInstancia: this.idInstancia,
		});
	}

	cancelarInstancia() {
		if (
			confirm(
				"Deseja realmente cancelar a instância? Esta ação não poderá ser desfeita."
			)
		) {
			this.apiAIconfigService
				.cancelarInstancia(this.codigoEmpresa, this.idInstancia, this.token)
				.subscribe(
					(res) => {
						if (res) {
							this.notificationService.success("Instancia cancelada");
							this.idInstancia = null;
							this.token = null;
							this.close("");
							this.cd.detectChanges();
						}
					},
					(e) => {
						console.error(e);
						this.notificationService.error("Erro ao desconectar instancia");
					}
				);
		}
	}

	close(result: any) {
		this.dialog.close({
			token: this.token,
			idInstancia: this.idInstancia,
		});
	}

	hasInstancia() {
		return this.idInstancia && this.token;
	}

	instanciaConectada() {
		return this.qrCode === false && this.status === "connected";
	}

	statusConexaoChanged() {
		const codigoEmpresa = this.sessionService.currentEmpresa.codigo;
		this.apiAIconfigService.atualizarContextosCrm(codigoEmpresa).subscribe(
			(res) => {
				this.notificationService.simple("Contextos atualizados com sucesso");
			},
			(error) => {
				console.error(error.message);
			}
		);
	}
}
