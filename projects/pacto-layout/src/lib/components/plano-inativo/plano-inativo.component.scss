.plano-modal-container {
	background-color: #fff;
	border-radius: 4px;
	max-width: 800px;
	width: 100%;
	display: flex;
	flex-direction: column;
	max-height: 90vh;
	min-height: 420px;
}
.plano-modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 13px 16px 8px;
	border-bottom: 1px solid #c9cbcf;
}
.vigencia {
	display: inline-block;
	text-align: right;
	width: 100%;
}
.modal-title {
	width: 322px;
	height: 18px;
	font-family: "Poppins", sans-serif;
	font-weight: 600;
	font-size: 14px;
	line-height: 125%;
	letter-spacing: 0.25px;
	color: #55585e;
	margin: 0;
	display: flex;
	align-items: center;
}
.plano-close-button {
	background: transparent;
	border: none;
	font-size: 12px;
	cursor: pointer;
	color: #666;
	padding: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 24px;
	height: 24px;
	outline: none !important;
}
.plano-modal-content {
	padding: 16px;
	display: flex;
	flex-direction: column;
	gap: 16px;
	flex: 1;
}
.plano-aviso {
	height: 36px;
	font-family: "Poppins", sans-serif;
	font-weight: 600;
	font-size: 14px;
	line-height: 125%;
	letter-spacing: 0.25px;
	color: #494b50;
	margin: 0;
	display: flex;
	align-items: center;
}
::ng-deep .plano-modal-content .pacto-relatorio-container {
	border-radius: 4px;
	overflow: hidden;
	border: 1px solid #e0e0e0;
}
::ng-deep .plano-modal-content .header-actions,
::ng-deep .plano-modal-content .relatorio-actions,
::ng-deep .plano-modal-content .page-nav {
	display: none !important;
}
.plano-modal-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: auto;
}

.alterar-data-link {
	color: #2962ff;
	text-decoration: none;
	font-size: 14px;
	cursor: pointer;
}

.alterar-data-link:hover {
	text-decoration: underline;
}

.nao-exibir-btn {
	background-color: #2962ff;
	color: white;
	border: none;
	border-radius: 4px;
	padding: 8px 16px;
	font-size: 14px;
	font-weight: 400;
	cursor: pointer;
	height: 40px;
	min-width: 316px;
}
.nao-exibir-btn:hover {
	background-color: #1e50c8;
}
::ng-deep .mat-dialog-container {
	padding: 0 !important;
	border-radius: 4px !important;
	overflow: hidden !important;
}
::ng-deep .cdk-overlay-backdrop {
	cursor: default !important;
}
::ng-deep table.table td:nth-child(2) {
	text-align: right !important;
	padding-right: 55px !important;
}
::ng-deep table.table th:nth-child(2) {
	text-align: right !important;
	padding-right: 16px;
}
@media (max-width: 768px) {
	.plano-modal-footer {
		flex-direction: column;
		align-items: flex-start;
		gap: 16px;
	}

	.nao-exibir-btn {
		width: 100%;
		min-width: unset;
	}
}
