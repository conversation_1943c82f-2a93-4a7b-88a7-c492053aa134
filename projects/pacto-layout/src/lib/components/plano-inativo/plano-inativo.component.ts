import {
	Component,
	Inject,
	OnInit,
	ViewChild,
	TemplateRef,
	ChangeDetectorRef,
	AfterViewInit,
	Optional,
} from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { SessionService } from "sdk";
import { PactoDataGridConfig, PactoDataGridState } from "ui-kit";
import { PlanoInativoService } from "../../services/plano-inativo.service";
import { PactoLayoutSDKWrapper } from "../../sdk-wrapper/sdk-wrappers";

@Component({
	selector: "pacto-plano-inativo",
	templateUrl: "./plano-inativo.component.html",
	styleUrls: ["./plano-inativo.component.scss"],
})
export class PlanoInativoComponent implements OnInit, AfterViewInit {
	@ViewChild("tableData", { static: false }) tableData;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("planoColumnName", { static: true })
	planoColumnName: TemplateRef<any>;
	@ViewChild("dataVigenciaColumnName", { static: true })
	dataVigenciaColumnName: TemplateRef<any>;

	constructor(
		public dialogRef: MatDialogRef<PlanoInativoComponent>,
		private planoInativoService: PlanoInativoService,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		@Optional() private pactoLayoutSDKWrapper: PactoLayoutSDKWrapper,
		@Inject(MAT_DIALOG_DATA) public data: any
	) {}
	usuarioId?: number;
	table: PactoDataGridConfig;

	ngOnInit(): void {
		const usuario = this.sessionService.loggedUser;
		this.usuarioId = usuario.id;
		this.data.usuarioId = usuario.id;
	}
	ngAfterViewInit(): void {
		this.initTable();
		setTimeout(() => {
			if (this.tableData && this.tableData.fetchData) {
				this.tableData.fetchData();
			}
		}, 100);
	}

	fechar(): void {
		this.dialogRef.close();
	}

	naoExibirModalHoje(checked: boolean): void {
		if (checked && this.data.usuarioId) {
			this.planoInativoService
				.naoExibirModalPlanosHoje(this.data.usuarioId)
				.subscribe({
					next: () => {
						this.dialogRef.close();
					},
					error: (error) => {
						console.error("Erro ao marcar modal como n�o exibir:", error);
					},
				});
		}
	}

	private initTable(): void {
		setTimeout(() => {
			let endpointUrl = "";
			if (this.pactoLayoutSDKWrapper) {
				endpointUrl = `${
					this.pactoLayoutSDKWrapper.clientDiscoveryService.getUrlMap()
						.planoMsUrl
				}/planos/proximos-inativar?empresa=${this.sessionService.empresaId}`;
			}
			this.table = new PactoDataGridConfig({
				endpointUrl,
				quickSearch: false,
				ghostLoad: false,
				showFilters: false,
				pagination: false,
				dataAdapterFn: (response) => {
					const content = response.content || [];

					const sortField = this.table.state.ordenacaoColuna;
					const direction = this.table.state.ordenacaoDirecao;

					if (sortField && direction) {
						content.sort((a, b) => {
							const valA = a[sortField];
							const valB = b[sortField];
							if (valA == null) {
								return 1;
							}
							if (valB == null) {
								return -1;
							}
							if (typeof valA === "number" && typeof valB === "number") {
								return direction === "ASC" ? valA - valB : valB - valA;
							}
							return direction === "ASC"
								? valA.localeCompare(valB)
								: valB.localeCompare(valA);
						});
					}

					const finalResult = {
						content,
						totalElements: content.length,
						totalPages: 1,
						size: content.length,
						number: 0,
					};
					return finalResult;
				},

				paramAdapterFn: (params) => {
					const finalParams = {
						filters: "{}",
						configs: "{}",
						...params,
					};
					if (params && params.sortField) {
						const sortedParams = {
							...finalParams,
							sort: params.sortField,
							order: params.sortDirection === "ASC" ? "asc" : "desc",
						};
						return sortedParams;
					}
					return finalParams;
				},
				state: new PactoDataGridState({
					ordenacaoColuna: null,
					ordenacaoDirecao: null,
				}),
				columns: [
					{
						nome: "descricao",
						titulo: this.planoColumnName,
						visible: true,
						ordenavel: true,
						buscaRapida: false,
					},
					{
						nome: "vigenciaAte",
						titulo: this.dataVigenciaColumnName,
						visible: true,
						ordenavel: true,
						buscaRapida: false,
						valueTransform: (value) => {
							if (value) {
								const date = new Date(value);
								return date.toLocaleDateString("pt-BR");
							}
							return "";
						},
					},
				],
			});
			this.cd.detectChanges();
		});
	}
	abrirAjuda() {
		window.open(
			"https://pactosolucoes.com.br/ajuda/conhecimento/como-reativar-um-plano-que-nao-esta-mais-vigente/",
			"_blank"
		);
	}
}
