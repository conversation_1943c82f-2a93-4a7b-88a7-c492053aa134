import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	HostListener,
	OnDestroy,
	OnInit,
	Optional,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { LayoutNavigationService } from "../../navigation/layout-navigation.service";
import { PlataformModuleConfig } from "../../navigation/models";
import { TaskbarService } from "../../navigation/taskbar/taskbar.service";
import {
	LoginUrlQueries,
	PactoLayoutSDKWrapper,
	PlataformaMenuV2Config,
} from "../../sdk-wrapper/sdk-wrappers";
import { Subscription } from "rxjs";
import { NavigationEnd, Router } from "@angular/router";
import { PermissaoService } from "../../navigation/permissao/permissao.service";
import {
	ZwPactoPayApiUtilService,
	FaciliteConfig,
	ApiResponseSingle,
} from "zw-pactopay-api";
import { LoaderService } from "ui-kit";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-menu-layout",
	templateUrl: "./menu.component.html",
	styleUrls: ["./menu.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MenuComponent implements OnInit, OnDestroy {
	@ViewChild("moduleContainer", { static: false }) moduleContainer: ElementRef;
	modules: Array<PlataformModuleConfig> = new Array<PlataformModuleConfig>();
	taskbarModules: Array<PlataformModuleConfig> =
		new Array<PlataformModuleConfig>();
	explorarModules: Array<PlataformModuleConfig> =
		new Array<PlataformModuleConfig>();

	currentModule: PlataformModuleConfig;
	configuracao: PlataformaMenuV2Config;
	subscription: Array<Subscription> = new Array<Subscription>();
	fullScreen: boolean;
	showBackMenuDescription: boolean;
	private _isMobile = false;
	openMenu: boolean = false;
	@ViewChild("sidebar", { static: false })
	sidebar;
	loadedConfig: boolean;

	constructor(
		@Optional() private pactoLayoutSDKWrapper: PactoLayoutSDKWrapper,
		private layoutNavigationService: LayoutNavigationService,
		private taskbarService: TaskbarService,
		private zwPactoPayApiUtilService: ZwPactoPayApiUtilService,
		private cd: ChangeDetectorRef,
		private router: Router,
		private loaderService: LoaderService,
		private notificationService: SnotifyService,
		private permissaoService: PermissaoService
	) {}

	ngOnInit() {
		if (
			this.pactoLayoutSDKWrapper &&
			this.pactoLayoutSDKWrapper.sessionService &&
			this.pactoLayoutSDKWrapper.sessionService.modulosHabilitados &&
			(this.pactoLayoutSDKWrapper.sessionService.modulosHabilitados.includes(
				PlataformModuleConfig.ADM.sigla
			) ||
				this.pactoLayoutSDKWrapper.sessionService.modulosHabilitados.includes(
					PlataformModuleConfig.ADM_LEGADO.sigla
				))
		) {
			this.loaderService.initForce();
			this.zwPactoPayApiUtilService
				.configFacilite(
					this.pactoLayoutSDKWrapper.sessionService.empresaId,
					this.pactoLayoutSDKWrapper.sessionService.chave,
					this.pactoLayoutSDKWrapper.sessionService.loggedUser.username
				)
				.subscribe(
					(response: ApiResponseSingle<FaciliteConfig>) => {
						this.layoutNavigationService.faciliteConfig = response.content;
						this.loadedConfig = true;
						this.init();
						this.loaderService.stopForce();
					},
					(httpResponseError) => {
						this.loadedConfig = true;
						this.init();
						// this.notificationService.error('Ocorreu um erro ao tentar carregar as configurações do Facilite');
						this.loaderService.stopForce();
					}
				);
		} else {
			this.loadedConfig = true;
			this.init();
		}
	}

	ngAfterViewInit() {}

	ngOnDestroy() {
		if (this.subscription) {
			this.subscription.forEach((s) => s.unsubscribe());
		}
	}

	private init() {
		this.getMenuByRoute();
		this.router.events.subscribe((event) => {
			if (event instanceof NavigationEnd) {
				this.getMenuByRoute();
				this.cd.detectChanges();
			}
		});
		if (this.pactoLayoutSDKWrapper) {
			const subscription = this.pactoLayoutSDKWrapper
				.getConfig()
				.subscribe((config) => {
					this.configuracao = config;
					this.cd.detectChanges();
				});
			this.subscription.push(subscription);
		} else {
			this.configuracao = new PlataformaMenuV2Config();
		}
		this.setIsMobile();
		this.modules = this.layoutNavigationService.filterModules(
			this.layoutNavigationService.modules
		);
		this.taskbarModules = this.layoutNavigationService.filterModules(
			this.layoutNavigationService.modulesTaskbar()
		);
		this.explorarModules = this.layoutNavigationService.filterModules(
			this.layoutNavigationService.modulesExplorar()
		);

		const localStorageParams =
			this.pactoLayoutSDKWrapper.getLocalStorageParams() ||
			({} as LoginUrlQueries);
		const taskbarSubscription =
			this.taskbarService.taskbarItemSelected$.subscribe((item) => {
				if (item) {
					if (!this.layoutNavigationService.modules.includes(item)) {
						this.currentModule = item;
					} else {
						this.currentModule =
							PlataformModuleConfig.findByIdOrIdInRoute(item.idInRoute) ||
							PlataformModuleConfig.findByIdOrIdInRoute(
								localStorageParams.moduleId
							);
					}
					this.cd.detectChanges();
				}
			});

		this.subscription.push(taskbarSubscription);
		this.cd.detectChanges();
	}

	@HostListener("window:resize", ["$event"])
	isMobileListener(event) {
		this.setIsMobile(event.target.innerWidth);
	}

	get opened() {
		return !this.isMobile;
	}

	get isMobile() {
		return this._isMobile;
	}

	setIsMobile(width = window.innerWidth) {
		this._isMobile = width <= 1280;
	}

	getMenuByRoute() {
		const dataRoute = this.layoutNavigationService.getFullCustomDataTree();
		this.fullScreen = dataRoute.fullscreen;
		if (dataRoute.moduleId) {
			this.currentModule = this.layoutNavigationService.getCurrentModule(
				dataRoute.moduleId
			);
		} else {
			this.currentModule = this.layoutNavigationService.getCurrentModule();
		}
		this.layoutNavigationService.updateTabNameLogo(this.currentModule);
	}

	trateCompanyLogoError(event: ErrorEvent) {
		if (event.type === "error") {
			const target: HTMLImageElement = event.target as HTMLImageElement;
			target.src = "pacto-ui/images/logos/pct-icone-fundo-pacto-branco.svg";
		}
	}

	showBackButton(): boolean {
		return (
			this.currentModule === PlataformModuleConfig.FAVORITES ||
			this.currentModule === PlataformModuleConfig.CONFIG ||
			this.currentModule === PlataformModuleConfig.PERSONS ||
			this.currentModule === PlataformModuleConfig.ADD_PERSON
		);
	}

	onMouseEnterSidebarCurrentModule() {
		if (this.showBackButton()) {
			this.showBackMenuDescription = true;
		}
	}

	onMouseLeaveSidebarCurrentModule() {
		if (this.showBackButton()) {
			this.showBackMenuDescription = false;
		}
	}

	get isUrlClubeDeBeneficios(): boolean {
		const url = this.router.url;
		return url.includes(";cb=true");
	}

	goToLastModule() {
		this.taskbarService.updateLastNavigationSubject();
		this.showBackMenuDescription = false;
		this.layoutNavigationService.navigateToLastRouterPage();
	}

	goToHome() {
		this.router.navigate(["/adm", "home"]).finally(() => {
			location.reload();
		});
	}

	get lastModuleNavigation(): PlataformModuleConfig {
		return this.layoutNavigationService.getLastModuleNavigationHistory();
	}

	openMenuExplorar() {
		this.openMenu = !this.openMenu;
		if (this.openMenu && this.sidebar.opened) {
			this.sidebar.toggle();
		}
		return this.openMenu;
	}
}
