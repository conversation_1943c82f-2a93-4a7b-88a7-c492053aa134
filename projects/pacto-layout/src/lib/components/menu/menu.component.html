<ng-container *ngIf="loadedConfig">
	<pacto-cat-sidebar-container *ngIf="!fullScreen">
		<pacto-cat-taskbar tabindex="-1">
			<div class="logo-topo" tabindex="-1">
				<img
					(error)="trateCompanyLogoError($event)"
					[src]="
						configuracao?.avatarRedeUrl
							? (configuracao?.avatarRedeUrl | safe)
							: 'pacto-ui/images/logos/pct-icone-fundo-pacto-branco.svg'
					"
					alt="Logo empresa"
					height="40"
					width="40" />
			</div>

			<nav
				tabindex="-1"
				(click)="goToHome()"
				(mouseenter)="onMouseEnterSidebarCurrentModule()"
				(mouseleave)="onMouseLeaveSidebarCurrentModule()"
				[id]="'taskbar-current-module-' + currentModule?.id"
				aria-label="Taskbar current module"
				class="taskbar-current-module bg-{{
					currentModule?.baseColor
				}} show-back-button-{{ showBackButton() }} show-back-menu-description-{{
					showBackMenuDescription
				}}">
				<div
					*ngIf="currentModule?.type === 'module' && !showBackButton()"
					class="module-icon">
					<i class="pct pct-new-brand-1 cor-branco"></i>
				</div>
				<div
					*ngIf="
						currentModule?.type !== 'module' &&
						currentModule?.taskbarConfig &&
						!showBackButton()
					"
					class="taskbar-item-icon">
					<i
						class="pct pct-{{
							currentModule.taskbarConfig.iconName
						}} cor-branco"></i>
				</div>
				<div
					(click)="goToHome()"
					*ngIf="showBackButton()"
					class="module-icon"
					style="border: none">
					<i
						class="pct pct-arrow-left-circle {{
							showBackMenuDescription ? 'cor-azul' : 'cor-branco'
						}} no-border"></i>
				</div>
			</nav>

			<pacto-taskbar-modules [modules]="taskbarModules"></pacto-taskbar-modules>
			<pacto-taskbar-resources [sidebar]="sidebar"></pacto-taskbar-resources>
		</pacto-cat-taskbar>

		<pacto-cat-sidebar
			tabindex="-1"
			#sidebar
			[mode]="isMobile ? 'overlay' : 'side'"
			[opened]="opened"
			class="pacto-cat-sidebar-flex">
			<div
				(click)="sidebar.toggle()"
				[ngClass]="{ opened: sidebar.opened }"
				class="open-sidebar-trigger"
				id="sidebar-menu-toggle">
				<i class="pct pct-chevron-right cor-branco"></i>
			</div>

			<pacto-menu-empresa-logada></pacto-menu-empresa-logada>

			<a
				*ngIf="!isUrlClubeDeBeneficios; else noHover"
				tabindex="-1"
				(click)="goToHome()"
				(mouseenter)="onMouseEnterSidebarCurrentModule()"
				(mouseleave)="onMouseLeaveSidebarCurrentModule()"
				[routerLink]="currentModule?.route?.internalLink"
				class="sidebar-current-module module-{{ currentModule?.id }} bg-{{
					currentModule?.baseColor
				}} show-back-button-{{ showBackMenuDescription }}"
				id="sidebar-current-module">
				<span *ngIf="!showBackMenuDescription">
					{{ "menu.modules." + currentModule?.id + ".name" | translate }}
				</span>
				<span *ngIf="showBackMenuDescription">Retornar ao início</span>
			</a>

			<ng-template #noHover>
				<a
					tabindex="-1"
					(click)="goToHome()"
					[routerLink]="currentModule?.route?.internalLink"
					class="sidebar-current-module module-{{ currentModule?.id }} bg-{{
						currentModule?.baseColor
					}} show-back-button-{{ showBackMenuDescription }}"
					id="sidebar-current-module">
					<span *ngIf="!showBackMenuDescription">
						{{ "menu.modules." + currentModule?.id + ".name" | translate }}
					</span>
					<span *ngIf="showBackMenuDescription">Retornar ao início</span>
				</a>
			</ng-template>

			<pacto-sidebar-navigation
				[currentModule]="currentModule"></pacto-sidebar-navigation>

			<pacto-system-info></pacto-system-info>
		</pacto-cat-sidebar>

		<pacto-cat-sidebar-content>
			<pacto-cat-topbar2 #topbarComponent tabindex="-1">
				<div class="menu-topbar-container">
					<div
						#triggerModules
						(click)="openMenuExplorar()"
						class="topbar-modules-toggle cor-azul-pacto-pri"
						id="topbar-modules-toggle">
						<i class="pct {{ openMenu ? 'pct-x' : 'pct-grid' }}"></i>
						<span>Explorar</span>
					</div>

					<pacto-topbar-search></pacto-topbar-search>

					<pacto-menu-topbar-actions></pacto-menu-topbar-actions>
					<pacto-modal-mkt></pacto-modal-mkt>
				</div>

				<pacto-menu-explorar
					[(opened)]="openMenu"
					[modules]="explorarModules"
					[style.top]="
						topbarComponent.elementRef.nativeElement.offsetHeight + 'px'
					"
					[triggerElement]="triggerModules"></pacto-menu-explorar>
			</pacto-cat-topbar2>
			<main
				cdkScrollable
				[ngClass]="{ 'pd-sidebar': isMobile }"
				class="menu-content bg-cinza-claro-pri">
				<router-outlet></router-outlet>
			</main>
		</pacto-cat-sidebar-content>
	</pacto-cat-sidebar-container>
	<ng-container *ngIf="fullScreen">
		<router-outlet></router-outlet>
	</ng-container>
</ng-container>
