<div class="blue-container">
	Perfil
	<div class="close-icon">
		<i class="pct pct-x"></i>
	</div>
</div>
<div class="user-container">
	<div class="text-container-perfil">
		<img
			(error)="trateAvatarError($event)"
			class="user-menu"
			src="{{ colaboradorAvatar }}" />
		<div class="text-perfil">
			<div class="text-perfil-colab">
				<div class="capt">
					{{ toLower(configuracao?.colaboradorNomeSimples) }}
				</div>
			</div>
			<div *ngIf="configuracao?.perfilAcessoAdm" class="text-perfil-item">
				<div class="capt">{{ toLower(configuracao?.perfilAcessoAdm) }}</div>
			</div>
			<div
				*ngIf="
					configuracao?.perfilAcessoTreino &&
					configuracao?.perfilAcessoAdm != configuracao?.perfilAcessoTreino
				"
				class="text-perfil-item">
				<div class="capt">{{ toLower(configuracao?.perfilAcessoTreino) }}</div>
			</div>
		</div>
	</div>

	<div *ngIf="!isUsuarioPactoSolucoes" class="text-action">
		<ng-container>
			<div
				(click)="closeHandler(); redirectToEditProfileLegado()"
				class="text-action-item">
				<span class="title-perfil">{{ "perfil.edit" | translate }}</span>
				<i class="pct pct-chevron-right seta-float" id="btn-perfil"></i>
			</div>
		</ng-container>
	</div>

	<div *ngIf="isPossuiIntegracaoZW()" class="text-action">
		<div
			(click)="closeHandler(); redirectToCanalCliente()"
			class="text-action-item"
			id="menu-cliente">
			<span class="title-perfil">{{ "perfil.canal" | translate }}</span>
			<i class="pct pct-chevron-right seta-float" id="btn-canal"></i>
		</div>
	</div>

	<div *ngIf="isExibirClubeDeBeneficios()" class="text-action">
		<div
			(click)="closeHandler(); redirectAdmHome()"
			class="text-action-item"
			id="menu-clube-de-beneficios">
			<span class="title-perfil">Clube de benefícios</span>
			<i
				class="pct pct-chevron-right seta-float"
				id="btn-clube-de-beneficios"></i>
		</div>
	</div>

	<div class="btn-row button-log-out-margin">
		<pacto-cat-button
			(click)="closeLogoutHandler()"
			[icon]="'log-out'"
			[label]="'perfil.sair' | translate"
			[type]="buttonType.OUTLINE"
			class="button-log-out"
			height="32px"
			iconPosition="after"
			id="menu-user-btn-edit-logout"
			type="OUTLINE"
			width="250px">
			<i class="pct pct-log-out"></i>
		</pacto-cat-button>
	</div>
</div>
