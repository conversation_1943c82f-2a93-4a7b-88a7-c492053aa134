import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { ApiResponseList } from "../../base/base.model";
import { BiMsApiBaseService } from "../../base/bi-ms-api-base.service";
import { BiMsApiModule } from "../../bi-ms-api.module";
import { ConfiguracaoBi } from "../../models/config-bi/config-bi.model";

@Injectable({
	providedIn: BiMsApiModule,
})
export class BiMsApiConfigBiService {
	constructor(private biMsApiBaseService: BiMsApiBaseService) {}

	findAllByEmpresa(
		empresaId?: number
	): Observable<ApiResponseList<ConfiguracaoBi>> {
		return this.biMsApiBaseService.get<ApiResponseList<ConfiguracaoBi>>(
			"v2-config-bi",
			{
				params: {
					empresaId: String(empresaId || 0),
				},
			}
		);
	}

	save(configs: Array<ConfiguracaoBi>) {
		return this.biMsApiBaseService.post<ApiResponseList<ConfiguracaoBi>>(
			"v2-config-bi",
			configs
		);
	}
}
