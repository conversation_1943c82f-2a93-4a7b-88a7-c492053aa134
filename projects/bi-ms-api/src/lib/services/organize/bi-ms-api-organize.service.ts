import { Injectable } from "@angular/core";
import { BiMsApiModule } from "../../bi-ms-api.module";
import { BiMsApiBaseService } from "../../base/bi-ms-api-base.service";
import { Observable } from "rxjs";
import { ConfiguracaoSistemaUsuario } from "../../models/configuracao-sistema-usuario/configuracao-sistema-usuario.model";
import { ApiResponseSingle } from "../../base/base.model";

@Injectable({
	providedIn: BiMsApiModule,
})
export class BiMsApiOrganizeService {
	constructor(private biMsApiBaseService: BiMsApiBaseService) {}

	public findByLoggedUser(): Observable<
		ApiResponseSingle<ConfiguracaoSistemaUsuario>
	> {
		return this.biMsApiBaseService
			.get<ApiResponseSingle<ConfiguracaoSistemaUsuario>>("/v2/organize")
			.pipe();
	}

	public save(
		configuracaoSistemaUsuario: ConfiguracaoSistemaUsuario
	): Observable<ApiResponseSingle<ConfiguracaoSistemaUsuario>> {
		return this.biMsApiBaseService.post<
			ApiResponseSingle<ConfiguracaoSistemaUsuario>
		>("/v2/organize", configuracaoSistemaUsuario);
	}
}
