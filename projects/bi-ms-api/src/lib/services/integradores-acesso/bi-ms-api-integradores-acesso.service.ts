import { Injectable } from "@angular/core";
import { throwError } from "rxjs";
import { map } from "rxjs/operators";
import { ApiResponseSingle } from "../../base/base.model";
import { BiMsApiBaseService } from "../../base/bi-ms-api-base.service";
import { BiMsApiModule } from "../../bi-ms-api.module";
import { FiltroBi } from "../../models/filtro-bi.model";
import { FiltroIntegradoresAcesso } from "../../models/integradores-acesso/filtro-integradores-acesso.model";
import { IntegradoresAcessoResponseModel } from "../../models/integradores-acesso/integradores-acesso-response.model";

@Injectable({
	providedIn: BiMsApiModule,
})
export class BiMsApiIntegradoresAcessoService {
	constructor(private biMsApiBaseService: BiMsApiBaseService) {}

	list(data: {
		filtroIntegradoresAcesso: FiltroIntegradoresAcesso;
		reloadFull?: boolean;
	}) {
		let reloadFull = "false";
		if (data.reloadFull) {
			reloadFull = data.reloadFull.toString();
		}
		return this.biMsApiBaseService
			.post<ApiResponseSingle<FiltroBi>>(
				"v2-integradores-acesso",
				data.filtroIntegradoresAcesso,
				{
					params: {
						reloadFull,
					},
				}
			)
			.pipe(
				map((response) => {
					try {
						return JSON.parse(
							response.content.jsonDados
						) as IntegradoresAcessoResponseModel;
					} catch (e) {
						console.error(
							`Não foi possível realizar a conversão dos dados: ${e}`
						);
						throwError(`Não foi possível realizar a conversão dos dados: ${e}`);
					}
				})
			);
	}
}
