export interface IntegradoresAcessoResponseModel {
	acessosPorDiaGympass: Map<number, number>;
	acessosPorDiaTotalPass: Map<number, number>;
	totalAlunosComIntegradoresAcesso: number;
	totalAcessoComIntegradoresAcesso: number;
	totalAcessosGympass: number;
	totalAcessoClienteGympass: Map<number, number>;
	totalAcessoClienteTotalpass: Map<number, number>;
	totalAcessosTotalPass: number;
	mediaAcessoFaixa1: number;
	mediaAcessoFaixa2: number;
	mediaAcessoFaixa3: number;
	mediaAcessoFaixa4: number;
	qtdAcessoGymPassInicioFaixa1: number;
	qtdAcessoGymPassInicioFaixa2: number;
	qtdAcessoGymPassInicioFaixa3: number;
	qtdAcessoGymPassInicioFaixa4: number;
	qtdAcessoGymPassFinalFaixa1: number;
	qtdAcessoGymPassFinalFaixa2: number;
	qtdAcessoGymPassFinalFaixa3: number;
	qtdAcessoGymPassFinalFaixa4: number;
	dadosGrafico: Array<DadosGraficoAcessoIntegradoresAcesso>;
}

export interface DadosGraficoAcessoIntegradoresAcesso {
	dia: number;
	qtdAcessos: number;
	tipoIntegrador: TipoIntegradorAcesso;
}

export enum TipoIntegradorAcesso {
	TODOS,
	GYMPASS,
	TOTALPASS,
}

export const isTipoIntegradorGympass = (tipoIntegrador: number) => {
	return (
		!tipoIntegrador ||
		tipoIntegrador === TipoIntegradorAcesso.TODOS ||
		tipoIntegrador === TipoIntegradorAcesso.GYMPASS
	);
};

export const isTipoIntegradorTotalpass = (tipoIntegrador: number) => {
	return (
		!tipoIntegrador ||
		tipoIntegrador === TipoIntegradorAcesso.TODOS ||
		tipoIntegrador === TipoIntegradorAcesso.TOTALPASS
	);
};
