import { FormControl } from "@angular/forms";
import { Empresa } from "../empresa/empresa.model";
import { Usuario } from "../usuario/usuario.model";

export interface ConfiguracaoBi {
	codigo: number;
	bi: BiType;
	configuracao: ConfiguracaoBiType;
	valor: string;
	valorAsBoolean: boolean;
	valorAsDouble: number;
	valorAsInteger: number;
	empresa: Empresa;
	empresaId: number;
	usuarioResponsavel: Usuario;
}

export interface BiType {
	codigo: number;
	name: string;
}

export interface ConfiguracaoBiType {
	bi: BiType;
	nome: string;
	name: string;
	codigo: number;
	valorPadrao: string;
	itens: Array<SelectItem>;
	formControlName: string;
	formControl: FormControl;
	tipo: ConfiguracaoTipoEnum;
}

export interface SelectItem {
	label: string;
	value: any;
}

export enum BiEnum {
	TICKET_MEDIO = 1,
	PENDENCIA = 2,
	GRUPO_RISCO = 3,
	INDICE_RENOVACAO = 4,
	CONVERSAO_VENDAS = 5,
	CONVERSAO_VENDAS_SS = 6,
	ROTATIVIDADE_CONTRATO = 7,
	METAS_FINANCEIRAS = 8,
	DCC = 9,
	CONTROLE_OPERACOES = 10,
	CONVITE_BI = 11,
	CLIENTES_VERIFICADOS = 12,
	AULA_EXPERIMENTAL = 13,
	GESTAO_ACESSO = 14,
	INADIMPLENCIA = 15,
	PROBABILIDADE_EVASAO = 16,
	LTV = 17,
	GYM_PASS = 18,
	RANKING_METAS_FINANCEIRAS = 19,
}

export enum ConfiguracaoTipoEnum {
	BOOLEAN,
	INTEGER,
	STRING,
	DOUBLE,
	COMBO,
	SENHA,
	MINUTOS,
	RADIO,
}

export enum ConfiguracaoBIEnum {
	INCLUIR_PRODUTOS_RECEITA = 0,
	INCLUIR_PRODUTOS_COMPETENCIA = 1,
	ALUNOS_BOLSA = 2,
	ATIVOS = 3,
	ASSINATURA_FOTO_ALUNO = 4,
	ASSINATURA_COMPROVANTE_ENDERECO = 5,
	ASSINATURA_DOCUMENTOS = 6,
	ASSINATURA_APTIDAO_FISICA = 7,
	VENDA_RAPIDA = 8,
	FONTE_RECEITA_DESPESA = 9,
	CONTAR_DEPENDENTES_COMO_PAGANTES = 10,
	FILTRAR_PARCELAS_VENCIDAS_NAO_PAGAS = 11,
	EXIBIR_AGREGADORES = 12,
	QTD_CHECKIN_CONSIDERAR_AGREGADOR = 13,
	QTD_DIAS_CONSIDERAR_AGREGADOR = 14,
	LTV_CONSIDERAR_LTV_REALIZADO = 15,
}

export function getConfigByBi(
	biCode: BiEnum,
	configuracoes: Array<ConfiguracaoBi>
) {
	if (!configuracoes) {
		return [];
	}
	return configuracoes.filter(
		(config) => config.bi && config.bi.codigo === biCode
	);
}
