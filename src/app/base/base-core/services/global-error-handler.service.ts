import { <PERSON>rror<PERSON>and<PERSON>, Injectable, Injector } from "@angular/core";
import { SessionService } from "../client/session.service";
import { GoogleCloudErrorReportingService } from "./google-cloud-error-reporting.service";

@Injectable()
export class GlobalErrorHandler implements ErrorHandler {
	constructor(private injector: Injector) {}

	handleError(error: any): void {
		// Log do erro no console (comportamento padrão)
		console.error("Global Error Handler:", error);

		this.handleChunkLoadError(error);
	}

	handleChunkLoadError(error: any) {
		if (!error) {
			return;
		}

		if (!error.message) {
			return;
		}

		const chunkFailedMessage = /ChunkLoadError/;

		if (chunkFailedMessage.test(error.message)) {
			console.warn("ChunkLoadError detected. Reloading app without cache...");
			window.location.replace(
				window.location.origin + window.location.pathname
			);
			return;
		}
	}

	handleErrorSendGoogleCloud(error: any): void {
		// Obter serviços usando o injector para evitar dependências circulares
		const googleCloudErrorReporting = this.injector.get(
			GoogleCloudErrorReportingService
		);
		const sessionService = this.injector.get(SessionService);

		// Preparar contexto do usuário
		const userContext = {
			user:
				sessionService.loggedUser.nome ||
				sessionService.loggedUser.email ||
				"Usuário não identificado",
			url: window.location.href,
			additionalInfo: {
				timestamp: new Date().toISOString(),
				userAgent: navigator.userAgent,
				sessionInfo: {
					empresaId: sessionService.empresaId,
					usuarioId: sessionService.loggedUser.id,
					modulosHabilitados: sessionService.modulosHabilitados,
				},
			},
		};

		// Reportar erro para Google Cloud Error Reporting
		if (this.shouldReportError(error)) {
			googleCloudErrorReporting
				.reportGenericError(error, userContext)
				.subscribe({
					next: () => {
						console.log(
							"Erro JavaScript reportado para Google Cloud Error Reporting"
						);
					},
					error: (reportError) => {
						console.warn(
							"Falha ao reportar erro JavaScript para Google Cloud:",
							reportError
						);
					},
				});
		}
	}

	private shouldReportError(error: any): boolean {
		// Não reporta erros de desenvolvimento ou erros muito comuns
		if (!error) {
			return false;
		}

		const errorMessage = error.message || error.toString();

		// Lista de erros que não devem ser reportados
		const ignoredErrors = [
			"Script error",
			"Non-Error promise rejection captured",
			"ResizeObserver loop limit exceeded",
			"Network request failed",
			"Loading chunk",
			"ChunkLoadError",
		];

		// Verifica se é um erro que deve ser ignorado
		const shouldIgnore = ignoredErrors.some((ignoredError) =>
			errorMessage.toLowerCase().includes(ignoredError.toLowerCase())
		);

		if (shouldIgnore) {
			return false;
		}

		// Reporta apenas em produção ou se for um erro crítico
		const isProduction =
			window.location.hostname !== "localhost" &&
			!window.location.hostname.includes("127.0.0.1");

		const isCriticalError =
			errorMessage.includes("TypeError") ||
			errorMessage.includes("ReferenceError") ||
			errorMessage.includes("Cannot read property") ||
			errorMessage.includes("Cannot read properties") ||
			errorMessage.includes("is not a function");

		return isProduction || isCriticalError;
	}
}
