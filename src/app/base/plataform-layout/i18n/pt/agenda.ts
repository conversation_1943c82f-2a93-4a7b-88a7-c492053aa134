import { NavModuleI18n } from "ui-kit";

export const agendaPt: NavModuleI18n = {
	tipoAgendamento: {
		name: "Tipos de Agendamento",
		description: "Configure os tipos de agendamento.",
	},
	indicadoresAgenda: {
		name: "Serviços Agendados",
		description: "Veja os serviços agendados.",
	},
	ambiente: {
		name: "Ambientes (Agenda)",
		description: "Configure os ambientes das aulas.",
	},
	"aula-excluida": {
		name: "<PERSON>las Excluídas",
		description: "Acompanhe as aulas excluídas.",
	},
	modalidade: {
		name: "<PERSON>dal<PERSON><PERSON> (Agenda)",
		description: "Gerencie suas modalidades.",
	},
	aulas: {
		name: "Configurar Aulas",
		description: "Programar as Aulas",
	},
	"agenda-cards": {
		name: "Agenda",
		description: "Agenda",
	},
	turmas: {
		name: "Configurar Turma<PERSON>",
		description: "Programar as Turmas",
	},
	disponibilidades: {
		name: "Configurar Disponibilidades",
		description: "Programar as Disponibilidades",
	},
	"agenda-aulas": {
		name: "Agenda de aulas",
		description: "Visualizar Aulas marcadas",
	},
	"agenda-servicos": {
		name: "Agenda de serviços",
		description: "Gerenciar serviços",
	},
	agendamentos: {
		name: "Agendamentos",
		description: "Relatorio dos serviços agendados",
	},
	"tv-aula": {
		name: "TV Aula",
		description: "Visualizar grade horária",
	},
	"tv-gestor": {
		name: "TV Gestor",
		description: "Acompanhe a movimentação das aulas",
	},
	dash: {
		name: "BI Agenda",
		description: "Dashboard da agenda",
	},
	dashboard: {
		name: "Business Intelligence",
		description: "Dashboard da agenda",
	},
	operacoes: {
		name: "Operações",
		description: "Operações da agenda",
	},
	relatorios: {
		name: "Relatórios",
		description: "Relatórios da agenda",
	},
	cadastros: {
		name: "Cadastros",
		description: "Cadastros da agenda",
	},
	"alunos-faltosos": {
		name: "Alunos faltosos",
		description: "Alunos faltosos",
	},
	"professores-substituidos": {
		name: "Professores substituidos",
		description: "Professores substituidos",
	},
	// disponibilidades: {
	// 	name: 'Configurar Disponibilidades',
	// 	description: 'Programar as Disponibilidades'
	// }
};
