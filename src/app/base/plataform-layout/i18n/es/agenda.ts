import { NavModuleI18n } from "ui-kit";

export const agendaEs: NavModuleI18n = {
	tipoAgendamento: {
		name: "Tipos de Agendamiento",
		description: "Configuración tipos de eventos.",
	},
	indicadoresAgenda: {
		name: "Serviços Agendados",
		description: "Veja os serviços agendados",
	},
	ambiente: {
		name: "Ambientes",
		description: "Administración de Ambientes registrados.",
	},
	modalidade: {
		name: "Modalidades",
		description: "Configuración tipo de aulas.",
	},
	aulas: {
		name: "Configurar Classe",
		description: "Programación de aulas.",
	},
	"agenda-aulas": {
		name: "Agenda de Classe",
		description: "Visualizar Aulas marcadas",
	},
	"agenda-servicos": {
		name: "Agenda de Servicios",
		description: "Gerenciar serviços",
	},
	agendamentos: {
		name: "Agendamentos",
		description: "Relatorio dos servicos agendados",
	},
	"tv-aula": {
		name: "TV Aula",
		description: "Visualizar grade Horária",
	},
	"tv-gestor": {
		name: "TV Gestor",
		description: "Acompanhe a movimentação das aulas",
	},
	dash: {
		name: "Dashboard",
		description: "Dashboard de agenda",
	},
	"alunos-faltosos": {
		name: "Alunos faltosos",
		description: "Alunos faltosos",
	},
	"professores-substituidos": {
		name: "Professores substituidos",
		description: "Professores substituidos",
	},
};
