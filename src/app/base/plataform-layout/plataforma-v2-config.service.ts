import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable, of, combineLatest } from "rxjs";
import { map, catchError, tap } from "rxjs/operators";

import { PlataformaConfigService, PlataformaMenuV2Config } from "ui-kit";
import { SessionService } from "src/app/base/base-core/client/session.service";
import { UsuarioBase } from "treino-api";
import { EmpresaImageSyncService } from "../configuracoes/services/empresa-image-sync.service";

@Injectable({
	providedIn: "root",
})
export class PlataformaV2ConfigService implements PlataformaConfigService {
	private ipCache: string;

	constructor(
		private sessionService: SessionService,
		private http: HttpClient,
		private empresaImageSync: EmpresaImageSyncService
	) {}

	getConfig(): Observable<PlataformaMenuV2Config> {
		return combineLatest([
			this.getIp(),
			this.empresaImageSync.empresaImage$,
		]).pipe(
			map(([ip, empresaImage]) => {
				return new PlataformaMenuV2Config({
					colaboradorNome: this.user
						? this.user.nome +
						  (this.user.perfis && this.user.perfis.length > 0
								? " (" + this.user.perfis[0] + ")"
								: "")
						: "",
					colaboradorNomeSimples: this.user ? this.user.nome : "",
					perfilAcessoTreino:
						this.user.perfis && this.user.perfis.length > 0
							? this.user.perfis[0]
							: "",
					colaboradorAvatarUrl: this.user ? this.user.imageUri : "",
					avatarRedeUrl: this.getAvatarRedeUrl(empresaImage),
					nomeUnidade: this.sessionService.currentEmpresa
						? this.sessionService.currentEmpresa.nome
						: "",
					multiUnidade: this.sessionService.multiUnidade,
					independente: !this.sessionService.integracaoZW,
					configurationUrl: "/config",
					colaboradorUrl: "/colaboradores/user/" + this.user.id,
					ip,
					pathUrlZw: this.sessionService.pathUrlZw,
					key: this.sessionService.getKey,
					codigoUsuarioZw: this.sessionService.codigoUsuarioZw,
					empresa: this.sessionService.codigoEmpresa,
					isUsuarioPacto: this.sessionService.isUsuarioPacto,
				});
			})
		);
	}

	getIp(): Observable<string> {
		if (this.ipCache) {
			return of(this.ipCache);
		} else {
			return this.http
				.get("https://www.cloudflare.com/cdn-cgi/trace", {
					responseType: "text",
				})
				.pipe(
					map((result) => {
						if (result) {
							const regex = /ip=([\d]*.[\d]*.[\d]*.[\d]*)/gm;
							const match = regex.exec(result);
							return match && match[1] ? match[1] : "0.0.0.0";
						} else {
							return "0.0.0.0";
						}
					}),
					catchError((erro) => {
						return of("0.0.0.0");
					}),
					tap((value) => {
						this.ipCache = value;
					})
				);
		}
	}

	get user(): UsuarioBase {
		return this.sessionService.loggedUser;
	}

	private getAvatarRedeUrl(empresaImage: string): string {
		if (empresaImage) {
			return this.empresaImageSync.convertToDataUrl(empresaImage);
		}
		return "./assets/images/empty-image.png";
	}
}
