import { Injectable } from "@angular/core";
import { BehaviorSubject, Observable } from "rxjs";
import { SessionService } from "@base-core/client/session.service";

@Injectable({
	providedIn: "root",
})
export class EmpresaImageSyncService {
	private empresaImageSubject = new BehaviorSubject<string>("");
	public empresaImage$ = this.empresaImageSubject.asObservable();

	constructor(private sessionService: SessionService) {
		// Inicializar com a imagem atual da empresa se existir
		this.initializeFromSession();
	}

	private initializeFromSession() {
		if (
			this.sessionService.currentEmpresa &&
			this.sessionService.currentEmpresa.fotoKey
		) {
			this.empresaImageSubject.next(this.sessionService.currentEmpresa.fotoKey);
		}
	}

	/**
	 * Atualiza a imagem da empresa em todos os locais necessários
	 * @param imageData - Dados da imagem (Base64 ou URL)
	 */
	updateEmpresaImage(imageData: string) {
		// Atualizar no SessionService
		if (this.sessionService.currentEmpresa) {
			this.sessionService.currentEmpresa.fotoKey = imageData || "";
		}

		// Notificar todos os observadores
		this.empresaImageSubject.next(imageData || "");
	}

	/**
	 * Converte Base64 para data URL se necessário
	 * @param base64Data - Dados em Base64 ou URL
	 * @returns URL válida para exibição
	 */
	convertToDataUrl(base64Data: string): string {
		if (!base64Data) {
			return "";
		}

		// Se já é uma URL completa, retorna como está
		if (base64Data.startsWith("http") || base64Data.startsWith("data:")) {
			return base64Data;
		}

		// Se é Base64 puro, converte para data URL
		if (base64Data.length > 0) {
			return `data:image/jpeg;base64,${base64Data}`;
		}

		return "";
	}

	/**
	 * Obtém a URL atual da imagem da empresa
	 * @returns URL da imagem ou fallback
	 */
	getCurrentImageUrl(): string {
		const currentImage = this.empresaImageSubject.value;
		if (currentImage) {
			return this.convertToDataUrl(currentImage);
		}
		return "./assets/images/empty-image.png";
	}

	/**
	 * Limpa a imagem da empresa
	 */
	clearEmpresaImage() {
		this.updateEmpresaImage("");
	}
}
