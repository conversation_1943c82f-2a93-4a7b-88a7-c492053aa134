export enum TipoIntegracaoEnum {
	// ADM
	MYWELLNESS = "MYWELLNESS",
	MENTOR_WEB = "MENTOR_WEB",
	ESTACIONAMENTO = "ESTACIONAMENTO",
	PARCEIRO_FIDELIDADE = "PARCEIRO_FIDELIDADE",
	CDL_SPC = "CDL_SPC",
	VITIO = "VITIO",
	DELSOFT = "DELSFOT",
	SESI_CONSULTA_OPERACOES = "SESI_CONSULTA_OPERACOES",
	API_SISTEMA_PACTO = "API_SISTEMA_PACTO",
	// CRM
	NOTIFICACAO_VIA_WEBHOOK = "NOTIFICACAO_VIA_WEBHOOK",
	AMIGO_FIT = "AMIGO_FIT",
	BOT_CONVERSA = "BOT_CONVERSA",
	GYMBOTPRO = "GYMBOTPRO",
	WEHELP = "WEHELP",
	WORDPRESS = "WORDPRESS",
	JOIN = "JOIN",
	GENERICA_LEADS = "GENERICA_LEADS",
	GENERICA_LEADS_GYMBOT = "GENERICA_LEADS_GYMBOT",
	BUZZLEAD = "BUZZLEAD",
	RD_STATION = "RD_STATION",
	HUBSPOT = "HUBSPOT",
	SMS = "SMS",
	GUPSHUP = "GUPSHUP",
	BITRIX24 = "BITRIX24",
	MANYCHAT = "MANYCHAT",
	// Financeiro
	F360_FATURAMENTO = "F360_FATURAMENTO",
	CONCILIADORA = "CONCILIADORA",
	SISTEMA_CONTABIL_ALTERDATA = "SISTEMA_CONTABIL_ALTERDATA",
	// TREINO
	BALANCA_DE_BIOIMPEDANCIA = "BALANCA_DE_BIOIMPEDANCIA",
	GYMPASS = "GYMPASS",
	GOGOOD = "GOGOOD",
	TOTALPASS = "TOTALPASS",
	MAPEAMENTO_DE_QUALIDADE_DE_VIDA = "MAPEAMENTO_DE_QUALIDADE_DE_VIDA",
	SPIVI = "SPIVI",
	METODOLOGIA_GUSTAVO_BORGES = "METODOLOGIA_GUSTAVO_BORGES",
	// PACTO PAY
	PJBANK = "PJBANK",
	PAGOLIVRE = "PAGOLIVRE",
	PLUGGY = "PLUGGY",
	FACILITEPAY = "FACILITEPAY",
	FACILITEPAY_RECURSOS = "FACILITEPAY_RECURSOS",
	NUVEMSHOP = "NUVEMSHOP",
	KOBANA = "KOBANA",
	SELFLOOPS = "SELFLOOPS",
	FOGUETE = "FOGUETE",
	ENVIO_ACESSO_PRATIQUE = "ENVIO_ACESSO_PRATIQUE",
	// SISTEMA SESC
	SESC_DF = "SESC_DF",
	WELLHUB = "WELLHUB",
}

export enum TipoVigenciaMyWellnessGymPassEnum {
	APENAS_NO_DIA = "Apenas um dia ao validar o token",
	QUANTIDADE_INFORMADA = "Por uma quantidade de dias a partir da validação do token",
	SEM_DATA_FINAL = "ASem data de expiração",
}
