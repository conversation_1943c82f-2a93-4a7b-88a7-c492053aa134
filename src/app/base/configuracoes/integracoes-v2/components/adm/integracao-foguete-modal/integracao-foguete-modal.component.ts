import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";

import { AdmCoreApiIntegracoesService } from "adm-core-api";
import { ProdutoApiProdutoService } from "produto-api";

@Component({
	selector: "pacto-integracao-foguete-modal",
	templateUrl: "./integracao-foguete-modal.component.html",
	styleUrls: ["./integracao-foguete-modal.component.scss"],
})
export class IntegracaoFogueteModalComponent implements OnInit {
	@Input() integracao: any;
	duracoesPlano: any[] = [];
	planoDuracoes: any[] = [];
	duracaoPlano: any;
	id;

	formGroup: FormGroup = new FormGroup({
		habilitada: new FormControl(false),
		tokenApi: new FormControl(""),
		urlApi: new FormControl(""),
		produto: new FormControl(""),
	});
	produtosOptions = new Array<any>();

	constructor(
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private activeModal: NgbActiveModal,
		private notify: SnotifyService,
		private produtoApiService: ProdutoApiProdutoService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		this.initProdutosOptions();
		this.formGroup.patchValue({
			habilitada: this.integracao.configuracao.habilitada,
			tokenApi: this.integracao.configuracao.tokenApi,
			urlApi: this.integracao.configuracao.urlApi,
			produto: { codigo: this.integracao.configuracao.produto, descricao: "" },
		});
		this.id = this.integracao.configuracao.codigo;
	}

	initProdutosOptions() {
		this.produtoApiService.findAllOnlyCodName().subscribe((response) => {
			this.produtosOptions = response.content;
			this.cd.detectChanges();
		});
	}

	fecharModal() {
		this.activeModal.close();
	}

	salvar() {
		this.integracao.configuracao.habilitada =
			this.formGroup.get("habilitada").value;
		this.integracao.configuracao.tokenApi =
			this.formGroup.get("tokenApi").value;
		this.integracao.configuracao.urlApi = this.formGroup.get("urlApi").value;
		this.integracao.configuracao.produto = this.formGroup.get("produto").value
			? this.formGroup.get("produto").value.codigo
			: null;
		this.integracao.configuracao.duracaoPlano = this.duracoesPlano
			? this.duracoesPlano
			: null;

		this.admCoreApiIntegracoes
			.salvarConfiguracaoIntegracaoFoguete(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notify.success("Integração salva com sucesso.");
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notify.error(err.meta.messageValue);
					} else {
						this.notify.error("Não foi possível salvar a integração.");
						console.log(err);
					}
				}
			);
	}

	updateConvenioConfiguracaoList(event?) {
		if (event) {
			this.duracoesPlano = event.duracoesPlano;
		}
	}
}
