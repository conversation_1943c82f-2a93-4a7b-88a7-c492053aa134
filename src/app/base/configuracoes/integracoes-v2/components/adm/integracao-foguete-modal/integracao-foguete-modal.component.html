<div class="main-content">
	<div class="row">
		<div class="col-md-6">
			<pacto-cat-form-input
				[control]="formGroup.get('tokenApi')"
				id="tokenAPi"
				label="Token Api"
				placeholder="Digite o Token Api"></pacto-cat-form-input>
		</div>
		<div class="col-ml-auto col-md-6" *ngIf="produtosOptions.length > 0">
			<pacto-cat-form-select-filter
				label="Produto"
				[addEmptyOption]="false"
				[control]="formGroup.get('produto')"
				[options]="produtosOptions"
				class="produto-select"
				i18n-label="@@integracao-foguete:label-produto"
				id="select-produto"
				idKey="codigo"
				labelKey="descricao"></pacto-cat-form-select-filter>
		</div>
	</div>

	<div class="table-wrapper pacto-shadow">
		<adm-table-foguete-duracao-config
			(afterEditAdd)="updateConvenioConfiguracaoList($event)"
			[duracaoPlano]="duracaoPlano"
			[idIntegracaoFoguete]="id"></adm-table-foguete-duracao-config>
	</div>

	<div class="row">
		<div class="col-md-12">
			<pacto-cat-form-input
				[control]="formGroup.get('urlApi')"
				id="urlApi"
				label="URL Api"
				placeholder="Digite a URL Api"></pacto-cat-form-input>
		</div>
	</div>
	<div class="btns">
		<div style="display: flex; gap: 8px; align-items: center">
			<pacto-cat-switch
				[control]="formGroup.get('habilitada')"
				id="habilitada-foguete"></pacto-cat-switch>
			<span style="margin-bottom: 0.5rem">Ativar/desativar integração</span>
		</div>
		<div>
			<pacto-cat-button
				(click)="fecharModal()"
				label="Fechar"
				size="LARGE"
				style="margin-right: 10px"
				type="SECONDARY"></pacto-cat-button>
			<pacto-cat-button
				(click)="salvar()"
				label="Salvar dados"
				size="LARGE"
				type="PRIMARY"></pacto-cat-button>
		</div>
	</div>
</div>
