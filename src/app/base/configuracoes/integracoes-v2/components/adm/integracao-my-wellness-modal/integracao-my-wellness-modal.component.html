<div class="main-content">
	<div class="row">
		<div class="col-md-5 mr-auto" style="margin-left: 0px">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('facilityUrl'))"
				i18n-label="@@integracao-mywellness:label-facilityurl"
				i18n-placeholder="
					@@integracao-mywellness:placeholder-digite-facilityurl"
				id="facility-url"
				label="FacilityUrl"
				placeholder="Digite a FacilityUrl"></pacto-cat-form-input>
		</div>
		<div class="col-md-5" style="margin-left: 0px">
			<pacto-cat-form-input
				[control]="toFormControl(formGroup.get('apiKey'))"
				i18n-label="@@integracao-mywellness:label-apikey"
				i18n-placeholder="@@integracao-mywellness:placeholder-digite-apikey"
				id="api-key"
				label="ApiKey"
				placeholder="Digite a <PERSON>"></pacto-cat-form-input>
		</div>
	</div>
	<div class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-select
				[control]="
					toFormControl(formGroup.get('tipoVigenciaMyWellnessGympass'))
				"
				[items]="tipoVigenciaOpcoes"
				i18n-label="@@integracao-mywellness:label-tipo-vigencia"
				id="tipo-vigencia-associacao"
				idKey="codigo"
				label="Tipo vigência associação para aluno WellHub"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
		<div class="col-md-5">
			<div class="row row-check-box" id="check-box-enviar-vinculos">
				<pacto-cat-checkbox
					[control]="toFormControl(formGroup.get('enviarVinculos'))"
					id="enviar-vinculos"
					label="Enviar vínculos dos alunos"></pacto-cat-checkbox>
			</div>
			<div class="row row-check-box" id="check-box-sincronizar">
				<pacto-cat-checkbox
					[control]="toFormControl(formGroup.get('enviarGrupos'))"
					id="sincronizar-grupos"
					label="Sincronizar grupos baseados na duração"></pacto-cat-checkbox>
			</div>
		</div>
	</div>
	<div *ngIf="exibirNrDiasVigencia()" class="row">
		<div class="col-md-5">
			<pacto-cat-form-input-number
				[formControl]="
					toFormControl(formGroup.get('nrDiasVigenciaMyWellnessGymPass'))
				"
				[max]="'9999'"
				id="dias-vingencia-associacao"
				label="Número de dias de vigência associação Gympass"
				placeholder="0"></pacto-cat-form-input-number>
		</div>
	</div>
	<div class="row description">
		<div class="col-md-12">
			<p>
				<span>Atenção!</span>
				É necessário adicionar um membro a staff do MyWellness com o email
				<EMAIL> (usuário já existente na plataforma) com a
				função de diretor. A integração será feita por esse membro.
			</p>
		</div>
	</div>
	<div class="row justify-content-end">
		<pacto-cat-button
			(click)="salvarIntegracao(integracao.ativa ? 'inativar' : 'ativar')"
			[label]="integracao.ativa ? 'Inativar' : 'Ativar'"
			i18n-label="@@integracoes:btn-ativar-inativar"
			id="btn-ativar"
			size="LARGE"
			style="margin-right: 15px"
			type="PRIMARY"
			width="90px"></pacto-cat-button>

		<pacto-cat-button
			(click)="salvarIntegracao()"
			i18n-label="@@integracao-mywellness:label-btn-salvar-alteracoes"
			id="btn-salvar"
			label="Salvar alterações"
			size="LARGE"
			style="margin-right: 15px"
			type="OUTLINE_GRAY"
			width="152px"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span
		i18n="@@integracoes:falha-salvar-integracao"
		xingling="falha-salvar-integracao">
		Falha ao tentar salvar integração!
	</span>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
</pacto-traducoes-xingling>
