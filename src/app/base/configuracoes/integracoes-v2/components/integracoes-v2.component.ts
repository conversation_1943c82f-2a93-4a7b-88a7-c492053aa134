import { ChangeDetector<PERSON>ef, Component, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { PactoModalSize } from "@base-core/modal/modal.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	AdmCoreApiConfiguracaoSistemaService,
	AdmCoreApiIntegracoesService,
	ConfiguracaoSistema,
	ConfiguracoesIntegracoes,
	Integracao,
} from "adm-core-api";
import {
	ConvenioCobranca,
	MsPactopayApiIntegracoesService,
} from "ms-pactopay-api";
import { SnotifyService } from "ng-snotify";
import { PactoApiTokenService } from "pacto-api";
import { isNullOrUndefinedOrEmpty } from "sdk";
import {
	ConfiguracoesIntegracoesLista,
	ConfiguracoesIntegracoesListaGoGood,
	ConfiguracoesIntegracoesListaMGB,
	ConfiguracoesIntegracoesListaMQV,
	ConfiguracoesIntegracoesListaTotalPass,
	ConfiguracoesIntegracoesListaSelfloops,
	TreinoApiEmpresaService,
} from "treino-api";
import { CatTabsTransparentComponent, DialogService } from "ui-kit";
import { TreinoConfigCacheService } from "../../configuration.service";
import { TipoIntegracaoEnum } from "../classes/integracoes-enums";
import { Modulo } from "../classes/modulos.enum";
import { IntegracaoCdlSpcComponent } from "./adm/integracao-cdl-spc/integracao-cdl-spc.component";
import { IntegracaoDelsoftModalComponent } from "./adm/integracao-delsoft-modal/integracao-delsoft-modal.component";
import { IntegracaoBalancaBioimpedanciaModalComponent } from "./treino/integracao-balanca-bioimpedancia-modal/integracao-balanca-bioimpedancia-modal.component";
import { IntegracaoGympassModalComponent } from "./treino/integracao-gympass-modal/integracao-gympass-modal.component";
import { IntegracaoMqvModalComponent } from "./treino/integracao-mqv-modal/integracao-mqv-modal.component";
import { IntegracaoMgbModalComponent } from "./treino/integracao-mgb-modal/integracao-mgb-modal.component";
import { IntegracaoSpiviModalComponent } from "./treino/integracao-spivi-modal/integracao-spivi-modal.component";
import { IntegracaoTotalpassModalComponent } from "./treino/integracao-totalpass-modal/integracao-totalpass-modal.component";
import { IntegracaoMyWellnessModalComponent } from "./adm/integracao-my-wellness-modal/integracao-my-wellness-modal.component";
import { IntegracaoEstacionamentoModalComponent } from "./adm/integracao-estacionamento-modal/integracao-estacionamento-modal.component";
import { IntegracaoMentorWebModalComponent } from "./adm/integracao-mentor-web-modal/integracao-mentor-web-modal.component";
import { IntegracaoSesiModalComponent } from "./adm/integracao-sesi-modal/integracao-sesi-modal.component";
import { IntegracaoVitioModalComponent } from "./adm/integracao-vitio-modal/integracao-vitio-modal.component";
import { IntegracaoAmigoFitModalComponent } from "./crm/integracao-amigo-fit-modal/integracao-amigo-fit-modal.component";
import { IntegracaoBitrix24LeadModalComponent } from "./crm/integracao-bitrix24-lead-modal/integracao-bitrix24-lead-modal.component";
import { IntegracaoBotConversaModalComponent } from "./crm/integracao-botconversa-modal/integracao-botconversa-modal.component";
import { IntegracaoGymbotProModalComponent } from "./crm/integracao-gymbotpro-modal/integracao-gymbotpro-modal.component";
import { IntegracaoBuzzLeadModalComponent } from "./crm/integracao-buzz-lead-modal/integracao-buzz-lead-modal.component";
import { IntegracaoGenericaLeadsModalComponent } from "./crm/integracao-generica-leads-modal/integracao-generica-leads-modal.component";
import { IntegracaoGupshupModalComponent } from "./crm/integracao-gupshup-modal/integracao-gupshup-modal.component";
import { IntegracaoHubspotModalComponent } from "./crm/integracao-hubspot-modal/integracao-hubspot-modal.component";
import { IntegracaoJoinModalComponent } from "./crm/integracao-join-modal/integracao-join-modal.component";
import { IntegracaoNotificacaoViaWebhookModalComponent } from "./crm/integracao-notificacao-via-webhook-modal/integracao-notificacao-via-webhook-modal.component";
import { IntegracaoRdStationModalComponent } from "./crm/integracao-rd-station-modal/integracao-rd-station-modal.component";
import { IntegracaoSmsModalComponent } from "./crm/integracao-sms-modal/integracao-sms-modal.component";
import { IntegracaoWeHelpModalComponent } from "./crm/integracao-we-help-modal/integracao-we-help-modal.component";
import { IntegracaoWordPressModalComponent } from "./crm/integracao-word-press-modal/integracao-word-press-modal.component";
import { EmpresaModalComponent } from "./empresa-modal/empresa-modal.component";
import { IntegracaoConciliadoraModalComponent } from "./financeiro/integracao-conciliadora-modal/integracao-conciliadora-modal.component";
import { IntegracaoF360ModalComponent } from "./financeiro/integracao-f360-modal/integracao-f360-modal.component";
import { IntegracaoPluggyModalComponent } from "./financeiro/integracao-pluggy-modal/integracao-pluggy-modal.component";
import { IntegracaoSistemaContabilAlterdataModalComponent } from "./financeiro/integracao-sistema-contabil-alterdata-modal/integracao-sistema-contabil-alterdata-modal.component";
import { IntegracaoPagoLivreModalComponent } from "./pactopay/integracao-pago-livre-modal/integracao-pago-livre-modal.component";
import { IntegracaoPjBankModalComponent } from "./pactopay/integracao-pj-bank-modal/integracao-pj-bank-modal.component";
import { IntegracaoRecursosFacilitepayComponent } from "./adm/integracao-recursos-facilitepay/integracao-recursos-facilitepay.component";
import { PlataformaModulo } from "../../../../microservices/client-discovery/client-discovery.model";
import { IntegracaoNuvemshopModalComponent } from "./adm/integracao-nuvemshop-modal/integracao-nuvemshop-modal.component";
import { IntegracaoGogoodModalComponent } from "./treino/integracao-gogood-modal/integracao-gogood-modal.component";
import { IntegracaoKobanaModalComponent } from "./pactopay/integracao-kobana-modal/integracao-kobana-modal.component";
import { NotifyChangesService } from "../services/notify-changes.service";
import { IntegracaoManychatComponent } from "./crm/integracao-manychat/integracao-manychat.component";
import { IntegracaoFogueteModalComponent } from "./adm/integracao-foguete-modal/integracao-foguete-modal.component";
import { IntegracaoSelfloopsModalComponent } from "./treino/integracao-selfloops-modal/integracao-selfloops-modal.component";
import { IntegracaoGenericaGymbotLeadsModalComponent } from "./crm/integracao-generica-leads-gymbot-modal/integracao-generica-leads-gymbot-modal.component";
import { IntegracaoEnvioAcessoPratiqueModalComponent } from "./adm/integracao-envio-acesso-pratique-modal/integracao-envio-acesso-pratique-modal.component";
import { IntegracaoSescDfModalComponent } from "./sesc/integracao-sesc-df-modal/integracao-sesc-df-modal.component";
import { PermissaoService } from "pacto-layout";

@Component({
	selector: "pacto-integracoes-v2",
	templateUrl: "./integracoes-v2.component.html",
	styleUrls: ["./integracoes-v2.component.scss"],
})
export class IntegracoesV2Component implements OnInit {
	@ViewChild("tabsMenus", { static: true })
	tabsMenus: CatTabsTransparentComponent;
	integracoes = [];
	integracoesModulo = [];
	moduloAtual = Modulo.TREINO;
	moduloAbaSelecionada: string;
	empresasTreino = new Array();
	empresaTreinoSelecionada: any;
	atualizandoDadosIntegracoes = true;
	falhaAtualizarDadosIntegracoes = true;
	itens: any;
	configuracoesIntegracoesEmpresasAdm: ConfiguracoesIntegracoes;
	configuracoesIntegracoesEmpresasTreino: any;
	tabIndexAnterior: number;
	integracoesEnum = TipoIntegracaoEnum;
	configuracaoSistema: ConfiguracaoSistema;
	notificator = this.notifyChangesService.subjectNotifier.subscribe(() => {
		this.consultarConfigsIntegracoesAdmCoreMs();
	});

	public readonly Modulo = Modulo;

	constructor(
		private router: Router,
		private modalService: DialogService,
		private ngbModal: NgbModal,
		public sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private admCoreApiIntegracoes: AdmCoreApiIntegracoesService,
		private configCache: TreinoConfigCacheService,
		private treinoApiEmpresaService: TreinoApiEmpresaService,
		private admCoreApiConfiguracaoSistema: AdmCoreApiConfiguracaoSistemaService,
		private msPactoPayApiIntegracoes: MsPactopayApiIntegracoesService,
		private notify: SnotifyService,
		private readonly pactoApiToken: PactoApiTokenService,
		private permissaoService: PermissaoService,
		private notifyChangesService: NotifyChangesService
	) {}

	ngOnInit() {
		this.tabIndexAnterior = this.tabsMenus.tabIndex;
		this.carregarIntegracoes();
		this.treinoApiEmpresaService.obterTodasEmpresas().subscribe(
			(response) => {
				this.empresasTreino = response;
				this.empresaTreinoSelecionada = this.getEmpresaAtual();
				if (this.possuiVariasUnidades()) {
					this.openModalAlterarEmpresa();
				} else {
					this.consultarConfigsIntegracoes();
				}
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notify.error(err.meta.messageValue);
				} else {
					this.notify.error("Falha ao obter Empresas! ");
				}
			}
		);
	}

	getEmpresaAtual() {
		let empresaAtual;
		const codigoEmpresaAtual = !isNullOrUndefinedOrEmpty(
			this.sessionService.empresaId
		)
			? parseInt(this.sessionService.empresaId, 10)
			: this.sessionService.currentEmpresa.codigo;
		for (const e of this.empresasTreino) {
			if (e.empresazw === codigoEmpresaAtual) {
				empresaAtual = e;
				break;
			}
		}
		return empresaAtual;
	}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	tabClickHandler(event: { index: number; previous: string; next: string }) {
		switch (event.next) {
			case Modulo.ADM:
				this.moduloAtual = Modulo.ADM;
				this.integracoesModulo = this.integracoes.filter(
					(i) => i.modulo === this.moduloAtual
				);
				this.tabIndexAnterior = 0;
				this.consultarConfigsIntegracoes();
				break;
			case Modulo.CRM:
				this.moduloAtual = Modulo.CRM;
				this.integracoesModulo = this.integracoes.filter(
					(i) => i.modulo === this.moduloAtual
				);
				this.tabIndexAnterior = 1;
				this.consultarConfigsIntegracoes();
				break;
			case Modulo.FINANCEIRO:
				this.moduloAtual = Modulo.FINANCEIRO;
				this.integracoesModulo = this.integracoes.filter(
					(i) => i.modulo === this.moduloAtual
				);
				this.tabIndexAnterior = 2;
				this.consultarConfigsIntegracoes();
				break;
			case Modulo.TREINO:
				this.moduloAtual = Modulo.TREINO;
				this.integracoesModulo = this.integracoes.filter(
					(i) => i.modulo === this.moduloAtual
				);
				this.tabIndexAnterior = 3;
				this.consultarConfigsIntegracoes();
				break;
			case Modulo.PACTO_PAY:
				this.moduloAtual = Modulo.PACTO_PAY;
				this.integracoesModulo = this.integracoes.filter(
					(i) => i.modulo === this.moduloAtual
				);
				this.tabsMenus.tabIndex = 4;
				this.consultarConfigsIntegracoes();
				break;
			case Modulo.FACILITE_PAY:
				this.moduloAtual = Modulo.FACILITE_PAY;
				this.integracoesModulo = this.integracoes.filter(
					(i) => i.modulo === this.moduloAtual
				);
				this.tabsMenus.tabIndex = 5;
				this.consultarConfigsIntegracoes();
				break;
			case Modulo.SISTEMA_SESC:
				this.moduloAtual = Modulo.SISTEMA_SESC;
				this.integracoesModulo = this.integracoes.filter(
					(i) => i.modulo === this.moduloAtual
				);
				this.tabsMenus.tabIndex = 5;
				this.consultarConfigsIntegracoes();
				break;
			default:
				this.tabsMenus.tabIndex = 0;
				this.tabIndexAnterior = 0;
				this.integracoesModulo = this.integracoes.filter(
					(i) => i.modulo === Modulo.ADM
				);
				break;
		}
	}

	isApresentarFacilitePay(): boolean {
		return this.getApresentarTab(Modulo.FACILITE_PAY);
	}

	isApresentarSistemaSesc(): boolean {
		return this.getApresentarTab(Modulo.SISTEMA_SESC);
	}

	getApresentarTab(modulo: Modulo): boolean {
		switch (modulo) {
			case Modulo.FACILITE_PAY:
				return this.sessionService.isModuloHabilitado(PlataformaModulo.FAC);
			case Modulo.SISTEMA_SESC:
				return this.permissaoService.temConfiguracaoSistemaAdm("sesc");
			default:
				return true;
		}
	}

	getApresentar(integracao): boolean {
		switch (integracao.id) {
			case TipoIntegracaoEnum.FACILITEPAY_RECURSOS:
				return this.isUsuarioPacto();
				break;
			case TipoIntegracaoEnum.PLUGGY:
				return (
					this.configuracoesIntegracoesEmpresasAdm
						.configuracaoIntegracaoRecursosFacilitePay !== undefined &&
					(this.configuracoesIntegracoesEmpresasAdm
						.configuracaoIntegracaoRecursosFacilitePay
						.concContasPagarFacilitePay ||
						this.configuracoesIntegracoesEmpresasAdm
							.configuracaoIntegracaoRecursosFacilitePay
							.concContasReceberFacilitePay)
				);
				break;
			case TipoIntegracaoEnum.CDL_SPC:
				return (
					this.configuracoesIntegracoesEmpresasAdm
						.configuracaoIntegracaoRecursosFacilitePay !== undefined &&
					this.configuracoesIntegracoesEmpresasAdm
						.configuracaoIntegracaoRecursosFacilitePay.facilitePayCDLSPC
				);
				break;
			default:
				return true;
				break;
		}
	}

	getApresentarSituacao(integracao): boolean {
		switch (integracao.id) {
			case TipoIntegracaoEnum.FACILITEPAY_RECURSOS:
				return false;
				break;
			case TipoIntegracaoEnum.GUPSHUP:
				return false;
				break;
			default:
				return true;
				break;
		}
	}

	openModalConfgIntegracao(integracao) {
		switch (integracao.id) {
			case TipoIntegracaoEnum.MYWELLNESS:
				this.openModalIntegracao(
					integracao,
					"Mywellness",
					IntegracaoMyWellnessModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.MENTOR_WEB:
				this.openModalIntegracao(
					integracao,
					"Mentor Web",
					IntegracaoMentorWebModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.ESTACIONAMENTO:
				this.openModalIntegracao(
					integracao,
					"Sistema de Estacionamento",
					IntegracaoEstacionamentoModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.VITIO:
				this.openModalIntegracao(
					integracao,
					"Vitio",
					IntegracaoVitioModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.CDL_SPC:
				this.openModalIntegracao(
					integracao,
					"CDL\\SPC",
					IntegracaoCdlSpcComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.DELSOFT:
				this.openModalIntegracao(
					integracao,
					"Delsoft",
					IntegracaoDelsoftModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.SESI_CONSULTA_OPERACOES:
				this.openModalOperacoes();
				break;
			case TipoIntegracaoEnum.NOTIFICACAO_VIA_WEBHOOK:
				this.openModalIntegracao(
					integracao,
					"Notificação via Webhook",
					IntegracaoNotificacaoViaWebhookModalComponent,
					"modal-integracao-notificacao-webhook"
				);
				break;
			case TipoIntegracaoEnum.AMIGO_FIT:
				this.openModalIntegracao(
					integracao,
					"Amigo Fit",
					IntegracaoAmigoFitModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.WEHELP:
				this.openModalIntegracao(
					integracao,
					"Wehelp",
					IntegracaoWeHelpModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.WORDPRESS:
				this.openModalIntegracao(
					integracao,
					"Wordpress",
					IntegracaoWordPressModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.JOIN:
				this.openModalIntegracao(
					integracao,
					"Join (Leads)",
					IntegracaoJoinModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.GENERICA_LEADS:
				this.openModalIntegracao(
					integracao,
					"Leads (Genérica)",
					IntegracaoGenericaLeadsModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.GENERICA_LEADS_GYMBOT:
				this.openModalIntegracao(
					integracao,
					"Leads (Genérica) GymBot Pro",
					IntegracaoGenericaGymbotLeadsModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.BUZZLEAD:
				this.openModalIntegracao(
					integracao,
					"Buzzlead",
					IntegracaoBuzzLeadModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.RD_STATION:
				this.openModalIntegracao(
					integracao,
					"RD Station",
					IntegracaoRdStationModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.HUBSPOT:
				this.openModalIntegracao(
					integracao,
					"Hubspot",
					IntegracaoHubspotModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.BOT_CONVERSA:
				this.openModalIntegracao(
					integracao,
					"GymBot",
					IntegracaoBotConversaModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.GYMBOTPRO:
				this.openModalIntegracao(
					integracao,
					"GymBotPro",
					IntegracaoGymbotProModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.SMS:
				this.openModalIntegracao(
					integracao,
					"SMS",
					IntegracaoSmsModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.GUPSHUP:
				this.openModalIntegracao(
					integracao,
					"GUPSHUP",
					IntegracaoGupshupModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.F360_FATURAMENTO:
				this.openModalIntegracao(
					integracao,
					"F360(Relatório faturamento recebido do dia anterior)",
					IntegracaoF360ModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.CONCILIADORA:
				this.openModalIntegracao(
					integracao,
					"Conciliadora",
					IntegracaoConciliadoraModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.SISTEMA_CONTABIL_ALTERDATA:
				this.openModalIntegracao(
					integracao,
					"Sistema contábil AlterData",
					IntegracaoSistemaContabilAlterdataModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.BALANCA_DE_BIOIMPEDANCIA:
				this.openModalIntegracao(
					integracao,
					"Balança de Bioimpedância",
					IntegracaoBalancaBioimpedanciaModalComponent,
					"modal-integracao-balanca-biopendancia"
				);
				break;
			case TipoIntegracaoEnum.GYMPASS:
				this.openModalIntegracao(
					integracao,
					"Wellhub",
					IntegracaoGympassModalComponent,
					"modal-integracao-gympass"
				);
				break;
			case TipoIntegracaoEnum.GOGOOD:
				this.openModalIntegracao(
					integracao,
					"Gogood",
					IntegracaoGogoodModalComponent,
					"modal-integracao-gogood"
				);
				break;
			case TipoIntegracaoEnum.MAPEAMENTO_DE_QUALIDADE_DE_VIDA:
				this.openModalIntegracao(
					integracao,
					"Mapeamento de Qualidade de Vida",
					IntegracaoMqvModalComponent,
					"modal-integracao-mqv"
				);
				break;
			case TipoIntegracaoEnum.SELFLOOPS:
				this.openModalIntegracao(
					integracao,
					"Selfloops",
					IntegracaoSelfloopsModalComponent,
					"modal-integracao-selfloops"
				);
				break;
			case TipoIntegracaoEnum.METODOLOGIA_GUSTAVO_BORGES:
				this.openModalIntegracao(
					integracao,
					"Metodologia Gustavo Borges",
					IntegracaoMgbModalComponent,
					"modal-integracao-mgb"
				);
				break;
			case TipoIntegracaoEnum.SPIVI:
				this.openModalIntegracao(
					integracao,
					"SPIVI",
					IntegracaoSpiviModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.PJBANK:
				this.openModalIntegracao(
					integracao,
					"PJBank",
					IntegracaoPjBankModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.PAGOLIVRE:
				this.openModalIntegracao(
					integracao,
					"PagoLivre",
					IntegracaoPagoLivreModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.TOTALPASS:
				this.openModalIntegracao(
					integracao,
					"Utilizar a integração com Totalpass",
					IntegracaoTotalpassModalComponent,
					"modal-integracao-totalpass"
				);
				break;
			case TipoIntegracaoEnum.PLUGGY:
				this.openModalIntegracao(
					integracao,
					"Conciliação Bancária",
					IntegracaoPluggyModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.API_SISTEMA_PACTO:
				this.router.navigate(["config", "integracoes-v2", "api-sistema-pacto"]);
				break;
			case TipoIntegracaoEnum.FACILITEPAY:
				this.openModalIntegracao(
					integracao,
					"Fypay",
					IntegracaoPagoLivreModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.FACILITEPAY_RECURSOS:
				this.openModalIntegracao(
					integracao,
					"Fypay - Recursos",
					IntegracaoRecursosFacilitepayComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.BITRIX24:
				console.log("BITIRX24");
				console.log(integracao);
				this.openModalIntegracao(
					integracao,
					"Integração Bitrix24",
					IntegracaoBitrix24LeadModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.NUVEMSHOP:
				this.openModalIntegracao(
					integracao,
					"NuvemShop",
					IntegracaoNuvemshopModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.KOBANA:
				this.openModalIntegracao(
					integracao,
					"Lote de Pagamentos",
					IntegracaoKobanaModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.MANYCHAT:
				this.openModalIntegracao(
					integracao,
					"ManyChat",
					IntegracaoManychatComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.FOGUETE:
				this.openModalIntegracao(
					integracao,
					"Foguete",
					IntegracaoFogueteModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.SESC_DF:
				this.openModalIntegracao(
					integracao,
					"SESC DF",
					IntegracaoSescDfModalComponent,
					"modal-integracao"
				);
				break;
			case TipoIntegracaoEnum.ENVIO_ACESSO_PRATIQUE:
				this.openModalIntegracao(
					integracao,
					"Envio de Acesso",
					IntegracaoEnvioAcessoPratiqueModalComponent,
					"modal-integracao"
				);
				break;
			default:
				break;
		}
	}

	openModalIntegracao(
		integracao: Integracao,
		title,
		modalComponent: any,
		windowClass: string
	) {
		if (
			integracao &&
			(integracao.configuracao ||
				this.configSeraConsultadaNoModal(integracao) ||
				integracao.id === TipoIntegracaoEnum.KOBANA)
		) {
			if (this.sessionService.empresas.length > 1 && integracao.configuracao) {
				if (
					integracao.configuracao.empresa ||
					integracao.id === TipoIntegracaoEnum.BOT_CONVERSA ||
					integracao.id === TipoIntegracaoEnum.GYMBOTPRO ||
					integracao.id === TipoIntegracaoEnum.BITRIX24
				) {
					title += ` (Empresa: ${this.empresaTreinoSelecionada.empresazw} - ${this.empresaTreinoSelecionada.nome})`;
				} else {
					title += ` (Todas as empresa)`;
				}
			}
			let modal = this.modalService.open(
				title,
				modalComponent,
				PactoModalSize.LARGE,
				windowClass
			);
			modal.componentInstance.integracao = integracao;
			modal.componentInstance.empresaSelecionada =
				this.empresaTreinoSelecionada;
			if (modal.result) {
				modal.result
					.then(() => {
						// Atualizar situacoes das integrações por modulo
						this.consultarConfigsIntegracoes();
						modal = undefined;
					})
					.catch(() => {});
			}
		} else {
			this.notify.error(
				"Falha ao consultar configuração da integração: " + title
			);
			const modal = this.modalService.open(
				title,
				modalComponent,
				PactoModalSize.LARGE,
				windowClass
			);
		}
	}

	configSeraConsultadaNoModal(integracao: Integracao): boolean {
		return integracao.id === TipoIntegracaoEnum.GUPSHUP;
	}

	openModalAlterarEmpresa() {
		this.atualizandoDadosIntegracoes = true;
		const modal = this.modalService.open(
			"Selecionar empresa",
			EmpresaModalComponent,
			PactoModalSize.LARGE,
			"modal-empresa"
		);
		modal.componentInstance.empresaSelecionada = this.empresaTreinoSelecionada;
		if (modal.result) {
			modal.result
				.then((value) => {
					this.empresaTreinoSelecionada = value;
					this.consultarConfigsIntegracoes();
				})
				.catch((error) => {
					console.log("Erro ao selecionar empresa");
				});
		}
	}

	openModalOperacoes() {
		const modalRef = this.ngbModal.open(IntegracaoSesiModalComponent, {
			windowClass: "modal-operacoes",
		});

		modalRef.componentInstance.configuracaoSistema = this.configuracaoSistema;
	}

	isUsuarioPacto(): boolean {
		return (
			this.sessionService.loggedUser.username.toUpperCase() === "PACTOBR" ||
			this.sessionService.loggedUser.username.toUpperCase() === "ADMIN"
		);
	}

	carregarIntegracoes() {
		this.integracoes = [
			{
				id: TipoIntegracaoEnum.MYWELLNESS,
				titulo: "Mywellness",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.ADM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.MENTOR_WEB,
				titulo: "Mentor Web",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.ADM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.ESTACIONAMENTO,
				titulo: "Sistema de Estacionamento",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.ADM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.VITIO,
				titulo: "Vitio",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.ADM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.DELSOFT,
				titulo: "Delsoft",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.ADM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.API_SISTEMA_PACTO,
				titulo: "API Sistema Pacto",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.ADM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.NUVEMSHOP,
				titulo: "NuvemShop",
				tituloModal: "Integração com API NuvemShop",
				ativa: false,
				modulo: Modulo.ADM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.FOGUETE,
				titulo: "Foguete",
				tituloModal: "Integração com API Foguete",
				ativa: false,
				modulo: Modulo.ADM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.ENVIO_ACESSO_PRATIQUE,
				titulo: "Envio de Acesso",
				tituloModal: "Integração com API de Recebimento de acesso",
				ativa: false,
				modulo: Modulo.ADM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.NOTIFICACAO_VIA_WEBHOOK,
				titulo: "Notificação via Webhook",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.AMIGO_FIT,
				titulo: "Amigo Fit",
				tituloModal: "",
				modulo: Modulo.CRM,
				ativa: false,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.WEHELP,
				titulo: "WeHelp",
				tituloModal: "",
				ativa: true,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.WORDPRESS,
				titulo: "WordPress",
				tituloModal: "",
				ativa: true,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.JOIN,
				titulo: "Join (Leads)",
				tituloModal: "",
				ativa: true,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.GENERICA_LEADS,
				titulo: "Leads (Genérica)",
				tituloModal: "",
				ativa: true,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.GENERICA_LEADS_GYMBOT,
				titulo: "Leads (Genérica) GymBot Pro",
				tituloModal: "",
				ativa: true,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.BUZZLEAD,
				titulo: "Buzz Lead",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.RD_STATION,
				titulo: "RD Station",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.HUBSPOT,
				titulo: "Hubspot (Leads)",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.BITRIX24,
				titulo: "Bitrix24 (Leads)",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			// {
			//     id: TipoIntegracaoEnum.GUPSHUP,
			//     titulo: 'Gupshup',
			//     tituloModal: '',
			//     ativa: false,
			//     modulo: Modulo.CRM,
			//     configuracao: undefined
			// },
			{
				id: TipoIntegracaoEnum.BOT_CONVERSA,
				titulo: "GymBot",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.GYMBOTPRO,
				titulo: "GymBot Pro",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.MANYCHAT,
				titulo: "ManyChat",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.CRM,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.BALANCA_DE_BIOIMPEDANCIA,
				titulo: "Balança de Bioimpedância",
				tituloModal: "Balança de Bioimpedância",
				ativa: false,
				modulo: Modulo.TREINO,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.GYMPASS,
				titulo: "Wellhub",
				tiuloModal: "Wellhub",
				ativa: false,
				modulo: Modulo.TREINO,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.GOGOOD,
				titulo: "Gogood",
				tiuloModal: "Gogood",
				ativa: false,
				modulo: Modulo.TREINO,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.MAPEAMENTO_DE_QUALIDADE_DE_VIDA,
				titulo: "Mapeamento de Qualidade de Vida",
				tiuloModal: "Mapeamento de Qualidade de Vida",
				ativa: false,
				modulo: Modulo.TREINO,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.SELFLOOPS,
				titulo: "Selfloops",
				tiuloModal: "Selfloops",
				ativa: false,
				modulo: Modulo.TREINO,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.METODOLOGIA_GUSTAVO_BORGES,
				titulo: "Metodologia Gustavo Borges",
				tituloModal: "Integração MGB",
				ativa: false,
				modulo: Modulo.TREINO,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.SPIVI,
				titulo: "SPIVI",
				tituloModal: "SPIVI",
				ativa: false,
				modulo: Modulo.TREINO,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.F360_FATURAMENTO,
				titulo: "F360(Relatório faturamento recebido do dia anterior)",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.FINANCEIRO,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.CONCILIADORA,
				titulo: "Conciliadora",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.FINANCEIRO,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.SISTEMA_CONTABIL_ALTERDATA,
				titulo: "Sistema contábil AlterData",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.FINANCEIRO,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.PJBANK,
				titulo: "PJBank",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.PACTO_PAY,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.PAGOLIVRE,
				titulo: "PagoLivre",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.PACTO_PAY,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.TOTALPASS,
				titulo: "TotalPass",
				tiuloModal: "Utilizar integração TotalPass",
				ativa: false,
				modulo: Modulo.TREINO,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.FACILITEPAY_RECURSOS,
				titulo: "Recursos",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.FACILITE_PAY,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.FACILITEPAY,
				titulo: "Fypay",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.FACILITE_PAY,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.PLUGGY,
				titulo: "Conciliação Bancária",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.FACILITE_PAY,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.CDL_SPC,
				titulo: "CDL\\SPC",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.FACILITE_PAY,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.KOBANA,
				titulo: "Lote de Pagamentos",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.FACILITE_PAY,
				configuracao: undefined,
			},
			{
				id: TipoIntegracaoEnum.SESC_DF,
				titulo: "SESC DF",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.SISTEMA_SESC,
				configuracao: undefined,
			},
		];
		if (
			this.sessionService.loggedUser.username.toUpperCase() === "PACTOBR" ||
			this.sessionService.loggedUser.username.toUpperCase() === "ADMIN"
		) {
			this.integracoes.push({
				id: TipoIntegracaoEnum.SMS,
				titulo: "SMS",
				tituloModal: "",
				ativa: false,
				modulo: Modulo.CRM,
				configuracao: undefined,
			});
		}
	}

	consultarConfigsIntegracoes() {
		if (isNullOrUndefinedOrEmpty(this.empresaTreinoSelecionada)) {
			return;
		}
		this.atualizandoDadosIntegracoes = true;
		if (
			this.moduloAtual === Modulo.ADM ||
			this.moduloAtual === Modulo.CRM ||
			this.moduloAtual === Modulo.FINANCEIRO ||
			this.moduloAtual === Modulo.TREINO ||
			this.moduloAtual === Modulo.FACILITE_PAY ||
			this.moduloAtual === Modulo.SISTEMA_SESC
		) {
			this.consultarConfigsIntegracoesAdmCoreMs();
		}
		if (
			this.moduloAtual === Modulo.PACTO_PAY ||
			this.moduloAtual === Modulo.FACILITE_PAY
		) {
			this.consultarConfigsIntegracoesPactoPayMs();
		}
	}

	atualizarIntegracoesAdm() {
		this.integracoes.forEach((integracao) => {
			switch (integracao.id) {
				case TipoIntegracaoEnum.MYWELLNESS: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoMyWellness.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoMyWellness;
					break;
				}
				case TipoIntegracaoEnum.MENTOR_WEB: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoMentorWeb.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoMentorWeb;
					break;
				}
				case TipoIntegracaoEnum.ESTACIONAMENTO: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoEstacionamento.utilizaSistemaEstacionamento;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoEstacionamento;
					break;
				}
				case TipoIntegracaoEnum.VITIO: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoVitio.usaVitio;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoVitio;
					break;
				}
				case TipoIntegracaoEnum.DELSOFT: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoDelsoft.utilizaIntegracaoDelsoft;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoDelsoft;
					break;
				}
				case TipoIntegracaoEnum.API_SISTEMA_PACTO: {
					const key = this.sessionService.chave;
					const companyId = this.sessionService.empresaId;
					this.pactoApiToken
						.obterTokens(key, Number(companyId))
						.subscribe((tokens) => {
							const temToken = tokens.content.length > 0;
							const temTokenAtivo = tokens.content.some((objeto) => {
								const dataDeVencimento = new Date(objeto.expiresAt);
								const hoje = new Date();

								dataDeVencimento.setHours(0, 0, 0, 0);
								hoje.setHours(0, 0, 0, 0);

								return dataDeVencimento >= hoje;
							});

							integracao.ativa = temToken && temTokenAtivo;
							this.cd.detectChanges();
						});
					break;
				}
				case TipoIntegracaoEnum.NUVEMSHOP: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoNuvemshop.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoNuvemshop;
					break;
				}
				case TipoIntegracaoEnum.FOGUETE: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoFoguete.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoFoguete;
					break;
				}
				case TipoIntegracaoEnum.ENVIO_ACESSO_PRATIQUE: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoEnvioAcessoPratique.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoEnvioAcessoPratique;
					break;
				}
			}
		});
		const index = this.integracoes.findIndex(
			(i) => i.id === TipoIntegracaoEnum.SESI_CONSULTA_OPERACOES
		);
		if (index === -1) {
			this.admCoreApiConfiguracaoSistema
				.getConfiguracaoSistema()
				.subscribe((responseConfigSistema) => {
					this.configuracaoSistema = responseConfigSistema;
					if (this.configuracaoSistema.utilizarServicoSesiSc) {
						const integracaoSesi = {
							id: TipoIntegracaoEnum.SESI_CONSULTA_OPERACOES,
							titulo: "Consulta operações integração SESI",
							tituloModal: "",
							ativa: true,
							modulo: Modulo.ADM,
							configuracao: undefined,
						};
						this.integracoes.push(integracaoSesi);
						this.integracoesModulo.push(integracaoSesi);
						this.cd.detectChanges();
					}
				});
		}
	}

	atualizarIntegracoesCRM() {
		this.integracoes.forEach((integracao) => {
			switch (integracao.id) {
				case TipoIntegracaoEnum.NOTIFICACAO_VIA_WEBHOOK: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoNotificacaoWebhook.notificarWebhook;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoNotificacaoWebhook;
					break;
				}
				case TipoIntegracaoEnum.AMIGO_FIT: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoAmigoFit.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoAmigoFit;
					break;
				}
				case TipoIntegracaoEnum.WEHELP: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoWeHelp.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoWeHelp;
					break;
				}
				case TipoIntegracaoEnum.BUZZLEAD: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoBuzzLead.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoBuzzLead;
					break;
				}
				case TipoIntegracaoEnum.SMS: {
					const configSms =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoSms;
					integracao.ativa =
						this.isNotNullOrUndefinedOrEmpty(configSms.tokenSMS) ||
						this.isNotNullOrUndefinedOrEmpty(configSms.tokenSMSShortCode);
					integracao.configuracao = configSms;
					break;
				}
				case TipoIntegracaoEnum.WORDPRESS: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoWordPress.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoWordPress;
					break;
				}
				case TipoIntegracaoEnum.JOIN: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoJoin.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoJoin;
					break;
				}
				case TipoIntegracaoEnum.GENERICA_LEADS: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoGenericaLeads.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoGenericaLeads;
					break;
				}
				case TipoIntegracaoEnum.GENERICA_LEADS_GYMBOT: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoGenericaLeadsGymbot.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoGenericaLeadsGymbot;
					break;
				}
				case TipoIntegracaoEnum.RD_STATION: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoRDStation.empresaUsaRd;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoRDStation;
					break;
				}
				case TipoIntegracaoEnum.HUBSPOT: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoHubSpot.empresaUsaHub;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoHubSpot;
					break;
				}
				case TipoIntegracaoEnum.BITRIX24: {
					const configBitrix =
						this.configuracoesIntegracoesEmpresasAdm
							.configuracaoEmpresaBitrix24;
					integracao.ativa = !this.isNotNullOrUndefinedOrEmpty(configBitrix)
						? configBitrix.habilitada
						: false;
					integracao.configuracao = configBitrix;
					break;
				}
				case TipoIntegracaoEnum.BOT_CONVERSA: {
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoBotConversa;
					integracao.ativa =
						integracao.configuracao === undefined
							? false
							: integracao.configuracao.filter((a) => a.ativo === true).length >
							  0;
					break;
				}
				case TipoIntegracaoEnum.GYMBOTPRO: {
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoGymbotPro;
					integracao.ativa =
						integracao.configuracao === undefined
							? false
							: integracao.configuracao.filter((a) => a.ativo === true).length >
							  0;
					break;
				}
				case TipoIntegracaoEnum.MANYCHAT: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoManyChat.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoManyChat;
					break;
				}
			}
		});
	}

	atualizarIntegracoesTreino() {
		const codigoEmpresaTreino = this.empresaTreinoSelecionada.id;
		const codigoEmpresaZw = this.empresaTreinoSelecionada.empresazw;
		this.integracoes.forEach((integracao) => {
			switch (integracao.id) {
				case TipoIntegracaoEnum.BALANCA_DE_BIOIMPEDANCIA: {
					integracao.ativa =
						this.configCache.configuracoesIntegracoes.usar_integracao_bioimpedancia;
					integracao.configuracao = {};
					break;
				}
				case TipoIntegracaoEnum.GYMPASS: {
					this.configuracoesIntegracoesEmpresasTreino =
						this.configCache.configuracoesIntegracoesLista;
					const integracaoGymPassConfigTreino: ConfiguracoesIntegracoesLista =
						this.configuracoesIntegracoesEmpresasTreino.filter(
							(i) => i.empresa === codigoEmpresaTreino
						)[0];
					const integracaoGymPassConfigAdm =
						this.configuracoesIntegracoesEmpresasAdm
							.configuracaoIntegracaoGympass;
					if (
						integracaoGymPassConfigTreino.usarGymPassBooking ||
						this.isNotNullOrUndefinedOrEmpty(
							integracaoGymPassConfigTreino.codigoGymPass
						) ||
						(this.isNotNullOrUndefinedOrEmpty(
							integracaoGymPassConfigAdm.codigoGympass
						) &&
							this.isNotNullOrUndefinedOrEmpty(
								integracaoGymPassConfigAdm.tokenApiGympass
							))
					) {
						integracao.ativa = true;
					} else {
						integracao.ativa = false;
					}
					integracao.configuracao = {
						integracaoGymPassConfigTreino,
						integracaoGymPassConfigAdm,
						empresa: { codigo: this.empresaTreinoSelecionada.empresazw },
					};
					break;
				}
				case TipoIntegracaoEnum.GOGOOD: {
					this.configuracoesIntegracoesEmpresasTreino =
						this.configCache.configuracoesIntegracoesListaGoGood;
					const integracaoGoGoodConfigTreino: ConfiguracoesIntegracoesListaGoGood =
						this.configuracoesIntegracoesEmpresasTreino.filter(
							(i) => i.empresa === codigoEmpresaTreino
						)[0];
					const integracaoGoGoodConfigAdm =
						this.configuracoesIntegracoesEmpresasAdm
							.configuracaoIntegracaoGoGood;
					if (
						(integracaoGoGoodConfigTreino &&
							this.isNotNullOrUndefinedOrEmpty(
								integracaoGoGoodConfigTreino.tokenAcademyGoGood
							)) ||
						this.isNotNullOrUndefinedOrEmpty(
							integracaoGoGoodConfigAdm.tokenAcademyGoGood
						)
					) {
						integracao.ativa = true;
					} else {
						integracao.ativa = false;
					}
					integracao.configuracao = {
						integracaoGoGoodConfigTreino,
						integracaoGoGoodConfigAdm,
						empresa: { codigo: this.empresaTreinoSelecionada.empresazw },
					};
					break;
				}
				case TipoIntegracaoEnum.METODOLOGIA_GUSTAVO_BORGES: {
					this.configuracoesIntegracoesEmpresasTreino =
						this.configCache.configuracoesIntegracoesListaMGB;
					// no dia deste ajuste i.empresa estava retornando o mesmo codigozw da empresa, por isso a alteração a seguir:
					const integracaoMGB: ConfiguracoesIntegracoesListaMGB =
						this.configuracoesIntegracoesEmpresasTreino.filter(
							(i) => i.empresa === codigoEmpresaZw
						)[0];
					integracao.ativa = this.isNotNullOrUndefinedOrEmpty(
						integracaoMGB.token
					);
					integracao.configuracao = integracaoMGB;
					break;
				}
				case TipoIntegracaoEnum.MAPEAMENTO_DE_QUALIDADE_DE_VIDA: {
					this.configuracoesIntegracoesEmpresasTreino =
						this.configCache.configuracoesIntegracoesListaMQV;
					const integracaoMQV: ConfiguracoesIntegracoesListaMQV =
						this.configuracoesIntegracoesEmpresasTreino.filter(
							(i) => i.empresa === codigoEmpresaTreino
						)[0];
					integracao.ativa = this.isNotNullOrUndefinedOrEmpty(
						integracaoMQV.token
					);
					integracao.configuracao = integracaoMQV;
					break;
				}
				case TipoIntegracaoEnum.SELFLOOPS: {
					this.configuracoesIntegracoesEmpresasTreino =
						this.configCache.configuracoesIntegracoesListaSelfloops;
					const integracaoSelfloops: ConfiguracoesIntegracoesListaSelfloops =
						this.configuracoesIntegracoesEmpresasTreino.filter(
							(i) => i.empresa === codigoEmpresaTreino
						)[0];
					integracao.ativa = this.isNotNullOrUndefinedOrEmpty(
						integracaoSelfloops.code
					);
					integracao.chave = this.sessionService.chave;
					integracao.empresaId = this.sessionService.empresaId;
					integracao.configuracao = integracaoSelfloops;
					break;
				}
				case TipoIntegracaoEnum.SPIVI: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoSpivi.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoSpivi;
					break;
				}
				case TipoIntegracaoEnum.TOTALPASS: {
					this.configuracoesIntegracoesEmpresasTreino =
						this.configCache.configuracoesIntegracoesListaTotalPass;
					const integracaoTotalPassConfig: ConfiguracoesIntegracoesListaTotalPass =
						this.configuracoesIntegracoesEmpresasTreino.filter(
							(i) => i.empresa === codigoEmpresaTreino
						)[0];
					const integracaoTotalPassConfigAdm =
						this.configuracoesIntegracoesEmpresasAdm
							.configuracaoIntegracaoTotalpass;
					if (integracaoTotalPassConfigAdm.inativo === true) {
						integracao.ativa = false;
					} else {
						integracao.ativa = true;
					}

					integracao.configuracao = { integracaoTotalPassConfigAdm };
					break;
				}
			}
		});
	}

	atualizarIntegracoesFinanceiro() {
		this.integracoes.forEach((integracao) => {
			switch (integracao.id) {
				case TipoIntegracaoEnum.F360_FATURAMENTO: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoF360Relatorio.habilitada;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoF360Relatorio;
					break;
				}
				case TipoIntegracaoEnum.CONCILIADORA: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoConciliadora.usarConciliadora;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoConciliadora;
					break;
				}
				case TipoIntegracaoEnum.SISTEMA_CONTABIL_ALTERDATA: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoSistemaContabilAlterData.habilitarExportacaoAlterData;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoSistemaContabilAlterData;
					break;
				}
			}
		});
	}

	atualizarIntegracoesFacilitePay() {
		this.integracoes.forEach((integracao) => {
			switch (integracao.id) {
				case TipoIntegracaoEnum.FACILITEPAY_RECURSOS: {
					integracao.ativa = true;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoRecursosFacilitePay;
					break;
				}
				case TipoIntegracaoEnum.PLUGGY: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm
							.configuracaoIntegracaoPluggy !== undefined &&
						this.configuracoesIntegracoesEmpresasAdm
							.configuracaoIntegracaoPluggy.length > 0;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoPluggy;
					break;
				}
				case TipoIntegracaoEnum.CDL_SPC: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoCDLSPC.consultarNovoCadastroSPC;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoCDLSPC;
					break;
				}
				case TipoIntegracaoEnum.KOBANA: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm
							.configuracaoIntegracaoKobana !== undefined &&
						this.configuracoesIntegracoesEmpresasAdm
							.configuracaoIntegracaoKobana !== null &&
						this.configuracoesIntegracoesEmpresasAdm
							.configuracaoIntegracaoKobana.ativo;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoKobana;
					break;
				}
			}
		});
	}

	atualizarIntegracoesSistemaSesc() {
		this.integracoes.forEach((integracao) => {
			switch (integracao.id) {
				case TipoIntegracaoEnum.SESC_DF: {
					integracao.ativa =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoSescDf.usarSescDf;
					integracao.configuracao =
						this.configuracoesIntegracoesEmpresasAdm.configuracaoIntegracaoSescDf;
					break;
				}
			}
		});
	}

	consultarConfigsIntegracoesAdmCoreMs() {
		this.admCoreApiIntegracoes
			.getConfiguracoesIntegracoes(
				this.empresaTreinoSelecionada.empresazw,
				this.moduloAtual
			)
			.subscribe(
				(response) => {
					this.configuracoesIntegracoesEmpresasAdm = response;
					switch (this.moduloAtual) {
						case Modulo.ADM: {
							this.atualizarIntegracoesAdm();
							break;
						}
						case Modulo.CRM: {
							this.atualizarIntegracoesCRM();
							break;
						}
						case Modulo.TREINO: {
							this.atualizarIntegracoesTreino();
							break;
						}
						case Modulo.FINANCEIRO: {
							this.atualizarIntegracoesFinanceiro();
							break;
						}
						case Modulo.FACILITE_PAY: {
							this.atualizarIntegracoesFacilitePay();
							break;
						}
						case Modulo.SISTEMA_SESC: {
							this.atualizarIntegracoesSistemaSesc();
							break;
						}
					}
					this.atualizandoDadosIntegracoes = false;
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notify.error(err.meta.messageValue);
					} else {
						this.notify.error("Falha ao consultar configurações integrações! ");
					}
				}
			);
	}

	consultarConfigsIntegracoesPactoPayMs() {
		this.msPactoPayApiIntegracoes
			.getConfiguracoesIntegracoes({
				codigoEmpresa: this.empresaTreinoSelecionada.empresazw,
			})
			.subscribe(
				(configuracoesIntegracoes) => {
					this.integracoes.forEach((integracao) => {
						switch (integracao.id) {
							case TipoIntegracaoEnum.PJBANK: {
								integracao.ativa =
									configuracoesIntegracoes.configuracaoIntegracaoPJBank.possuiConvenioAtivo;
								integracao.configuracao =
									configuracoesIntegracoes.configuracaoIntegracaoPJBank;
								if (
									isNullOrUndefinedOrEmpty(integracao.configuracao.convenios)
								) {
									integracao.configuracao.convenios =
										new Array<ConvenioCobranca>();
								}
								break;
							}
							case TipoIntegracaoEnum.PAGOLIVRE: {
								integracao.ativa =
									configuracoesIntegracoes.configuracaoIntegracaoPagoLivre.possuiConvenioAtivo;
								integracao.configuracao =
									configuracoesIntegracoes.configuracaoIntegracaoPagoLivre;
								if (
									isNullOrUndefinedOrEmpty(integracao.configuracao.convenios)
								) {
									integracao.configuracao.convenios =
										new Array<ConvenioCobranca>();
								}
								break;
							}
							case TipoIntegracaoEnum.FACILITEPAY: {
								integracao.ativa =
									configuracoesIntegracoes.configuracaoIntegracaoFacilitePay.possuiConvenioAtivo;
								integracao.configuracao =
									configuracoesIntegracoes.configuracaoIntegracaoFacilitePay;
								if (
									isNullOrUndefinedOrEmpty(integracao.configuracao.convenios)
								) {
									integracao.configuracao.convenios =
										new Array<ConvenioCobranca>();
								}
								break;
							}
						}
						this.atualizandoDadosIntegracoes = false;
						this.cd.detectChanges();
					});
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notify.error(err.meta.messageValue);
					} else {
						this.notify.error(
							"Falha ao consultar configurações integrações PactoPay! "
						);
					}
				}
			);
	}

	isNotNullOrUndefinedOrEmpty(value: any): boolean {
		return (
			value !== null &&
			typeof value !== "undefined" &&
			value !== "" &&
			value.length > 0
		);
	}

	possuiVariasUnidades(): boolean {
		return this.empresasTreino.length > 1;
	}
}
