import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";

import { SessionService } from "sdk";
import {
	PactoDataGridConfig,
	PactoDataGridState,
	RelatorioComponent,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
	DataFiltro,
} from "ui-kit";

import { AdmCoreApiIntegracoesService, Integracao } from "adm-core-api";
import { CrmApiGenericoService } from "crm-api";
import { ModalService } from "@base-core/modal/modal.service";

@Component({
	selector: "pacto-integracao-gymbotpro-modal",
	templateUrl: "./integracao-gymbotpro-modal.component.html",
	styleUrls: ["./integracao-gymbotpro-modal.component.scss"],
})
export class IntegracaoGymbotProModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("tableDataComponent", { static: true })
	tableDataComponent: RelatorioComponent;
	@ViewChild("columnAtivo", { static: true }) columnAtivo: TemplateRef<any>;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnDisponibilidade", { static: true })
	columnDisponibilidade: TemplateRef<any>;
	@ViewChild("columnFase", { static: true }) columnFase: TemplateRef<any>;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;
	@ViewChild("tooltipAtivar", { static: true }) tooltipAtivar;
	@ViewChild("inativarModalTitle", { static: true }) inativarModalTitle;
	@ViewChild("inativarModalBody", { static: true }) inativarModalBody;
	@ViewChild("inativarModalMsg", { static: true }) inativarModalMsg;
	@ViewChild("ativarModalTitle", { static: true }) ativarModalTitle;
	@ViewChild("ativarModalBody", { static: true }) ativarModalBody;
	@ViewChild("ativarModalMsg", { static: true }) ativarModalMsg;

	@Input() integracao: Integracao;
	@Input() empresaSelecionada: any;

	nomeIdentificador: String = "";

	formGroup: FormGroup = new FormGroup({
		codigo: new FormControl(),
		tipoFluxo: new FormControl(),
		acaoFaseCrm: new FormControl(),
		webhook: new FormControl(),
		descricaoFluxo: new FormControl(),
		idFluxo: new FormControl(),
		token: new FormControl(),
		ativo: new FormControl(),
	});
	situacao = [
		{ codigo: true, descricao: "Ativo" },
		{ codigo: false, descricao: "Inativo" },
	];
	tipoFluxolist = [
		{ codigo: 0, descricao: "Contato em grupo" },
		{ codigo: 1, descricao: "Fase do crm" },
		{ codigo: 2, descricao: "Tela do cliente" },
	];

	acaoFaseCrm = [
		{ codigo: "AG", descricao: "Agendamentos Presenciais" },
		{ codigo: "LA", descricao: "Agendados de Amanhã" },
		{ codigo: "RE", descricao: "Renovação" },
		{ codigo: "VA", descricao: "Visitantes Antigos" },
		{ codigo: "HO", descricao: "Visitantes 24h" },
		{ codigo: "PE", descricao: "Desistentes" },
		{ codigo: "EX", descricao: "Ex-Alunos" },
		{ codigo: "ID", descricao: "Indicações" },
		{ codigo: "CI", descricao: "Conversão de Indicados" },
		{ codigo: "CV", descricao: "Conversão de Agendados" },
		{ codigo: "CE", descricao: "Conversão de Ex-Alunos" },
		{ codigo: "CA", descricao: "Conversão de Visitantes Antigos" },
		{ codigo: "CD", descricao: "Conversão de Desistentes" },
		{ codigo: "CI", descricao: "Conversão de Receptivo" },
		{ codigo: "RI", descricao: "Grupo de Risco" },
		{ codigo: "VE", descricao: "Vencidos" },
		{ codigo: "PV", descricao: "Pós Venda" },
		{ codigo: "FA", descricao: "Faltosos" },
		{ codigo: "AN", descricao: "Aniversariantes" },
		{ codigo: "SE", descricao: "Últimas Sessões" },
		{ codigo: "SA", descricao: "Sessões sem agenda" },
		{ codigo: "AL", descricao: "Agendamentos de Ligações" },
		{ codigo: "IS", descricao: "Indicações sem Contato" },
		{ codigo: "CP", descricao: "Receptivo" },
		{ codigo: "GY", descricao: "Aluno WellHub" },
		{ codigo: "CR", descricao: "Meta Extra" },
		{ codigo: "LH", descricao: "Leads Hoje" },
		{ codigo: "LC", descricao: "Leads Acumuladas" },
		{ codigo: "VR", descricao: "Visitas recorrentes" },
		{ codigo: "CL", descricao: "Conversão de Lead" },
		{ codigo: "UG", descricao: "Últ. Acesso WellHub" },
	];

	constructor(
		private cd: ChangeDetectorRef,
		public restService: RestService,
		public admCoreApiIntegracoesService: AdmCoreApiIntegracoesService,
		public crmApiGenericoService: CrmApiGenericoService,
		private modalService: ModalService,
		public sessionService: SessionService,
		private notificationService: SnotifyService,
		private activeModal: NgbActiveModal
	) {}

	listGymbotPros = new Array<any>();
	tableData: PactoDataGridConfig;
	gymbotprolist = new Array<any>();
	gymbotproData: {
		content: Array<any>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<any>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	page = 1;
	size = 5;
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 15, label: "15" },
	];
	state: PactoDataGridState = new PactoDataGridState();

	ngOnInit() {
		this.initTableData();
		let count = 1;
		if (this.integracao.configuracao) {
			console.log("meus logs" + this.integracao.configuracao);
			this.integracao.configuracao.forEach((i) => {
				const fase = this.acaoFaseCrm.find((p) => p.codigo === i.fase);
				const itemGymbotPro = {
					codigo: i.codigo,
					ativo: this.situacao.find((p) => p.codigo === i.ativo).descricao,
					descricao: i.descricao,
					disponibilidade: this.tipoFluxolist[i.tipoFluxo].descricao,
					fase: fase == null ? "-" : fase.descricao,
					cdigoFase: fase == null ? "" : fase.codigo,
					hook: i.urlWebHooGymbotPro,
					tipoFluxo: i.tipoFluxo,
					idFluxo: i.idFluxo,
					token: i.token,
				};
				this.gymbotprolist.push(itemGymbotPro);
				const qtdGymbotpro = this.integracao.configuracao;
				this.sortList(this.gymbotprolist, "codigo", "ASC");
				this.createGymbotproPageObject(
					this.page,
					this.size,
					count === qtdGymbotpro
				);
				count++;
			});
		} else {
			this.notificationService.error("Falha ao obter configuração GymbotPro!");
		}
	}

	isFaseCrm() {
		if (this.formGroup.get("tipoFluxo").value == 0) {
			this.formGroup.get("acaoFaseCrm").setValue(null);
		}
		return this.formGroup.get("tipoFluxo").value == 1;
	}

	ordenarGymbotpro(eventSort) {
		this.gymbotprolist = this.sortList(
			this.gymbotprolist,
			eventSort.columnName,
			eventSort.direction
		);
		this.createGymbotproPageObject(this.page, this.size, true);
	}

	removerdaListaGymbotpro(event) {
		const index = this.gymbotprolist.findIndex(
			(p) => p.codigo === event.row.codigo
		);
		this.gymbotprolist.splice(index, 1);
		this.createGymbotproPageObject(this.page, this.size, true);
	}

	createGymbotproPageObject(page = 1, size = 5, reloadData = true) {
		this.gymbotproData.totalElements = this.gymbotprolist.length;
		this.gymbotproData.size = size;
		this.gymbotproData.totalPages = Math.ceil(
			+(this.gymbotproData.totalElements / this.gymbotproData.size)
		);
		this.gymbotproData.first = page === 0 || page === 1;
		this.gymbotproData.last = page === this.gymbotproData.totalPages;
		this.gymbotproData.content = this.gymbotprolist.slice(
			size * page - size,
			size * page
		);
		this.tableDataComponent.showBtnAdd = false;
		if (reloadData) {
			this.tableDataComponent.reloadData();
		}
		this.tableDataComponent.ngbPage = this.page;
	}

	pageChangeEvent(page) {
		if (!isNaN(page)) {
			this.page = page;
		}
		this.createGymbotproPageObject(this.page, this.size, true);
	}

	pageSizeChange(size) {
		if (!isNaN(size)) {
			this.size = size;
			this.page = 1;
		}
		this.createGymbotproPageObject(this.page, this.size, true);
	}

	editarSituacaoHandler(item) {
		this.nomeIdentificador = item.row.descricao;
		if (item.row.ativo == "Inativo" && item.row.tipoFluxo != 0) {
			const verificaDuplicidade = this.gymbotprolist.find(
				(p) =>
					p.tipoFluxo == item.row.tipoFluxo &&
					p.ativo == "Ativo" &&
					(item.row.tipoFluxo != 1 || p.fase === item.row.fase)
			);
			if (verificaDuplicidade) {
				this.notificationService.error(
					"Já exite um Identificador ativo para está fase!"
				);
				return;
			}
		}

		setTimeout(() => {
			let modalTitle = "";
			let modalBody = "";
			let modalButton = "";
			let modalMsg = "";
			if (item.row.ativo == "Ativo") {
				modalTitle = this.inativarModalTitle.nativeElement.innerHTML;
				modalBody = this.inativarModalBody.nativeElement.innerHTML;
				modalButton = this.tooltipInativar.nativeElement.innerHTML;
				modalMsg = this.inativarModalMsg.nativeElement.innerHTML;
			} else {
				modalTitle = this.ativarModalTitle.nativeElement.innerHTML;
				modalBody = this.ativarModalBody.nativeElement.innerHTML;
				modalButton = this.tooltipAtivar.nativeElement.innerHTML;
				modalMsg = this.ativarModalMsg.nativeElement.innerHTML;
			}
			const handler = this.modalService.confirm(
				modalTitle,
				modalBody,
				modalButton
			);
			handler.result.then(() => {
				const atividade = { ativa: item.ativa };
				const codigo: number = item.row.codigo;
				this.admCoreApiIntegracoesService
					.inativarOuAtivarConfiguracaoIntegracaoGymbotPro(codigo)
					.subscribe(
						(ret) => {
							if (ret && ret.content === "OK") {
								if (this.gymbotprolist[index].ativo == "Ativo") {
									this.notificationService.success("Ativado com sucesso.");
								} else {
									this.notificationService.success("Inativado com sucesso.");
								}
							} else {
								this.notificationService.error(
									"Fluxo gymbotpro sendo utilizado. Código do(s) contato(s) em grupo: " +
										ret.content
								);
							}
						},
						(httpErrorResponse) => {
							const err = httpErrorResponse.error;
							if (err.meta && err.meta.messageValue) {
								this.notificationService.error(
									this.traducao.getLabel("error-ao-salvar")
								);
							}
						}
					);

				const index = this.gymbotprolist.findIndex(
					(p) => p.codigo === item.row.codigo
				);
				this.gymbotprolist[index].ativo =
					this.gymbotprolist[index].ativo == "Ativo" ? "Inativo" : "Ativo";
				this.createGymbotproPageObject(this.page, this.size, true);
			});
		});
	}

	sortList(
		list: Array<any>,
		columnName: string,
		direction: string
	): Array<any> {
		list = list.sort((a, b) => {
			if (direction === "ASC") {
				if (a[columnName] > b[columnName]) {
					return 1;
				} else if (a[columnName] < b[columnName]) {
					return -1;
				} else {
					return 0;
				}
			} else {
				if (a[columnName] < b[columnName]) {
					return 1;
				} else if (a[columnName] > b[columnName]) {
					return -1;
				} else {
					return 0;
				}
			}
		});
		return list;
	}

	timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	getAditionalFiltersUsuariosMeta() {
		return { codigoEmpresa: this.empresaSelecionada.empresazw };
	}

	removerConfig(item: any) {
		this.admCoreApiIntegracoesService
			.removerConfiguracaoIntegracaoGymbotPro(item.row.codigo)
			.subscribe(
				(ret) => {
					if (ret && ret.content === "OK") {
						this.removerdaListaGymbotpro(item);
						this.notificationService.success("Removido com sucesso.");
					} else {
						this.notificationService.error(ret.content);
					}
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(
							this.traducao.getLabel("error-ao-salvar")
						);
					}
				}
			);
	}

	salvarIntegracao() {
		this.salvar("salva-com-sucesso");
	}

	salvar(labelMensagemSucesso?: string) {
		this.listGymbotPros = [];
		this.gymbotprolist.forEach((i) => {
			const itemGymbotPro = {
				codigo: i.codigo,
				ativo: i.ativo == "Ativo" ? true : false,
				descricao: i.descricao,
				tipoFluxo: i.tipoFluxo,
				empresa: this.empresaSelecionada.empresazw,
				fase: i.cdigoFase,
				token: i.token,
				idFluxo: i.idFluxo,
			};
			this.listGymbotPros.push(itemGymbotPro);
			this.integracao.configuracao = this.listGymbotPros;
		});
		this.admCoreApiIntegracoesService
			.salvarConfiguracaoIntegracaoGymbotPro(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notificationService.success(
						this.traducao.getLabel(labelMensagemSucesso)
					);
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(
							this.traducao.getLabel("error-ao-salvar")
						);
					}
				}
			);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	initTableData() {
		this.state.paginaTamanho = 5;
		this.state.paginaNumero = 0;
		this.tableData = new PactoDataGridConfig({
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			dataAdapterFn: (serverData) => {
				return this.gymbotproData;
			},
			pagination: true,
			state: this.state,
			columns: [
				{
					nome: "ativo",
					titulo: this.columnAtivo,
					visible: true,
					ordenavel: true,
					width: "8%",
					styleClass: "left",
				},
				{
					nome: "codigo",
					titulo: this.columnCodigo,
					visible: true,
					ordenavel: true,
					width: "10%",
				},
				{
					nome: "descricao",
					titulo: this.columnDescricao,
					visible: true,
					ordenavel: true,
					width: "24%",
				},
				{
					nome: "disponibilidade",
					titulo: this.columnDisponibilidade,
					visible: true,
					ordenavel: true,
					width: "24%",
				},
				{
					nome: "fase",
					titulo: this.columnFase,
					visible: true,
					ordenavel: true,
					width: "20%",
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil margin-right-5",
					tooltipText: "Editar gymbotpro",
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o margin-right-5",
					tooltipText: "Remover gymbotpro",
				},
				{
					nome: "inative",
					iconClass: "pct pct-eye-off",
					tooltipText: "Inativar/Ativar",
				},
			],
		});
		this.tableDataComponent.pageSizeControl.setValue(5);
	}

	montarEdicao(item: any) {
		this.formGroup.get("codigo").setValue(item.row.codigo);
		this.formGroup.get("descricaoFluxo").setValue(item.row.descricao);
		this.formGroup.get("idFluxo").setValue(item.row.idFluxo);
		this.formGroup.get("token").setValue(item.row.token);
		this.formGroup.get("tipoFluxo").setValue(item.row.tipoFluxo);
		if (this.formGroup.get("tipoFluxo").value == 1) {
			this.formGroup.get("acaoFaseCrm").setValue(item.row.cdigoFase);
		}
		this.removerdaListaGymbotpro(item);
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.montarEdicao($event);
		} else if ($event.iconName === "remove") {
			if ($event.row.codigo) {
				this.removerConfig($event);
			} else {
				this.removerdaListaGymbotpro($event);
			}
		} else if ($event.iconName === "inative") {
			this.editarSituacaoHandler($event);
		}
	}

	adicionarGymbotpro() {
		if (
			!(
				this.formGroup.get("descricaoFluxo").value === null ||
				this.formGroup.get("descricaoFluxo").value.trim() === ""
			) &&
			!(this.formGroup.get("tipoFluxo").value === null) &&
			!(
				this.formGroup.get("token").value === null ||
				this.formGroup.get("token").value.trim() === ""
			) &&
			!(
				this.formGroup.get("idFluxo").value === null ||
				this.formGroup.get("idFluxo").value.trim() === ""
			)
		) {
			if (
				this.formGroup.get("tipoFluxo").value == 1 &&
				this.formGroup.get("acaoFaseCrm").value === null
			) {
				this.notificationService.error(
					'O campo: "Fase do Crm" deve ser informado.'
				);
			} else {
				const tipoFluxo = this.formGroup.get("tipoFluxo").value;
				const fase = this.acaoFaseCrm.find(
					(p) => p.codigo === this.formGroup.get("acaoFaseCrm").value
				);
				this.sortList(this.gymbotprolist, "codigo", "DESC");
				const fluxo = {
					codigo: this.formGroup.get("codigo").value,
					ativo: "Ativo",
					descricao: this.formGroup.get("descricaoFluxo").value,
					disponibilidade: this.tipoFluxolist[tipoFluxo].descricao,
					fase: fase == null ? "-" : fase.descricao,
					cdigoFase: fase == null ? "" : fase.codigo,
					idFluxo: this.formGroup.get("idFluxo").value,
					token: this.formGroup.get("token").value,
					tipoFluxo,
				};

				if (tipoFluxo !== null && tipoFluxo !== undefined) {
					const indexFase = this.gymbotprolist.findIndex(
						(p) => p.cdigoFase === fluxo.cdigoFase && p.ativo == "Ativo"
					);
					const indexNome = this.gymbotprolist.findIndex(
						(p) => p.descricao === fluxo.descricao && p.ativo == "Ativo"
					);

					if (tipoFluxo == 2) {
						const fluxoTipo2 = this.gymbotprolist.find(
							(p) => p.tipoFluxo == 2 && p.ativo == "Ativo"
						);
						if (fluxoTipo2) {
							this.notificationService.warning(
								"Já existe um fluxo do tipo Tela do Cliente configurado."
							);
							return; // Não permite criar outro fluxo tipo 2
						}
					}

					if (
						(tipoFluxo == 1 && indexFase === -1 && indexNome === -1) ||
						(tipoFluxo == 0 && indexNome === -1) ||
						tipoFluxo == 2
					) {
						this.gymbotprolist.push(fluxo);
						this.formGroup.get("tipoFluxo").setValue(undefined);
						this.sortList(this.gymbotprolist, "codigo", "DESC");
						this.createGymbotproPageObject(this.page, this.size, true);
					} else {
						this.notificationService.warning(
							"Já existe um fluxo para esta mesma " +
								(indexFase !== -1 && indexNome === -1
									? "fase"
									: indexNome !== -1 && indexFase === -1
									? "descrição"
									: indexNome !== -1 && tipoFluxo === 1 && indexFase !== -1
									? "fase e descrição"
									: "configuração")
						);
					}
					this.formGroup.reset();
				}
			}
		} else {
			this.notificationService.error("Campo obrigatório não preenchido.");
		}
	}
}
