<div class="main-content">
	<pacto-cat-tabs-transparent
		#tabsMenus
		(activateTab)="tabClickHandler($event)"
		[actionIcon]="'pct pct-refresh-ccw'"
		[actionLabel]="'Alterar empresa'"
		[tabIndex]="0"
		id="menus-modulos">
		<ng-template
			label="Configuração WellHub"
			pactoTabTransparent="gympass"></ng-template>
		<ng-template
			label="Configuração Pacto"
			pactoTabTransparent="pacto"></ng-template>
	</pacto-cat-tabs-transparent>

	<div *ngIf="selectedTab === 'gympass1'" class="pendente">
		<i class="pct pct-alert-triangle"></i>
		<span>Integração em aguardo de aprovação pela WellHub</span>
	</div>

	<div *ngIf="selectedTab === 'pacto'" class="pendente">
		<i class="pct pct-alert-triangle"></i>
		<span>
			As configurações dessa aba serão utilizadas apenas dentro do sistema Pacto
		</span>
	</div>

	<div *ngIf="selectedTab === 'gympass'" class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input
				[control]="formGroup.get('gymId')"
				i18n-label="@@integracao-gympass:label-gymid"
				i18n-placeholder="@@integracao-gympass:placeholder-gymid"
				id="gym-id"
				label="Gym ID"
				placeholder="Digite o GYMID"></pacto-cat-form-input>
		</div>
		<div class="col-md-5 mr-auto"></div>
	</div>
	<div *ngIf="selectedTab === 'pacto'" class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-form-input-number
				[formControl]="formGroup.get('limiteDeAcessosPorDia')"
				label="Limite de acessos por dia"
				placeholder="0"></pacto-cat-form-input-number>
		</div>
		<div class="col-md-5">
			<pacto-cat-form-input-number
				[formControl]="formGroup.get('limiteDeAulasPorDia')"
				label="Limite de aulas por dia"
				placeholder="0"></pacto-cat-form-input-number>
		</div>
	</div>

	<div *ngIf="selectedTab === 'pacto'" class="row">
		<div class="col-md-5 mr-auto">
			<pacto-cat-checkbox
				[control]="formGroup.get('permitirWod')"
				[label]="'Permitir WellHub executar o WOD '"></pacto-cat-checkbox>
		</div>
		<div class="col-md-5"></div>
	</div>
	<div class="row justify-content-end btns-ativar-inativar">
		<pacto-cat-button
			(click)="salvar()"
			i18n-label="@@integracao-parceiro-fidelidade:label-btn-excluir-tabela"
			id="btn-salvar"
			label="Salvar alterações"
			size="LARGE"
			style="margin-right: 15px; margin-left: 17px"
			type="PRIMARY"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducao>
	<span i18n="@@integracoes:salva-com-sucesso" xingling="salva-com-sucesso">
		Integração salva com sucesso!
	</span>
	<span
		i18n="@@integracoes:inativada-com-sucesso"
		xingling="inativada-com-sucesso">
		Integração inativada com sucesso!
	</span>
	<span i18n="@@integracoes:ativada-com-sucesso" xingling="ativada-com-sucesso">
		Integração ativada com sucesso!
	</span>
	<span
		i18n="@@integracoes:gym-id-nao-informado"
		xingling="gym-id-nao-informado">
		Para utilizar o Gympass Booking, o campo "Gym ID" deve ser informado!
	</span>
	<span
		i18n="@@integracoes:token-api-gympass-nao-informado"
		xingling="token-api-gympass-nao-informado">
		O campo "Código WellHub" foi preenchido, então o campo "Token API WellHub"
		também deve ser informado!
	</span>
	<span
		i18n="@@integracoes:codigo-gympass-nao-informado"
		xingling="codigo-gympass-nao-informado">
		O campo "Token API WellHub" foi preenchido, então o campo "Código Gympass"
		também deve ser informado!
	</span>
	<span
		i18n="@@integracoes:falha-consulta-configs-gympass-adm"
		xingling="falha-consulta-configs-gympass-adm">
		Falha ao tentar consultar as configurações do WellHub!
	</span>
	<span
		i18n="@@integracoes:falha-salvar-configs-gympass"
		xingling="falha-salvar-configs-gympass">
		Falha ao salvar WellHub!
	</span>
	<span
		i18n="@@integracoes:nenhuma-configuracao-informada-para-ativar"
		xingling="nenhuma-configuracao-informada-para-ativar">
		O campo "Gym ID" ou "Token API Gym" e "Código WellHub" devem ser informados!
	</span>
</pacto-traducoes-xingling>
