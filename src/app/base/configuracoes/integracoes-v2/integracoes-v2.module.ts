import { CommonModule, DecimalPipe } from "@angular/common";
import { NgModule, NO_ERRORS_SCHEMA } from "@angular/core";
import { MatIconModule } from "@angular/material/icon";
import { MatSlideToggleModule } from "@angular/material/slide-toggle";
import { RouterModule, Routes } from "@angular/router";
import { ConfiguracoesGuard } from "@base-core/guards/configuracoes.guard";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { ApiSistemaPactoComponent } from "./components/adm/api-sistema-pacto/api-sistema-pacto.component";
import { GerarTokenComponent } from "./components/adm/api-sistema-pacto/gerar-token/gerar-token.component";
import { ParametrosComponent } from "./components/adm/api-sistema-pacto/parametros/parametros.component";
import { IntegracaoCdlSpcComponent } from "./components/adm/integracao-cdl-spc/integracao-cdl-spc.component";
import { IntegracaoDelsoftModalComponent } from "./components/adm/integracao-delsoft-modal/integracao-delsoft-modal.component";
import { IntegracaoEstacionamentoModalComponent } from "./components/adm/integracao-estacionamento-modal/integracao-estacionamento-modal.component";
import { IntegracaoMentorWebModalComponent } from "./components/adm/integracao-mentor-web-modal/integracao-mentor-web-modal.component";
import { IntegracaoMyWellnessModalComponent } from "./components/adm/integracao-my-wellness-modal/integracao-my-wellness-modal.component";
import { IntegracaoParceiroFidelidadeModalComponent } from "./components/adm/integracao-parceiro-fidelidade-modal/integracao-parceiro-fidelidade-modal.component";
import { TableAcumularDotzComponent } from "./components/adm/integracao-parceiro-fidelidade-modal/table-acumular-dotz/table-acumular-dotz.component";
import { TableResgateDotzComponent } from "./components/adm/integracao-parceiro-fidelidade-modal/table-resgate-dotz/table-resgate-dotz.component";
import { IntegracaoSesiModalComponent } from "./components/adm/integracao-sesi-modal/integracao-sesi-modal.component";
import { IntegracaoVitioModalComponent } from "./components/adm/integracao-vitio-modal/integracao-vitio-modal.component";
import { IntegracaoAmigoFitModalComponent } from "./components/crm/integracao-amigo-fit-modal/integracao-amigo-fit-modal.component";
import { IntegracaoBitrix24LeadModalComponent } from "./components/crm/integracao-bitrix24-lead-modal/integracao-bitrix24-lead-modal.component";
import { IntegracaoBotConversaModalComponent } from "./components/crm/integracao-botconversa-modal/integracao-botconversa-modal.component";
import { IntegracaoGymbotProModalComponent } from "./components/crm/integracao-gymbotpro-modal/integracao-gymbotpro-modal.component";
import { IntegracaoBuzzLeadModalComponent } from "./components/crm/integracao-buzz-lead-modal/integracao-buzz-lead-modal.component";
import { IntegracaoGenericaLeadsModalComponent } from "./components/crm/integracao-generica-leads-modal/integracao-generica-leads-modal.component";
import { IntegracaoGupshupModalComponent } from "./components/crm/integracao-gupshup-modal/integracao-gupshup-modal.component";
import { IntegracaoHubspotModalComponent } from "./components/crm/integracao-hubspot-modal/integracao-hubspot-modal.component";
import { IntegracaoJoinModalComponent } from "./components/crm/integracao-join-modal/integracao-join-modal.component";
import { IntegracaoRecursosFacilitepayComponent } from "./components/adm/integracao-recursos-facilitepay/integracao-recursos-facilitepay.component";
import { IntegracaoNotificacaoViaWebhookModalComponent } from "./components/crm/integracao-notificacao-via-webhook-modal/integracao-notificacao-via-webhook-modal.component";
import { IntegracaoRdStationModalComponent } from "./components/crm/integracao-rd-station-modal/integracao-rd-station-modal.component";
import { IntegracaoSmsModalComponent } from "./components/crm/integracao-sms-modal/integracao-sms-modal.component";
import { IntegracaoWeHelpModalComponent } from "./components/crm/integracao-we-help-modal/integracao-we-help-modal.component";
import { IntegracaoWordPressModalComponent } from "./components/crm/integracao-word-press-modal/integracao-word-press-modal.component";
import { EmpresaModalComponent } from "./components/empresa-modal/empresa-modal.component";
import { IntegracaoConciliadoraModalComponent } from "./components/financeiro/integracao-conciliadora-modal/integracao-conciliadora-modal.component";
import { IntegracaoF360ModalComponent } from "./components/financeiro/integracao-f360-modal/integracao-f360-modal.component";
import { IntegracaoPluggyModalComponent } from "./components/financeiro/integracao-pluggy-modal/integracao-pluggy-modal.component";
import { IntegracaoPluggyQrcodeModalComponent } from "./components/financeiro/integracao-pluggy-modal/integracao-pluggy-qrcode-modal.component";
import { IntegracaoSistemaContabilAlterdataModalComponent } from "./components/financeiro/integracao-sistema-contabil-alterdata-modal/integracao-sistema-contabil-alterdata-modal.component";
import { IntegracoesV2Component } from "./components/integracoes-v2.component";
import { IntegracaoPagoLivreModalComponent } from "./components/pactopay/integracao-pago-livre-modal/integracao-pago-livre-modal.component";
import { IntegracaoPagolivreFormCadastroComponent } from "./components/pactopay/integracao-pago-livre-modal/integracao-pagolivre-form-cadastro/integracao-pagolivre-form-cadastro.component";
import { IntegracaoPjBankModalComponent } from "./components/pactopay/integracao-pj-bank-modal/integracao-pj-bank-modal.component";
import { IntegracaoPjbankFormNovaCredencialComponent } from "./components/pactopay/integracao-pj-bank-modal/integracao-pjbank-form-nova-credencial/integracao-pjbank-form-nova-credencial.component";
import { IntegracaoBalancaBioimpedanciaModalComponent } from "./components/treino/integracao-balanca-bioimpedancia-modal/integracao-balanca-bioimpedancia-modal.component";
import { IntegracaoGympassModalComponent } from "./components/treino/integracao-gympass-modal/integracao-gympass-modal.component";
import { IntegracaoMgbModalComponent } from "./components/treino/integracao-mgb-modal/integracao-mgb-modal.component";
import { IntegracaoMqvModalComponent } from "./components/treino/integracao-mqv-modal/integracao-mqv-modal.component";
import { IntegracaoSpiviModalComponent } from "./components/treino/integracao-spivi-modal/integracao-spivi-modal.component";
import { IntegracaoTotalpassModalComponent } from "./components/treino/integracao-totalpass-modal/integracao-totalpass-modal.component";
import { IntegracaoNuvemshopModalComponent } from "./components/adm/integracao-nuvemshop-modal/integracao-nuvemshop-modal.component";
import { IntegracaoGogoodModalComponent } from "./components/treino/integracao-gogood-modal/integracao-gogood-modal.component";
import { IntegracaoKobanaModalComponent } from "./components/pactopay/integracao-kobana-modal/integracao-kobana-modal.component";
import { IntegracaoKobanaFormCadastroComponent } from "./components/pactopay/integracao-kobana-modal/integracao-kobana-form-cadastro/integracao-kobana-form-cadastro.component";
import { IntegracaoKobanaFormDadosComponent } from "./components/pactopay/integracao-kobana-modal/integracao-kobana-form-dados/integracao-kobana-form-dados.component";
import { IntegracaoSelfloopsModalComponent } from "./components/treino/integracao-selfloops-modal/integracao-selfloops-modal.component";
import { IntegracaoManychatComponent } from "./components/crm/integracao-manychat/integracao-manychat.component";
import { IntegracaoFogueteModalComponent } from "./components/adm/integracao-foguete-modal/integracao-foguete-modal.component";
import { IntegracaoSescDfModalComponent } from "./components/sesc/integracao-sesc-df-modal/integracao-sesc-df-modal.component";
import { IntegracaoGenericaGymbotLeadsModalComponent } from "./components/crm/integracao-generica-leads-gymbot-modal/integracao-generica-leads-gymbot-modal.component";
import { IntegracaoEnvioAcessoPratiqueModalComponent } from "./components/adm/integracao-envio-acesso-pratique-modal/integracao-envio-acesso-pratique-modal.component";
import { TableFogueteDuracaoConfigComponent } from "./components/adm/integracao-foguete-modal/table-foguete-duracao-config/table-foguete-duracao-config.component";

const routes: Routes = [
	{
		path: "integracoes-v2",
		canActivate: [ConfiguracoesGuard],
		children: [
			{
				path: "",
				component: IntegracoesV2Component,
			},
			{
				path: "api-sistema-pacto",
				component: ApiSistemaPactoComponent,
			},
		],
	},
];

@NgModule({
	declarations: [
		IntegracoesV2Component,
		EmpresaModalComponent,
		IntegracaoBalancaBioimpedanciaModalComponent,
		IntegracaoGympassModalComponent,
		IntegracaoGogoodModalComponent,
		IntegracaoMqvModalComponent,
		IntegracaoSelfloopsModalComponent,
		IntegracaoMgbModalComponent,
		IntegracaoSpiviModalComponent,
		IntegracaoMyWellnessModalComponent,
		IntegracaoMentorWebModalComponent,
		IntegracaoEstacionamentoModalComponent,
		IntegracaoParceiroFidelidadeModalComponent,
		IntegracaoNotificacaoViaWebhookModalComponent,
		IntegracaoAmigoFitModalComponent,
		IntegracaoWeHelpModalComponent,
		IntegracaoWordPressModalComponent,
		IntegracaoBuzzLeadModalComponent,
		IntegracaoBitrix24LeadModalComponent,
		IntegracaoRdStationModalComponent,
		IntegracaoSmsModalComponent,
		IntegracaoF360ModalComponent,
		IntegracaoConciliadoraModalComponent,
		IntegracaoPjBankModalComponent,
		IntegracaoNuvemshopModalComponent,
		IntegracaoPagoLivreModalComponent,
		TableAcumularDotzComponent,
		TableResgateDotzComponent,
		IntegracaoGupshupModalComponent,
		IntegracaoSesiModalComponent,
		IntegracaoGenericaLeadsModalComponent,
		IntegracaoGenericaGymbotLeadsModalComponent,
		IntegracaoJoinModalComponent,
		IntegracaoCdlSpcComponent,
		IntegracaoVitioModalComponent,
		IntegracaoDelsoftModalComponent,
		IntegracaoSistemaContabilAlterdataModalComponent,
		IntegracaoPjbankFormNovaCredencialComponent,
		IntegracaoPagolivreFormCadastroComponent,
		IntegracaoHubspotModalComponent,
		IntegracaoTotalpassModalComponent,
		IntegracaoPluggyModalComponent,
		IntegracaoPluggyQrcodeModalComponent,
		IntegracaoBotConversaModalComponent,
		IntegracaoGymbotProModalComponent,
		IntegracaoRecursosFacilitepayComponent,
		ApiSistemaPactoComponent,
		IntegracaoSescDfModalComponent,
		GerarTokenComponent,
		ParametrosComponent,
		IntegracaoKobanaModalComponent,
		IntegracaoKobanaFormCadastroComponent,
		IntegracaoKobanaFormDadosComponent,
		IntegracaoManychatComponent,
		IntegracaoFogueteModalComponent,
		IntegracaoEnvioAcessoPratiqueModalComponent,
		TableFogueteDuracaoConfigComponent,
	],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		BaseSharedModule,
		MatSlideToggleModule,
		MatIconModule,
	],
	providers: [DecimalPipe],
	exports: [IntegracoesV2Component],
	entryComponents: [
		EmpresaModalComponent,
		IntegracaoBalancaBioimpedanciaModalComponent,
		IntegracaoGympassModalComponent,
		IntegracaoGogoodModalComponent,
		IntegracaoMqvModalComponent,
		IntegracaoSelfloopsModalComponent,
		IntegracaoMgbModalComponent,
		IntegracaoSpiviModalComponent,
		IntegracaoMyWellnessModalComponent,
		IntegracaoMentorWebModalComponent,
		IntegracaoEstacionamentoModalComponent,
		IntegracaoParceiroFidelidadeModalComponent,
		IntegracaoNotificacaoViaWebhookModalComponent,
		IntegracaoAmigoFitModalComponent,
		IntegracaoWeHelpModalComponent,
		IntegracaoWordPressModalComponent,
		IntegracaoBuzzLeadModalComponent,
		IntegracaoBitrix24LeadModalComponent,
		IntegracaoRdStationModalComponent,
		IntegracaoSmsModalComponent,
		IntegracaoF360ModalComponent,
		IntegracaoConciliadoraModalComponent,
		IntegracaoPjBankModalComponent,
		IntegracaoNuvemshopModalComponent,
		IntegracaoPagoLivreModalComponent,
		IntegracaoGupshupModalComponent,
		IntegracaoSesiModalComponent,
		IntegracaoGenericaLeadsModalComponent,
		IntegracaoGenericaGymbotLeadsModalComponent,
		IntegracaoVitioModalComponent,
		IntegracaoJoinModalComponent,
		IntegracaoCdlSpcComponent,
		IntegracaoDelsoftModalComponent,
		IntegracaoSistemaContabilAlterdataModalComponent,
		IntegracaoHubspotModalComponent,
		IntegracaoTotalpassModalComponent,
		IntegracaoPluggyModalComponent,
		IntegracaoPluggyQrcodeModalComponent,
		IntegracaoBotConversaModalComponent,
		IntegracaoGymbotProModalComponent,
		IntegracaoRecursosFacilitepayComponent,
		IntegracaoSescDfModalComponent,
		GerarTokenComponent,
		ParametrosComponent,
		IntegracaoKobanaModalComponent,
		IntegracaoKobanaFormCadastroComponent,
		IntegracaoKobanaFormDadosComponent,
		IntegracaoManychatComponent,
		IntegracaoFogueteModalComponent,
		IntegracaoEnvioAcessoPratiqueModalComponent,
		TableFogueteDuracaoConfigComponent,
	],
	schemas: [NO_ERRORS_SCHEMA],
})
export class IntegracoesV2Module {}
