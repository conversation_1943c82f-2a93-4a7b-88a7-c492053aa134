<pacto-configuration-builder
	*ngIf="ready"
	[items]="items"></pacto-configuration-builder>

<div *ngIf="consultarPermissaoUsaPrescricaoIA()" class="configuration-item">
	<div class="row">
		<div class="col-md-8">
			<div class="config-title">
				Atividades do Treino por IA na prescrição de treino
			</div>
			<div class="config-description">
				Adicione ou remova da prescrição de treino as atividades que são usadas
				pela IA. Você pode também configurar individualmente cada atividade, na
				tela de cadastro de atividades.
			</div>
		</div>
		<div class="col-md-4 btns-processo">
			<pacto-cat-button
				(click)="adicionarTodas()"
				i18n-label="@@label-caixa-aberto-btn"
				id="btn-caixa-aberto"
				label="Adicionar todas"
				size="LARGE"
				type="OUTLINE"></pacto-cat-button>

			<pacto-cat-button
				(click)="removerTodas()"
				i18n-label="@@label-confirmar-btn"
				id="btn-receber"
				label="Remover todas"
				size="LARGE"
				type="OUTLINE"></pacto-cat-button>
		</div>
	</div>
</div>

<div class="footer-actions">
	<button
		(click)="saveHandler()"
		[disabled]="saving ? 'disabled' : null"
		class="btn btn-primary"
		id="btn-save-config-treino">
		<span i18n="@@configuracoes-treino:salvar">Salvar</span>
	</button>
	<pacto-log [url]="urlLog"></pacto-log>
</div>

<ng-template #translator let-item="item">
	<ng-container [ngSwitch]="item">
		<span
			*ngSwitchCase="'emitir-ficha-apos-vencimento-treino-title'"
			i18n="
				@@configuration-sistema-treino:emitir-ficha-apos-vencimento-treino-title">
			Emitir ficha após vencimento do treino
		</span>
		<span
			*ngSwitchCase="'numero-impressao-ficha-title'"
			i18n="@@configuration-sistema-treino:numero-impressao-ficha-title">
			Número máximo de impressões de fichas por dia
		</span>
		<span
			*ngSwitchCase="'bloquear-impressao-ficha-apos-todas-execucoes-title'"
			i18n="
				@@configuration-sistema-treino:bloquear-impressao-ficha-apos-todas-execucoes-title">
			Bloquear impressão após o aluno atingir a quantidade de execuções
			previstas
		</span>
		<span
			*ngSwitchCase="
				'dias-sem-observacao-considerar-desacompanhado-descripition'
			"
			i18n="
				@@configuration-sistema-treino:dias-sem-observacao-considerar-desacompanhado-descripition">
			Caso o valor aqui seja maior que 0, o filtro de alunos Desacompanhados
			passa a olhar para os alunos que estão a X dias sem ter observações
			registradas.
		</span>
		<span
			*ngSwitchCase="'dias-antes-vencimento-title'"
			i18n="@@configuration-sistema-treino:dias-antes-vencimento-title">
			Quantidade de dias para identificar um programa de treino como a renovar.
		</span>
		<span
			*ngSwitchCase="'dias-antes-vencimento-description'"
			i18n="@@configuration-sistema-treino:dias-antes-vencimento-description">
			Aqui você pode definir a quantidade de dias que o programa de treino entra
			no status de a renovar, terá impacto no dashboard em Treino a Renovar e no
			relatório de carteira dos professores no campo Próx. Vencimento.
		</span>
		<span
			*ngSwitchCase="'inativos-a-x-dias'"
			i18n="@@configuration-sistema-treino:inativos-a-x-dias">
			Inativos a X dias ( Deixar '0' para mostrar todos):
		</span>
		<span *ngSwitchCase="'agrupamento-series-set'">
			Habilitar agrupamento de atividades para o método BI-Set e Tri-Set:
		</span>
		<span
			*ngSwitchCase="'agrupamento-series-set-description'"
			i18n="@@configuration-sistema-treino:agrupamento-series-set-description">
			Ao marcar a configuração as atividades que são do tipo BI-set e TRI-set
			serão agrupadas e enviadas para os aplicativos/retira fichas como uma
			única atividade.
		</span>
		<span
			*ngSwitchCase="'bloqueio-treino-ativo-visitante'"
			i18n="@@configuration-sistema-treino:bloquear-prescricao">
			Bloquear prescrição de treino para alunos inativos/visitantes
		</span>
		<span
			*ngSwitchCase="'visualizar-mensagem-aviso'"
			i18n="@@configuration-sistema-treino:visualizar-mensagem-aviso">
			Visualizar mensagem aviso
		</span>
		<span
			*ngSwitchCase="'permitir-visualizar-wod'"
			i18n="@@configuration-sistema-treino:permitir-visualizar-wod">
			Permitir visualizar wod:
		</span>
		<span
			*ngSwitchCase="'permitir-visualizar-wod-description'"
			i18n="@@configuration-sistema-treino:permitir-visualizar-wod-description">
			Permitir que clientes WellHub ou TotalPass possam ver, mas não realizar o
			WOD antes de realizar o check-in
		</span>
		<span
			*ngSwitchCase="'permitir-visualizar-cref'"
			i18n="@@configuration-sistema-treino:permitir-visualizar-cref">
			Visualizar campo Cref:
		</span>
		<span
			*ngSwitchCase="'permitir-visualizar-cref-description'"
			i18n="
				@@configuration-sistema-treino:permitir-visualizar-cref-description">
			Exibir o Cref do professor na ficha do aluno.
		</span>
		<!-- Ocultar o item 'permitir-visualizar-par-q-10-perguntas' -->
		<!--<span *ngSwitchCase="'permitir-visualizar-par-q-10-perguntas'">
			Modelo Par-Q 10 Perguntas
		</span>-->
		<span *ngSwitchCase="'permitir_visualizar_lei_parq'">
			Visualizar Lei Par-Q
		</span>
		<span
			*ngSwitchCase="'permitir-visualizar-aviso-de-pendencias'"
			i18n="
				@@configuration-sistema-treino:permitir-visualizar-aviso-de-pendencias">
			Permitir visualizar Aviso de Pendências:
		</span>
		<div *ngIf="exibirBlocoAprovacaoAutomatica">
			<span
				*ngSwitchCase="'tempo-maximo-revisao-title'"
				i18n="@@configuration-sistema-treino:tempo-maximo-revisao-title">
				Tempo máximo para revisão do treino pelo professor
			</span>
			<span
				*ngSwitchCase="'tempo-maximo-revisao-desc'"
				i18n="@@configuration-sistema-treino:tempo-maximo-revisao-desc">
				Defina o tempo máximo para que o professor revise o treino solicitado.
			</span>
		</div>
		<span
			*ngSwitchCase="'forcar-criar-novo-programa'"
			i18n="@@configuration-sistema-treino:forcar-criar-novo-programa">
			Forçar criação de novo programa na renovação do treino
		</span>
	</ng-container>
</ng-template>

<ng-template #buttonLabel i18n="@@buttons:salvar">Salvar</ng-template>
<span
	#configSuccess
	[hidden]="true"
	i18n="@@configuration-sistema-treino:config-salva">
	Configurações salvas com sucesso
</span>
