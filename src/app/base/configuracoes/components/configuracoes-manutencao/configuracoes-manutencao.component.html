<div [ngClass]="{ 'loading-blur': loading }">
	<pacto-configuration-builder
		*ngIf="ready"
		[items]="items"></pacto-configuration-builder>

	<pacto-configuration-builder
		*ngIf="ready"
		[items]="itemsExperimentais"></pacto-configuration-builder>

	<div *ngIf="showAbasAppPersonalizado && pactobr" class="config-group-wrapper">
		<pacto-configuration-builder
			*ngIf="ready"
			[items]="itensAppPersonalizado"></pacto-configuration-builder>
	</div>

	<div class="footer-actions">
		<button
			(click)="saveHandler()"
			[disabled]="saving ? 'disabled' : null"
			class="btn btn-primary"
			id="btn-save-config-app">
			<span i18n="@@configuration-apps:button:salvar-app">Salvar</span>
		</button>
	</div>

	<ng-template #translator let-item="item">
		<ng-container [ngSwitch]="item">
			<span
				*ngSwitchCase="'excluir-cliente-tr-nao-zw-title'"
				i18n="@@configuracoes-manutencao:excluir-cliente-tr-nao-zw-title">
				Excluir alunos que não existem no ZW:
			</span>
			<span
				*ngSwitchCase="'excluir-cliente-tr-nao-zw-desc'"
				i18n="@@configuracoes-manutencao:excluir-cliente-tr-nao-zw-desc">
				Exclui todos os alunos do treino, que tem um código de cliente que não
				existe no ZW. O processo vai excluir todo perfil, programa de treino,
				avaliações e vínculos. Deve ser utilizado para possíveis erros de
				importação ou erros momentâneos.
			</span>
			<pacto-cat-button
				*ngSwitchCase="'button'"
				[label]="'Executar'"
				[id]="'button'"
				(click)="excluirDuplicados()"></pacto-cat-button>
		</ng-container>
		<ng-container [ngSwitch]="item">
			<span
				*ngSwitchCase="'atualizar-nr-aula-experimental-aluno-title'"
				i18n="@@configuracoes-manutencao:atualizar-aulas:titulo">
				Atualizar aulas experimentais para todos os alunos:
			</span>
			<span
				*ngSwitchCase="'atualizar-nr-aula-experimental-aluno-desc'"
				i18n="@@configuracoes-manutencao:atualizar-aulas:descricao">
				Atualizar todos os alunos com o número de aulas experimentais da
				configuração do treino.
			</span>
			<pacto-cat-button
				*ngSwitchCase="'atualizarButton'"
				[label]="'Executar'"
				[id]="'atualizarButton'"
				(click)="atualizarNumeroAulasExperimentais()"></pacto-cat-button>
		</ng-container>
		<ng-container [ngSwitch]="item">
			<span
				*ngSwitchCase="'sincrizar-professor-zw-tw-title'"
				i18n="@@configuracoes-manutencao:sincrizar-professor-zw-tw:titulo">
				Atualizar professores que estão com situação incorreta entre ZW e TW:
			</span>
			<span
				*ngSwitchCase="'sincrizar-professor-zw-tw-desc'"
				i18n="@@configuracoes-manutencao:sincrizar-professor-zw-tw:descricao">
				Sincronizar com o ZW professores que estão com situação incorreta entre
				o ZW e TW.
			</span>
			<pacto-cat-button
				*ngSwitchCase="'atualizarButtonProf'"
				[label]="'Executar'"
				[id]="'atualizarButtonProf'"
				(click)="sincronizarProfessores()"></pacto-cat-button>
		</ng-container>

		<ng-container [ngSwitch]="item">
			<span
				*ngSwitchCase="'sincronizar-gympass-title'"
				i18n="@@configuracoes-manutencao:sincronizar-gympass:titulo">
				Sincronizar Booking Gympass:
			</span>
			<span
				*ngSwitchCase="'sincronizar-gympass-desc'"
				i18n="@@configuracoes-manutencao:sincronizar-gympass:descricao">
				Força a sincronização das aulas consfiguradas com a integração Booking
				Gympass.
			</span>
			<pacto-cat-button
				(click)="sincronizarGympass()"
				*ngSwitchCase="'sincronizarButton'"
				[id]="'sincronizarButton'"
				[label]="'Sincronizar'"></pacto-cat-button>
		</ng-container>

		<ng-container [ngSwitch]="item">
			<span
				*ngSwitchCase="'aplicativo-personalizado-title'"
				i18n="@@configuracao-manutencao:aplicativo-personalizado">
				Empresa utiliza aplicativo personalizado?
			</span>
			<span
				*ngSwitchCase="'aplicativo-personalizado-nome-title'"
				i18n="@@configuracao-manutencao:aplicativo-personalizado:nome">
				Nome do aplicativo
			</span>
			<span
				*ngSwitchCase="'aplicativo-personalizado-url-title'"
				i18n="@@configuracao-manutencao:aplicativo-personalizado:url">
				URL do leadpage
			</span>
		</ng-container>

		<ng-container [ngSwitch]="item">
			<span
				*ngSwitchCase="'sincronizar-aluno-mgb-title'"
				i18n="@@configuracoes-manutencao:sincronizar-aluno-mgb-title">
				Sincronizar alunos MGB:
			</span>
			<span
				*ngSwitchCase="'sincronizar-aluno-mgb-desc'"
				i18n="@@configuracoes-manutencao:sincronizar-aluno-mgb-desc">
				Este processo irá verificar todos alunos ativos que não estão
				sincronizados com o MGB e fará a sincronização dos mesmos.
			</span>
			<pacto-cat-button
				(click)="syncAlunosMGB()"
				*ngSwitchCase="'syncAlunosMGBButton'"
				[id]="'syncAlunosMGBButton'"
				[label]="'Sincronizar'"></pacto-cat-button>
		</ng-container>

		<ng-container [ngSwitch]="item">
			<span *ngSwitchCase="'sincronizar-atividades-ia-title'">
				Sincronizar atividades da IA:
			</span>
			<span *ngSwitchCase="'sincronizar-atividades-ia-desc'">
				Executa o download completo do banco de atividades da IA para
				atualização local.
			</span>
			<pacto-cat-button
				*ngSwitchCase="'syncAtividadesIaButton'"
				[label]="'Sincronizar'"
				[id]="'syncAtividadesIaButton'"
				(click)="syncAtividadesIa()"></pacto-cat-button>
		</ng-container>

		<ng-container [ngSwitch]="item">
			<span *ngSwitchCase="'sincronizar-alunos-zw-tr-title'">
				Verificar e sincronizar todos alunos entre os bancos ZW e TW:
			</span>
			<span *ngSwitchCase="'sincronizar-alunos-zw-tr-desc'">
				Executa o processo de verificação de todos os alunos da empresa logada
				comparando situação e vínculo do aluno e caso esteja diferente entre os
				bancos é atualizado no banco do Treino para ficar igual ao banco do ZW.
				E caso o aluno não exista no banco do Treino mas exista no banco do ZW
				ele será importado para o Treino.
			</span>
			<pacto-cat-button
				*ngSwitchCase="'syncAlunosZwTrButton'"
				[label]="'Sincronizar'"
				[id]="'syncAlunosZwTrButton'"
				(click)="syncAlunosZwTrButton()"></pacto-cat-button>
		</ng-container>
	</ng-template>
</div>

<span
	#configSuccess
	[hidden]="true"
	i18n="@@configuration-sistema-app:config-salva">
	Configurações salvas com sucesso
</span>

<div *ngIf="loading">
	<div class="sk-fading-circle">
		<i class="pct pct-refresh-cw"></i>
	</div>
	<div class="loading-text">
		Excluindo alunos sem vínculos, pode demorar alguns minutos...
	</div>
</div>

<div *ngIf="loadingAtualizar">
	<div class="sk-fading-circle">
		<i class="pct pct-refresh-cw"></i>
	</div>
	<div class="loading-text">
		Atualizando o número de aulas experimentais para todos os alunos, pode
		demorar alguns minutos...
	</div>
</div>

<div *ngIf="loadingProfessores">
	<div class="sk-fading-circle">
		<i class="pct pct-refresh-cw"></i>
	</div>
	<div class="loading-text">
		Sincronizando professores, pode demorar alguns minutos...
	</div>
</div>

<div *ngIf="loadingBooking">
	<div class="sk-fading-circle">
		<i class="pct pct-refresh-cw"></i>
	</div>
	<div class="loading-text">
		Sincronizando aulas, pode demorar alguns minutos...
	</div>
</div>

<div *ngIf="loadingSyncAlunosMGB">
	<div class="sk-fading-circle">
		<i class="pct pct-refresh-cw"></i>
	</div>
	<div class="loading-text">
		Verificando e sincronizando alunos ativos com MGB, pode demorar alguns
		minutos...
	</div>
</div>

<div *ngIf="loadingAtividadesIa">
	<div class="sk-fading-circle">
		<i class="pct pct-refresh-cw"></i>
	</div>
	<div class="loading-text">
		Sincronizando atividades do banco da IA, pode demorar alguns minutos...
	</div>
</div>

<span
	#removerTitulo
	[hidden]="true"
	i18n="@@configuracoes-manutencao:remover-aluno:title">
	Remover alunos ?
</span>
<span
	#sucessoRemover
	[hidden]="true"
	i18n="@@configuracoes-manutencao:remover-aluno:mensagem-success">
	Manutenção executada com sucesso.
</span>
<span
	#erroRemover
	[hidden]="true"
	i18n="@@configuracoes-manutencao:remover-aluno:mensagem-error">
	Não é possível remover os alunos.
</span>
<span
	#removerBody
	[hidden]="true"
	i18n="@@configuracoes-manutencao:remover-aluno:body">
	Tem certeza que deseja remover todos os alunos que não tem vínculo com o ZW?
	Esse processo pode demorar alguns minutos.
</span>

<span [hidden]="true" #atualizarTituloQuantidade>
	Atualizar o número de aulas experimentais para todos os alunos ?
</span>
<span [hidden]="true" #sucessoAtualizar>Manutenção executada com sucesso.</span>
<span [hidden]="true" #erroAtualizar>
	Não é possível atualizar o número de aulas experimentais para todos os alunos.
</span>
<span [hidden]="true" #atualizarBody>
	Tem certeza que deseja atualizar o número de aulas experimentais para todos os
	alunos. Esse processo pode demorar alguns minutos.
</span>
<span [hidden]="true" #atualizarProfessorSituacaoIncorreta>
	Sincronizar os professores ?
</span>
<span [hidden]="true" #atualizarProfessorSituacaoIncorretaBody>
	Tem certeza que deseja sincronizar os professores que estão com situação
	incorreta. Esse processo pode demorar alguns minutos.
</span>

<span
	#tituloSyncAlunosMGB
	[hidden]="true"
	i18n="@@configuracoes-manutencao:sincronizar-aluno-mgb:title">
	Sincronizar alunos?
</span>
<span
	#sucessoSyncAlunosMGB
	[hidden]="true"
	i18n="@@configuracoes-manutencao:sincronizar-aluno-mgb:mensagem-success">
	Sincronização executada com sucesso.
</span>
<span
	#erroSyncAlunosMGB
	[hidden]="true"
	i18n="@@configuracoes-manutencao:sincronizar-aluno-mgb:mensagem-error">
	Não é possível sincronizar os alunos.
</span>
<span
	#bodySyncAlunosMGB
	[hidden]="true"
	i18n="@@configuracoes-manutencao:sincronizar-aluno-mgb:body">
	Tem certeza que deseja sincronizar os alunos ativos com o MGB? Esse processo
	pode demorar alguns minutos.
</span>
<span
	#confirmarSyncAlunosMGB
	[hidden]="true"
	i18n="@@configuracoes-manutencao:sincronizar-aluno-mgb:action">
	Sincronizar
</span>
