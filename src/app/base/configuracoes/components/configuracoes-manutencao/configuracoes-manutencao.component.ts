import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";

import { SnotifyService } from "ng-snotify";

import { SessionService } from "@base-core/client/session.service";
import { TreinoConfigCacheService } from "src/app/base/configuracoes/configuration.service";
import {
	ConfigItemType,
	PerfilAcessoRecursoNome,
	TreinoApiAlunosService,
	TreinoApiConfiguracoesTreinoService,
	TreinoApiProgramaService,
} from "treino-api";
import { ModalService } from "@base-core/modal/modal.service";
import { switchMap } from "rxjs/operators";

@Component({
	selector: "pacto-configuracoes-manutencao",
	templateUrl: "./configuracoes-manutencao.component.html",
	styleUrls: ["./configuracoes-manutencao.component.scss"],
})
export class ConfiguracoesManutencaoComponent implements OnInit {
	@ViewChild("translator", { static: true }) translator;
	@ViewChild("notificacoes", { static: true }) notificacoes;

	@ViewChild("removerTitulo", { static: true }) removerTitulo;
	@ViewChild("removerBody", { static: true }) removerBody;
	@ViewChild("erroRemover", { static: true }) erroRemover;
	@ViewChild("sucessoRemover", { static: true }) sucessoRemover;
	@ViewChild("atualizarTituloQuantidade", { static: true })
	atualizarTituloQuantidade;
	@ViewChild("atualizarProfessorSituacaoIncorreta", { static: true })
	atualizarProfessorSituacaoIncorreta;
	@ViewChild("atualizarProfessorSituacaoIncorretaBody", { static: true })
	atualizarProfessorSituacaoIncorretaBody;
	@ViewChild("atualizarBody", { static: true }) atualizarBody;
	@ViewChild("erroAtualizar", { static: true }) erroAtualizar;
	@ViewChild("sucessoAtualizar", { static: true }) sucessoAtualizar;
	@ViewChild("configSuccess", { static: true }) configSuccess;
	@ViewChild("tituloSyncAlunosMGB", { static: true }) tituloSyncAlunosMGB;
	@ViewChild("sucessoSyncAlunosMGB", { static: true }) sucessoSyncAlunosMGB;
	@ViewChild("erroSyncAlunosMGB", { static: true }) erroSyncAlunosMGB;
	@ViewChild("bodySyncAlunosMGB", { static: true }) bodySyncAlunosMGB;
	@ViewChild("confirmarSyncAlunosMGB", { static: true }) confirmarSyncAlunosMGB;

	formGroup: FormGroup = new FormGroup({});
	formGroupAppPersonalizado: FormGroup = new FormGroup({});
	ready = false;
	saving = false;
	itemsExperimentais: any[] = [];
	itensAppPersonalizado: any[] = [];
	showAbasAppPersonalizado: boolean;
	integradoZw = false;
	items: any[] = [];
	numberMask = { mask: [/\d/, /\d/, /\d/], guide: false };
	loading = false;
	loadingAtualizar = false;
	loadingProfessores = false;
	loadingBooking = false;
	loadingSyncAlunosMGB = false;
	loadingAtividadesIa = false;
	loadingSyncAlunosZwTr = false;
	nr_aulas: any;
	pactobr = false;
	mgb: any;

	constructor(
		private treinoConfigService: TreinoConfigCacheService,
		private treinoConfigApiService: TreinoApiConfiguracoesTreinoService,
		private treinoApiProgramaService: TreinoApiProgramaService,
		private treinoApiAlunosService: TreinoApiAlunosService,
		private notify: SnotifyService,
		private appModal: ModalService,
		private session: SessionService,
		private cd: ChangeDetectorRef,
		private configCache: TreinoConfigCacheService
	) {}

	ngOnInit() {
		this.integradoZw = this.session.integracaoZW;
		this.nr_aulas =
			this.treinoConfigService.configuracoesAula.nr_aula_experimental_aluno;
		this.pactobr = this.session.loggedUser.username.toLowerCase() === "pactobr";

		if (this.integradoZw) {
			this.items.push({
				name: "excluir-cliente-tr-nao-zw",
				titleKey: "excluir-cliente-tr-nao-zw-title",
				descriptionKey: "excluir-cliente-tr-nao-zw-desc",
				type: ConfigItemType.BUTTON,
				translator: this.translator,
				mask: this.numberMask,
				typeDescription: "button",
			});
			if (this.isMgbAtiva()) {
				this.items.push({
					name: "sincronizar-aluno-mgb",
					titleKey: "sincronizar-aluno-mgb-title",
					descriptionKey: "sincronizar-aluno-mgb-desc",
					type: ConfigItemType.BUTTON,
					translator: this.translator,
					mask: this.numberMask,
					typeDescription: "syncAlunosMGBButton",
				});
			}
			if (this.pactobr) {
				this.items.push({
					name: "sincrizar-professor-zw-tw",
					titleKey: "sincrizar-professor-zw-tw-title",
					descriptionKey: "sincrizar-professor-zw-tw-desc",
					type: ConfigItemType.BUTTON,
					translator: this.translator,
					mask: this.numberMask,
					typeDescription: "atualizarButtonProf",
				});
			}
			this.itemsExperimentais.push({
				name: "sincronizar-atividades-ia",
				titleKey: "sincronizar-atividades-ia-title",
				descriptionKey: "sincronizar-atividades-ia-desc",
				type: ConfigItemType.BUTTON,
				translator: this.translator,
				mask: this.numberMask,
				typeDescription: "syncAtividadesIaButton",
			});
		}

		this.itemsExperimentais.push({
			name: "atualizar-nr-aula-experimental-aluno",
			titleKey: "atualizar-nr-aula-experimental-aluno-title",
			descriptionKey: "atualizar-nr-aula-experimental-aluno-desc",
			type: ConfigItemType.BUTTON,
			translator: this.translator,
			mask: this.numberMask,
			typeDescription: "atualizarButton",
		});

		this.itemsExperimentais.push({
			name: "sincronizar-alunos-zw-tr",
			titleKey: "sincronizar-alunos-zw-tr-title",
			descriptionKey: "sincronizar-alunos-zw-tr-desc",
			type: ConfigItemType.BUTTON,
			translator: this.translator,
			mask: this.numberMask,
			typeDescription: "syncAlunosZwTrButton",
		});

		if (this.pactobr) {
			this.itemsExperimentais.push({
				name: "aplicativo_personalizado",
				titleKey: "aplicativo-personalizado-title",
				type: ConfigItemType.CHECKBOX,
				translator: this.translator,
			});

			this.itensAppPersonalizado = [
				{
					name: "aplicativo_personalizado_nome",
					titleKey: "aplicativo-personalizado-nome-title",
					type: ConfigItemType.INPUT,
					translator: this.translator,
				},
				{
					name: "aplicativo_personalizado_url",
					titleKey: "aplicativo-personalizado-url-title",
					type: ConfigItemType.INPUT,
					translator: this.translator,
				},
			];
		}

		this.popularFormGroup();
		this.carregarFormGroupAppPersonalizado();
		this.setFormGroupValues();
		this.setupEvents();
		this.verificarPermissoes();
	}

	private isMgbAtiva() {
		this.mgb = this.configCache.configuracoesIntegracoesListaMGB;
		const find = this.mgb.find(
			(x) => x.empresa === Number(this.session.empresaId)
		);
		const tokenMgb = find ? find.token : null;
		return tokenMgb !== null && tokenMgb.length > 0;
	}

	private popularFormGroup() {
		this.formGroup = new FormGroup({});
		this.items.forEach((item) => {
			this.formGroup.addControl(item.name, new FormControl(null));
			item.formControl = this.formGroup.get(item.name);
		});
		this.itemsExperimentais.forEach((item) => {
			this.formGroup.addControl(item.name, new FormControl(null));
			item.formControl = this.formGroup.get(item.name);
		});
		this.ready = true;
	}

	private setFormGroupValues() {
		const configuracoes = this.treinoConfigService.configuracoesManutencao;
		this.showAbasAppPersonalizado = configuracoes.aplicativo_personalizado;

		for (const prop in configuracoes) {
			if (this.formGroup.get(prop) != null) {
				this.formGroup.get(prop).setValue(configuracoes[prop]);
			}
		}

		this.formGroupAppPersonalizado.setValue({
			aplicativo_personalizado_nome:
				configuracoes.aplicativo_personalizado_nome,
			aplicativo_personalizado_url: configuracoes.aplicativo_personalizado_url,
		});
		this.showAbasAppPersonalizado =
			configuracoes.aplicativo_personalizado.toString() === "true";
	}

	excluirDuplicados() {
		setTimeout(() => {
			const title = this.removerTitulo.nativeElement.innerHTML;
			const body = this.removerBody.nativeElement.innerHTML;
			const mensagemError = this.erroRemover.nativeElement.innerHTML;
			const sucesso = this.sucessoRemover.nativeElement.innerHTML;

			const appModal = this.appModal.confirm(title, body);
			appModal.result
				.then(() => {
					if (this.integradoZw !== false) {
						this.loading = true;
						this.cd.detectChanges();
						this.treinoConfigApiService
							.executarExclusaoNaoExisteZw(this.session.empresaId)
							.subscribe({
								error: (error) => {
									this.loading = false;
									this.cd.detectChanges();
									this.notify.error(error.error.meta.message);
								},
								next: (response) => {
									if (response) {
										this.loading = false;
										if (response.toString().includes("OK")) {
											this.notify.success(sucesso);
										} else {
											this.notify.error(mensagemError);
										}
									}
									this.cd.detectChanges();
								},
							});
					} else {
						this.notify.error(mensagemError);
					}
				})
				.catch(() => {});
		});
	}

	atualizarNumeroAulasExperimentais() {
		setTimeout(() => {
			const title = this.atualizarTituloQuantidade.nativeElement.innerHTML;
			const body = this.atualizarBody.nativeElement.innerHTML;
			const mensagemError = this.erroAtualizar.nativeElement.innerHTML;
			const sucesso = this.sucessoAtualizar.nativeElement.innerHTML;

			const appModal = this.appModal.confirm(title, body, "Atualizar");
			appModal.result
				.then(() => {
					this.loadingAtualizar = true;
					this.cd.detectChanges();
					this.treinoConfigApiService
						.updateConfiguracaoAtualizarAulaExperimental(this.nr_aulas)
						.subscribe((response) => {
							if (response) {
								this.loadingAtualizar = false;
								if (response.toString().includes("OK")) {
									this.notify.success(sucesso);
								} else {
									this.notify.error(mensagemError);
								}
							}
							this.cd.detectChanges();
						});
				})
				.catch(() => {});
		});
	}

	sincronizarProfessores() {
		setTimeout(() => {
			const title =
				this.atualizarProfessorSituacaoIncorreta.nativeElement.innerHTML;
			const body =
				this.atualizarProfessorSituacaoIncorretaBody.nativeElement.innerHTML;

			const appModal = this.appModal.confirm(title, body, "Atualizar");
			appModal.result
				.then(() => {
					this.loadingProfessores = true;
					this.cd.detectChanges();
					this.treinoConfigApiService
						.sincronizarProfessores(this.session.chave)
						.subscribe((response) => {
							console.log(response);
							this.notify.success("Processo finalizado!");
							this.loadingProfessores = false;
							this.cd.detectChanges();
						});
				})
				.catch(() => {});
		});
	}

	verificarPermissoes() {
		const configuracoesEmpresa = this.session.recursos.get(
			PerfilAcessoRecursoNome.CONFIGURACOES_EMPRESA
		);
		if (
			configuracoesEmpresa &&
			!configuracoesEmpresa.incluir &&
			!configuracoesEmpresa.editar &&
			!configuracoesEmpresa.excluir
		) {
			this.formGroup.disable();
			this.saving = true;
		}
	}

	sincronizarGympass() {
		setTimeout(() => {
			const title = "Sincronizar Booking Gympass";
			const body =
				"Força a sincronização das aulas consfiguradas com a integração Booking Gympass.";
			const mensagemError = "Erro na sincronização";
			const sucesso = "Aulas sincronizadas com sucesso!";

			const appModal = this.appModal.confirm(title, body, "Sincronizar");
			appModal.result
				.then(() => {
					if (this.integradoZw !== false) {
						this.loadingBooking = true;
						this.cd.detectChanges();
						this.treinoConfigApiService
							.sincronizarGympass(this.session.empresaId)
							.subscribe((response) => {
								if (response) {
									this.loadingBooking = false;
									if (response.toString().includes("OK")) {
										this.notify.success(sucesso);
									} else {
										this.notify.error(mensagemError);
									}
								}
								this.cd.detectChanges();
							});
					} else {
						this.loadingBooking = false;
						this.notify.error(mensagemError);
					}
				})
				.catch(() => {});
		});
	}

	private carregarFormGroupAppPersonalizado() {
		this.formGroupAppPersonalizado = new FormGroup({});
		this.itensAppPersonalizado.forEach((itensAppPersonalizado) => {
			this.formGroupAppPersonalizado.addControl(
				itensAppPersonalizado.name,
				new FormControl(null)
			);
			itensAppPersonalizado.formControl = this.formGroupAppPersonalizado.get(
				itensAppPersonalizado.name
			);
		});
		this.ready = true;
	}

	private setupEvents() {
		const habilitarAbasAppPersonalizado = this.itemsExperimentais.find(
			(item) => {
				return item.name === "aplicativo_personalizado";
			}
		);
		if (habilitarAbasAppPersonalizado) {
			habilitarAbasAppPersonalizado.formControl.valueChanges.subscribe(
				(value) => {
					this.showAbasAppPersonalizado = value;
				}
			);
		}
	}

	saveHandler() {
		const dto = {
			aplicativo_personalizado:
				this.formGroup.getRawValue().aplicativo_personalizado,
			aplicativo_personalizado_nome:
				this.formGroup.getRawValue().aplicativo_personalizado_nome,
			aplicativo_personalizado_url:
				this.formGroup.getRawValue().aplicativo_personalizado_url,
		};

		if (dto["aplicativo_personalizado"] === true) {
			const abaAplicativo = this.formGroupAppPersonalizado.getRawValue();
			dto["aplicativo_personalizado_nome"] =
				abaAplicativo["aplicativo_personalizado_nome"];
			dto["aplicativo_personalizado_url"] =
				abaAplicativo["aplicativo_personalizado_url"];
		}

		const save$ =
			this.treinoConfigApiService.updateConfiguracoesManutencao(dto);
		const update$ = this.treinoConfigService.loadTreinoConfigCache();
		save$.pipe(switchMap(() => update$)).subscribe(() => {
			this.atualizarInfo();
			const configSuccess = this.configSuccess.nativeElement.innerHTML;
			this.notify.success(configSuccess);
		});
	}

	private atualizarInfo() {
		this.treinoConfigApiService.atualizarInfoPath().subscribe((value) => {});
	}

	syncAlunosMGB() {
		setTimeout(() => {
			const title = this.tituloSyncAlunosMGB.nativeElement.innerHTML;
			const body = this.bodySyncAlunosMGB.nativeElement.innerHTML;
			const mensagemError = this.erroSyncAlunosMGB.nativeElement.innerHTML;
			const sucesso = this.sucessoSyncAlunosMGB.nativeElement.innerHTML;
			const acao = this.confirmarSyncAlunosMGB.nativeElement.innerHTML;

			const appModal = this.appModal.confirm(title, body, acao);
			appModal.result
				.then(() => {
					if (this.integradoZw !== false) {
						this.loadingSyncAlunosMGB = true;
						this.cd.detectChanges();
						this.treinoConfigApiService
							.sincronizarAlunosMGB(this.session.empresaId)
							.subscribe({
								error: (error) => {
									this.notify.error(error.error.meta.message);
									this.loadingSyncAlunosMGB = false;
									this.cd.detectChanges();
								},
								next: (response) => {
									if (response && response.toString().includes("error:")) {
										this.notify.error(response);
									} else {
										this.notify.success(response);
									}
									this.loadingSyncAlunosMGB = false;
									this.cd.detectChanges();
								},
							});
					} else {
						this.notify.error(mensagemError);
					}
				})
				.catch(() => {});
		});
	}

	syncAtividadesIa() {
		const title = "Sincronizar Atividades IA";
		const body =
			"Confirma a sincronização completa do banco de atividades da IA? " +
			"Esse processo atualizará todas as atividades geradas pela IA com seus dados originais. " +
			"Caso alguma atividade tenha sido editada, os valores alterados serão sobrescritos. " +
			"A sincronização pode levar alguns minutos.";
		const acao = "Sincronizar";
		const appModal = this.appModal.confirm(title, body, acao);

		appModal.result
			.then(() => {
				this.loadingAtividadesIa = true;
				this.cd.detectChanges();
				this.treinoApiProgramaService.atualizarBancoAtividadesIA().subscribe({
					next: (response) => {
						this.loadingAtividadesIa = false;
						if (response && response.toString().includes("OK")) {
							this.notify.success("Atividades sincronizadas com sucesso!");
						} else {
							this.notify.error("Erro ao sincronizar atividades IA.");
						}
						this.cd.detectChanges();
					},
					error: (error) => {
						this.loadingAtividadesIa = false;
						this.cd.detectChanges();
						this.notify.error(
							error.error.meta.message ||
								"Erro na sincronização das atividades IA."
						);
					},
				});
			})
			.catch(() => {});
	}

	syncAlunosZwTrButton() {
		const title =
			"Verificar e sincronizar todos alunos entre os bancos ZW e TW";
		const body =
			"Confirma que deseja que seja verificado e sincronizado todos os alunos entre os bancos ZW e TW? " +
			"Esse processo atualizará a situação e vínculo dos alunos que estejam diferentes entre os bancos, " +
			"e também irá importar todos os alunos que não foram enviados para o Treino. " +
			"Se existirem muitos alunos para serem importados esse processo pode levar alguns minutos.";
		const acao = "Sincronizar";
		const appModal = this.appModal.confirm(title, body, acao);

		appModal.result
			.then(() => {
				this.loadingSyncAlunosZwTr = true;
				this.cd.detectChanges();
				this.treinoApiAlunosService
					.validarSincronizacaoTodosAlunosZwTr("true")
					.subscribe({
						next: (response) => {
							this.loadingSyncAlunosZwTr = false;
							this.notify.success(
								"Verificação e sincronização concluída com sucesso!"
							);
							this.cd.detectChanges();
						},
						error: (error) => {
							this.loadingSyncAlunosZwTr = false;
							this.cd.detectChanges();
							this.notify.error(
								error.error.meta.message ||
									"Erro na sincronização de alunos entre ZW e TR."
							);
						},
					});
			})
			.catch(() => {});
	}
}
