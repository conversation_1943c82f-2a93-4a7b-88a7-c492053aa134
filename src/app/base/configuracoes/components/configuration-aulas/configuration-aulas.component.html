<pacto-configuration-builder
	*ngIf="ready"
	[items]="itemsControle"></pacto-configuration-builder>

<pacto-configuration-builder
	*ngIf="ready && !esconderExperimental"
	[items]="itemsExperimentais"></pacto-configuration-builder>

<pacto-configuration-builder
	*ngIf="ready"
	[items]="items"></pacto-configuration-builder>

<div class="footer-actions">
	<button
		class="btn btn-primary"
		(click)="saveHandler()"
		[disabled]="saving ? 'disabled' : null"
		id="btn-save-config-aula">
		<span i18n="@@configuration-aulas:button-salvar">Salvar</span>
	</button>
	<pacto-log [url]="urlLog"></pacto-log>
</div>

<ng-template #translator let-item="item">
	<ng-container [ngSwitch]="item">
		<span
			*ngSwitchCase="'minutos-agendar-com-antecedencia-title'"
			i18n="@@configuration-sitema-aula:minutos-agendar-com-antecedencia-title">
			Tempo para agendar com antecedência
		</span>
		<span
			*ngSwitchCase="'minutos-agendar-com-antecedencia-desc'"
			i18n="@@configuration-sitema-aula:minutos-agendar-com-antecedencia-desc">
			Tempo em que o aluno pode marcar sua aula com antecedência, ou seja, em
			quanto tempo o aluno pode agendar antes do início da aula. Caso você não
			deseje validar essa restrição de antecedência, preencha com 0 minutos,
			permitindo o aluno marcar aulas em qualquer dia.
		</span>
		<span
			*ngSwitchCase="'minutos-agendar-com-antecedencia-perfil-title'"
			i18n="
				@@configuration-sitema-aula:minutos-agendar-com-antecedencia-perfil-title">
			Tempo para usuário agendar com antecedência
		</span>
		<span
			*ngSwitchCase="'minutos-desmarcar-com-antecedencia-title'"
			i18n="
				@@configuration-sitema-aula:minutos-desmarcar-com-antecedencia-title">
			Tempo para desmarcar com antecedência
		</span>
		<span
			*ngSwitchCase="'minutos-desmarcar-com-antecedencia-desc'"
			i18n="
				@@configuration-sitema-aula:minutos-desmarcar-com-antecedencia-desc">
			Tempo em minutos que o aluno pode desmarcar sua aula com antecedência, ou
			seja, em até quanto tempo o aluno pode desmarcar antes do início da aula.
		</span>
		<span
			*ngSwitchCase="'minutos-alterar-equipamento-com-antecedencia-title'"
			i18n="
				@@configuration-sitema-aula:minutos-alterar-equipamento-com-antecedencia-title">
			Tempo para alterar equipamento com antecedência
		</span>
		<span
			*ngSwitchCase="'minutos-alterar-equipamento-com-antecedencia-desc'"
			i18n="
				@@configuration-sitema-aula:minutos-alterar-equipamento-com-antecedencia-desc">
			Tempo em minutos que o aluno pode alterar o equipamento com antecedência,
			ou seja, em até quanto tempo o aluno pode alterar o equipamento antes do
			início da aula.
		</span>
		<span
			*ngSwitchCase="'modulo-modalidade-title'"
			i18n="@@configuration-sistema-app:modulo-aulas-title">
			Validar Modalidade
		</span>
		<span
			*ngSwitchCase="'modulo-modalidade-desc'"
			i18n="@@configuration-sistema-app:modulo-aulas-desc">
			Validar modalidade para marcar aula: Caso esteja marcado, apenas alunos
			com a modalidade no contrato poderão fazer a aula da modalidade
			correspondente, se o aluno não tiver a modalidade, será contabilizado como
			aula experimental.
		</span>
		<span
			*ngSwitchCase="'nr-aula-modalidade-aluno-title'"
			i18n="@@configuration-sistema-app:modulo-aulas-title">
			Quantidade máxima de agendamentos por modalidade
		</span>
		<span
			*ngSwitchCase="'nr-aula-modalidade-aluno-desc'"
			i18n="configuration-sitema-aula:nr-aula-modalidade-aluno-desc">
			Restrinja quantos agendamentos concomitantes de aula o aluno pode marcar
			previamente dentro da mesma modalidade. Caso você não deseje restringir,
			deixe em branco, isso permitirá ao aluno marcar quantas aulas quiser na
			mesma modalidade.
		</span>
		<span
			*ngSwitchCase="'modulo-horario-title'"
			i18n="@@configuration-sistema-app:modulo-aulas-title">
			Validar horário do contrato
		</span>
		<span
			*ngSwitchCase="'modulo-horario-desc'"
			i18n="@@configuration-sistema-app:modulo-aulas-desc">
			Caso esteja marcado, o aluno não conseguirá marcar uma aula fora do
			horário de seu contrato, a menos que seja uma aula experimental.
		</span>
		<span
			*ngSwitchCase="'nr-aula-experimental-aluno-title'"
			i18n="@@configuration-sistema-app:aluno-title">
			Aulas experimentais por aluno:
		</span>
		<span
			*ngSwitchCase="'controlar_por_freepass-title'"
			i18n="@@configuration-sitema-aula:controlar_por_freepass-title">
			Aulas experimentais por freepass
		</span>
		<span
			*ngSwitchCase="'nr-aula-experimental-aluno-desc'"
			i18n="configuration-sitema-aula:aula-experimental-aluno">
			Este número define a quantidade de aulas experimentais que cada aluno
			possuí. São consideradas aulas experimentais as turmas com modalidades que
			o aluno não adquiriu em seu contrato.
		</span>
		<span
			*ngSwitchCase="'controlar_por_freepass-desc'"
			i18n="@@configuration-sitema-aula:controlar_por_freepass-desc">
			Ao marcar esta opção o aluno poderá realizar aula experimental apenas se
			tiver um freepass lançado, além disso alunos de wellHub poderão ingressar
			nas aulas coletivas após efetuar o check-in
		</span>
		<span
			*ngSwitchCase="'check-outras-unidades-title'"
			i18n="@@configuration-sistema-app:check-outras-unidades-title">
			Permitir check-in em outras unidades da rede
		</span>
		<span
			*ngSwitchCase="'check-outras-unidades-desc'"
			i18n="@@configuration-sistema-app:check-outras-unidades-desc">
			Marque esta opção para que seu aluno consiga agendar uma aula em outra
			unidade. Caso a opção "Validar Modalidade" esteja marcada, o aluno só
			conseguirá realizar as mesmas aulas do contrato (uma única modalidade para
			várias empresas) ou se as modalidades do contrato e da aula estiverem
			vinculadas ao mesmo "Tipo de Modalidade".
		</span>
		<span
			*ngSwitchCase="'bloquear-mesmo-ambiente-title'"
			i18n="@@configuration-sistema-app:bloquear-mesmo-ambiente-title">
			Bloquear marcações conflitantes no mesmo ambiente
		</span>
		<span
			*ngSwitchCase="'bloquear-mesmo-ambiente-desc'"
			i18n="@@configuration-sistema-app:bloquear-mesmo-ambiente-desc">
			Com esta configuração marcada, a agenda não permitirá marcações em
			aulas/serviços distintos no mesmo ambiente e no mesmo horário.
		</span>
		<span
			*ngSwitchCase="'bloquear-aula-coletiva-nao-pertence-modalidade-title'"
			i18n="
				@@configuration-sistema-aula:bloquear-aula-coletiva-nao-pertence-modalidade-title">
			Bloquear visualização de aula coletiva que não pertence a modalidade
		</span>
		<span
			*ngSwitchCase="'bloquear-aula-coletiva-nao-pertence-modalidade-desc'"
			i18n="
				@@configuration-sistema-aula:bloquear-aula-coletiva-nao-pertence-modalidade-desc">
			Marque esta opção para que seja apresentadas apenas aulas coletivas
			pertencentes a modalidade.
		</span>

		<span
			*ngSwitchCase="'desmarcar-aulas-futuras-parcela-atrasada-title'"
			i18n="
				@@configuration-sistema-aula:desmarcar-aulas-futuras-parcela-atrasada-title">
			Desmarcar aulas futuras em caso de parcela atrasada
		</span>
		<span
			*ngSwitchCase="'desmarcar-aulas-futuras-parcela-atrasada-desc'"
			i18n="
				@@configuration-sistema-aula:desmarcar-aulas-futuras-parcela-atrasada-desc">
			Configure esse número de dias e o cliente terá até esse limite para fazer
			o pagamento sem interferir nos agendamentos futuros, caso extrapole esse
			limite todas as aulas futuras serão desmarcadas, independentemente de ser
			coletiva ou turma.
		</span>

		<span
			*ngSwitchCase="'manter_reposicao_aula_coletiva-title'"
			i18n="@@configuration-sistema-aula:manter_reposicao_aula_coletiva-title">
			Manter reposições e aula coletiva na renovação de contrato
		</span>
		<span
			*ngSwitchCase="'manter_reposicao_aula_coletiva-desc'"
			i18n="@@configuration-sistema-aula:manter_reposicao_aula_coletiva-desc">
			Caso marcado, se o aluno ainda tiver reposições dentro do prazo, ele
			poderá utilizar essas reposições no novo contrato. No entanto, se essa
			opção estiver desabilitada, assim que o contrato finalizar, o aluno
			perderá o direito de realizar essas reposições.
		</span>

		<span
			*ngSwitchCase="'limite_dias_reposicao_aula_coletiva-title'"
			i18n="
				@@configuration-sistema-aula:limite_dias_reposicao_aula_coletiva-title">
			Limite de dias para realizar reposição de aula coletiva
		</span>
		<span
			*ngSwitchCase="'limite_dias_reposicao_aula_coletiva-desc'"
			i18n="
				@@configuration-sistema-aula:limite_dias_reposicao_aula_coletiva-desc">
			Se houver um limite de dias e a desmarcação ocorrer em um determinado dia,
			o aluno poderá repor a aula dentro do prazo estipulado. Após esse prazo,
			caso ele ainda não tenha reposto a aula, perderá o direito a essa
			reposição.
		</span>

		<span
			*ngSwitchCase="
				'descontar_credito_ao_marcar_aula_sem_confirmar_presenca-title'
			"
			i18n="
				@@configuration-sistema-aula:descontar_credito_ao_marcar_aula_sem_confirmar_presenca-title">
			Descontar crédito do contrato ao marcar uma aula coletiva
		</span>
		<span
			*ngSwitchCase="
				'descontar_credito_ao_marcar_aula_sem_confirmar_presenca-desc'
			"
			i18n="
				@@configuration-sistema-aula:descontar_credito_ao_marcar_aula_sem_confirmar_presenca-desc">
			Quando essa configuração estiver ativada, o sistema descontará
			automaticamente o crédito do contrato (plano de crédito) do aluno ao
			marcar uma aula coletiva, sem necessidade de confirmar a presença.
		</span>

		<span
			*ngSwitchCase="'bloquear_gerar_reposicao_aula_ja_reposta-title'"
			i18n="
				@@configuration-sistema-aula:bloquear_gerar_reposicao_aula_ja_reposta-title">
			Bloquear gerar reposição de uma aula coletiva já reposta
		</span>
		<span
			*ngSwitchCase="'bloquear_gerar_reposicao_aula_ja_reposta-desc'"
			i18n="
				@@configuration-sistema-aula:bloquear_gerar_reposicao_aula_ja_reposta-desc">
			Caso esteja habilitada, o aluno não conseguirá utilizar uma reposição que
			anteriormente já foi desmarcada.
		</span>

		<span
			*ngSwitchCase="
				'utilizar_numeracao_sequencial_identificador_equipamento-title'
			"
			i18n="
				@@configuration-sistema-aula:utilizar_numeracao_sequencial_identificador_equipamento-title">
			Usar numeração sequencial como identificador do equipamento no mapa de
			reservas
		</span>
		<span
			*ngSwitchCase="
				'utilizar_numeracao_sequencial_identificador_equipamento-desc'
			"
			i18n="
				@@configuration-sistema-aula:utilizar_numeracao_sequencial_identificador_equipamento-desc">
			Quando esta configuração estiver habilitada, o equipamento no mapa de
			reservas será identificado por uma numeração sequencial em vez da sua
			posição no mapa.
		</span>
		<span
			*ngSwitchCase="
				'proibir_marcar_aula_antes_pagamento_primeira_parcela_pix-title'
			"
			i18n="
				@@configuration-sistema-aula:proibir_marcar_aula_antes_pagamento_primeira_parcela_pix-title">
			Bloquear check-in sem pagamento da primeira parcela via PIX pelo Vendas
			Online
		</span>
		<span
			*ngSwitchCase="
				'proibir_marcar_aula_antes_pagamento_primeira_parcela-title'
			"
			i18n="
				@@configuration-sistema-aula:proibir_marcar_aula_antes_pagamento_primeira_parcela-title">
			Bloquear check-in sem o pagamento da primeira parcela
		</span>
		<span
			*ngSwitchCase="
				'proibir_marcar_aula_antes_pagamento_primeira_parcela-desc'
			"
			i18n="
				@@configuration-sistema-aula:proibir_marcar_aula_antes_pagamento_primeira_parcela-desc">
			Com esta opção marcada, o aluno será impedido de realizar o check-in em
			aulas coletivas pelo Aplicativo, Agenda e RetiraFicha, caso a primeira
			parcela ainda não tenha sido paga.
		</span>
	</ng-container>
</ng-template>

<ng-template #buttonLabel i18n="@@buttons:salvar">Salvar</ng-template>

<span
	[hidden]="true"
	#configSuccess
	i18n="@@configuration-sitema-aula:config-salva">
	Configurações salvas com sucesso
</span>
