import { HttpClient } from "@angular/common/http";
import {
	ChangeDetectorRef,
	Component,
	Inject,
	OnInit,
	LOCALE_ID,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { CampanhaService } from "marketing-api";
import { catchError, switchMap } from "rxjs/operators";
import { IpService } from "src/services/ip.service";
import { Observable, Subject } from "rxjs";
import { AdmMsApiUsuarioService } from "adm-ms-api";
import { ModalObrigatoriosCallerService } from "pacto-layout";

enum TagsEnum {
	CAIXAS_PEQUENAS = "CAIXAS_PEQUENAS",
	CAIXAS_GRANDES = "CAIXAS_GRANDES",
	SLIDER = "SLIDER",
	SLIDER_LINK = "SLIDER_LINK",
	CONTEUDO_BLOG = "CONTEUDO_BLOG",
	IFRAME = "IFRAME",
}

@Component({
	selector: "graduacao-pacto-home",
	templateUrl: "./home.component.html",
	styleUrls: ["./home.component.scss"],
})
export class HomeComponent implements OnInit {
	itemsCampanha: Array<any> = new Array<any>();
	tagsEnum = TagsEnum;
	selectedItem = 0;
	_destroyedInterval$: Subject<null> = new Subject();

	constructor(
		private ipService: IpService,
		private httpClient: HttpClient,
		private campanhaService: CampanhaService,
		@Inject(LOCALE_ID) private locale,
		private sessionService: SessionService,
		private changeDetector: ChangeDetectorRef,
		private usuarioService: AdmMsApiUsuarioService,
		private modaisObrigatoriosService: ModalObrigatoriosCallerService
	) {}

	ngOnInit() {
		this.loadBanner();
		this.modaisObrigatoriosService.modaisObrigatorios();
	}

	loadBanner() {
		this.ipService
			.getIp()
			.pipe(
				switchMap((ipNumber) => {
					return this.httpClient
						.get(`https://ipwho.is/${ipNumber}`, {
							params: { lang: "pt-BR" },
						})
						.pipe(
							switchMap((ipData) => {
								const currentEmpresa = this.sessionService.currentEmpresa;

								const chaveEmpresa = this.sessionService.chave;
								const redeEmpresa = this.getValueFromObject(
									"codigo",
									currentEmpresa
								);
								const loggedUser = this.sessionService.loggedUser;
								const estado = this.getValueFromObject(
									"estado",
									currentEmpresa
								);
								const pais = this.getValueFromObject("pais", currentEmpresa);
								const idioma = this.getValueFromObject(
									"siglaNovaPlataforma",
									currentEmpresa
								);

								return this.usuarioService
									.getPerfil(
										this.sessionService.codigoUsuarioZw,
										Number(this.sessionService.codigoEmpresa)
									)
									.pipe(
										switchMap((response) => {
											const tipoPerfil = response.content.perfilAcesso;
											return this.obterCampanhas(
												redeEmpresa,
												chaveEmpresa,
												estado,
												pais,
												idioma,
												ipData,
												tipoPerfil
											);
										}),
										catchError((error) => {
											return this.obterCampanhas(
												redeEmpresa,
												chaveEmpresa,
												estado,
												pais,
												idioma,
												ipData,
												""
											);
										})
									);
							})
						);
				})
			)
			.subscribe((data: any) => {
				if (data.result) {
					const campanhas = [];
					(data.result as Array<any>).forEach(
						(campanha: { itens: Array<any> }) => {
							campanha.itens.forEach((item) => {
								campanhas.push(item);
							});
						}
					);
					this.itemsCampanha = campanhas;
				}
				if (this.itemsCampanha.length === 0) {
					this.itemsCampanha.unshift({
						tag: TagsEnum.SLIDER_LINK,
						urlImagem: "pacto-ui/images/banners/graduacao.png",
					});
				}
				this.changeDetector.detectChanges();
			});
	}

	obterCampanhas(
		redeEmpresa,
		chaveEmpresa,
		estado,
		pais,
		idioma,
		ipData,
		tipoPerfil
	): Observable<any> {
		const campanhaparams = {
			redeEmpresa,
			chaveEmpresa: chaveEmpresa ? chaveEmpresa : null,
			siglaEstado: estado ? estado : ipData["region_code"],
			tipoPerfil,
			nomePais: pais ? pais : ipData["country"],
			modulo: "GRD",
			linguagem: idioma ? (idioma as string).toUpperCase() : this.locale,
			page: 0,
			size: 100,
			orderBy: "codigo",
			ativa: true,
		};
		return this.campanhaService.getCurrentCampanha(campanhaparams);
	}

	openLinkInNewTab(link: string) {
		window.open(link, "_blank");
	}

	getValueFromObject(fieldValue: string, obj: any): any {
		if (obj && obj[fieldValue]) {
			return obj[fieldValue];
		}

		return null;
	}
}
