import { HttpClient } from "@angular/common/http";
import {
	ChangeDetectorRef,
	Component,
	Inject,
	LOCALE_ID,
	OnInit,
	Optional,
} from "@angular/core";
import { DomSanitizer, SafeResourceUrl } from "@angular/platform-browser";
import { SessionService } from "@base-core/client/session.service";
import { CampanhaService } from "marketing-api";
import { Subject } from "rxjs";
import { catchError, switchMap } from "rxjs/operators";
import { isNullOrUndefinedOrEmpty } from "sdk";
import { TreinoApiBiService } from "treino-api";
import { IpService } from "../../services/ip.service";
import { AdmMsApiUsuarioService } from "adm-ms-api";
import { ModalObrigatoriosCallerService } from "pacto-layout";
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router";
import { ModalPendenciasComponent } from "./modal-pendencias/modal-pendencias.component";
import { TreinoConfigCacheService } from "../../base/configuracoes/configuration.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";

enum TagsEnum {
	CAIXAS_PEQUENAS = "CAIXAS_PEQUENAS",
	CAIXAS_GRANDES = "CAIXAS_GRANDES",
	SLIDER = "SLIDER",
	SLIDER_LINK = "SLIDER_LINK",
	CONTEUDO_BLOG = "CONTEUDO_BLOG",
	IFRAME = "IFRAME",
}

@Component({
	selector: "pacto-treino-home",
	templateUrl: "./treino-home.component.html",
	styleUrls: ["./treino-home.component.scss"],
})
export class TreinoHomeComponent implements OnInit {
	itemsCampanha: Array<any> = new Array<any>();
	tagsEnum = TagsEnum;
	selectedItem = 0;
	_destroyedInterval$: Subject<null> = new Subject();
	isClubeDeBeneficios = false;

	constructor(
		private ipService: IpService,
		private httpClient: HttpClient,
		private campanhaService: CampanhaService,
		@Inject(LOCALE_ID) private locale,
		private sessionService: SessionService,
		private changeDetector: ChangeDetectorRef,
		private usuarioService: AdmMsApiUsuarioService,
		private route: ActivatedRoute,
		private sanitizer: DomSanitizer,
		private router: Router,
		private modal: NgbModal,
		private treinoBiService: TreinoApiBiService,
		private treinoConfigService: TreinoConfigCacheService,
		private modaisObrigatoriosService: ModalObrigatoriosCallerService
	) {}

	ngOnInit() {
		this.route.params.subscribe((p) => {
			this.isClubeDeBeneficios = p.cb;
		});
		if (!this.isClubeDeBeneficios) {
			this.loadBanner();
		}
		this.verificarModal();
		this.modaisObrigatoriosService.modaisObrigatorios();
	}

	loadBanner() {
		this.ipService
			.getIp()
			.pipe(
				switchMap((ipNumber) => {
					return this.httpClient
						.get(`https://ipwho.is/${ipNumber}`, {
							params: { lang: "pt-BR" },
						})
						.pipe(
							switchMap((ipData) => {
								const currentEmpresa = this.sessionService.currentEmpresa;
								const chaveEmpresa = this.sessionService.chave;
								const redeEmpresa = this.getValueFromObject(
									"codigo",
									currentEmpresa
								);
								const estado = this.getValueFromObject(
									"estado",
									currentEmpresa
								);
								const pais = this.getValueFromObject("pais", currentEmpresa);
								const idioma = this.getValueFromObject(
									"siglaNovaPlataforma",
									currentEmpresa
								);

								return this.usuarioService
									.getPerfil(
										this.sessionService.codigoUsuarioZw,
										Number(this.sessionService.codigoEmpresa)
									)
									.pipe(
										switchMap((response) => {
											const tipoPerfil = response.content.perfilAcesso;
											return this.obterCampanhas(
												redeEmpresa,
												chaveEmpresa,
												estado,
												pais,
												idioma,
												ipData,
												tipoPerfil
											);
										}),
										catchError((error) => {
											return this.obterCampanhas(
												redeEmpresa,
												chaveEmpresa,
												estado,
												pais,
												idioma,
												ipData,
												""
											);
										})
									);
							})
						);
				})
			)
			.subscribe((data: any) => {
				if (data.result) {
					const campanhas = [];
					(data.result as Array<any>).forEach(
						(campanha: { itens: Array<any> }) => {
							campanha.itens.forEach((item) => {
								campanhas.push(item);
							});
						}
					);
					this.itemsCampanha = campanhas.filter(
						(campanha: any) =>
							campanha.tag === TagsEnum.SLIDER_LINK ||
							campanha.tag === TagsEnum.IFRAME
					);
				}
				if (this.itemsCampanha.length === 0) {
					this.itemsCampanha.unshift({
						tag: TagsEnum.SLIDER_LINK,
						urlImagem: "pacto-ui/images/banners/treino.png",
					});
				}
				this.changeDetector.detectChanges();
			});
	}

	obterCampanhas(
		redeEmpresa,
		chaveEmpresa,
		estado,
		pais,
		idioma,
		ipData,
		tipoPerfil
	) {
		const campanhaparams = {
			redeEmpresa,
			chaveEmpresa: chaveEmpresa ? chaveEmpresa : null,
			siglaEstado: estado ? estado : ipData["region_code"],
			tipoPerfil,
			nomePais: pais ? pais : ipData["country"],
			modulo: "NTR",
			linguagem: idioma ? (idioma as string).toUpperCase() : this.locale,
			page: 0,
			size: 100,
			orderBy: "codigo",
			ativa: true,
		};
		return this.campanhaService.getCurrentCampanha(campanhaparams);
	}

	openLinkInNewTab(link: string) {
		window.open(link, "_blank");
	}

	getValueFromObject(fieldValue: string, obj: any): any {
		if (obj && obj[fieldValue]) {
			return obj[fieldValue];
		}

		return null;
	}

	existeClubeDeBeneficios(): boolean {
		return (
			this.sessionService.clubeDeBeneficiosMarketing &&
			!isNullOrUndefinedOrEmpty(
				this.sessionService.clubeDeBeneficiosMarketing.link
			)
		);
	}

	linkClubeDeBeneficios(): SafeResourceUrl {
		const link = this.existeClubeDeBeneficios()
			? this.sessionService.clubeDeBeneficiosMarketing.link
			: "";
		return this.sanitizer.bypassSecurityTrustResourceUrl(link);
	}

	validarConfigVisualizarAvisoDePendencias(): boolean {
		if (
			this.treinoConfigService &&
			this.treinoConfigService.configuracoesTreino &&
			this.treinoConfigService.configuracoesTreino
				.permitir_visualizar_aviso_de_pendencias
		) {
			return true;
		} else {
			return false;
		}
	}

	verificarModal() {
		const now = new Date().getTime();
		const remindLaterTimestamp = localStorage.getItem("remindLaterTimestamp");

		if (now < parseInt(remindLaterTimestamp)) {
			return;
		}

		const id = this.sessionService.loggedUser.professorResponse.id;
		const codigoPessoa =
			this.sessionService.loggedUser.professorResponse.codigoPessoa;

		if (this.validarConfigVisualizarAvisoDePendencias()) {
			this.treinoBiService
				.verificarPendencia(id, codigoPessoa)
				.subscribe((result) => {
					if (result.content) {
						const modalRef = this.modal.open(ModalPendenciasComponent, {
							centered: true,
							windowClass: "larguraModal",
						});

						modalRef.result.then((data) => {
							const now = new Date();
							if (data === "notShowAgain") {
								localStorage.setItem(
									"remindLaterTimestamp",
									new Date(now.getTime() + 24 * 60 * 60 * 1000)
										.getTime()
										.toString()
								);
							} else if (data === "remindLater") {
								localStorage.setItem(
									"remindLaterTimestamp",
									new Date(now.getTime() + 3 * 60 * 60 * 1000)
										.getTime()
										.toString()
								);
							} else if (data === "onlyPendencias") {
								localStorage.setItem(
									"remindLaterTimestamp",
									new Date(now.getTime() + 4 * 60 * 60 * 1000)
										.getTime()
										.toString()
								);
							}
						});
					}
				});
		}
	}

	navegar() {
		this.router.navigate(["treino", "bi", "dashboard"]);
	}
}
