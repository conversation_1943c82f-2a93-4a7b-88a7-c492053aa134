import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import {
	AlunoBase,
	ConfigsTreinoRede,
	PerfilAcessoFuncionalidadeNome,
	PerfilAcessoRecursoNome,
	Programa,
	ProgramaCriar,
	TreinoApiProgramaService,
} from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { TraducoesXinglingComponent } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { Router } from "@angular/router";
import { TreinoConfigCacheService } from "../../../base/configuracoes/configuration.service";

@Component({
	selector: "pacto-modal-criar-programa",
	templateUrl: "./modal-criar-programa.component.html",
	styleUrls: ["./modal-criar-programa.component.scss"],
})
export class ModalCriarProgramaComponent implements OnInit {
	@Input() aluno: AlunoBase;
	codigoColaborador;
	@Input() programasPreDefinidos: Array<Programa> = [];
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@Input() situacaoTreino: string;

	pesquisa = "";
	permissaoProgramaTreino;
	permissaoAtribuirProgramaTreinoPreDefinido;
	alunoPossuiMaisProgramas = false;
	timeout: any;
	cod;
	mat;
	showTreinoPorIa: boolean = false;
	configForcarCriarNovoPrograma: boolean = false;
	permissaoPrescricaoDeTreinoPorIa;

	constructor(
		private programaService: TreinoApiProgramaService,
		private sessionService: SessionService,
		private snotifyService: SnotifyService,
		private modal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private router: Router,
		private treinoConfigService: TreinoConfigCacheService
	) {}

	ngOnInit() {
		this.validarConfigForcarCriarNovoPrograma();
		this.carregarPermissoes();
		if (!this.aluno) {
			this.aluno = {};
		}
		this.alunoPossuiMaisProgramas =
			this.aluno.programas && this.aluno.programas.length > 0;
		this.cod = this.aluno.id;
		this.mat = this.aluno.matriculaZW;
		if (
			this.situacaoTreino === "SEM_TREINO" ||
			this.situacaoTreino === "VENCIDO" ||
			this.situacaoTreino === "A_VENCER"
		) {
			this.showTreinoPorIa = true;
		}
	}

	validarConfigForcarCriarNovoPrograma(): void {
		if (this.codigoColaborador && this.codigoColaborador > 0) {
			this.configForcarCriarNovoPrograma = false;
		} else if (
			this.treinoConfigService &&
			this.treinoConfigService.configuracoesTreino &&
			this.treinoConfigService.configuracoesTreino.forcar_criar_novo_programa &&
			this.situacaoTreino != "SEM_TREINO"
		) {
			this.configForcarCriarNovoPrograma = true;
		}
	}

	getFichasResumido(programaPredefinido: Programa) {
		let result = "";
		if (programaPredefinido.fichas) {
			programaPredefinido.fichas.forEach((ficha, index) => {
				if (index < programaPredefinido.fichas.length) {
					result += ficha.nome + ", ";
				} else {
					result += ficha.nome;
				}
			});
		}

		return result;
	}

	criarProgramaHandler(
		programaPredefinidoId?: string,
		chaveOrigem?: string,
		renovarAtual?: boolean
	) {
		this.programaService.setMostrarComparacaoCriarRenovar(true);
		if (!chaveOrigem) {
			chaveOrigem = "";
		}

		if (
			this.permissaoProgramaTreino.incluir ||
			this.permissaoAtribuirProgramaTreinoPreDefinido
		) {
			if (
				programaPredefinidoId &&
				(this.permissaoProgramaTreino.incluir ||
					this.permissaoAtribuirProgramaTreinoPreDefinido)
			) {
				this.programaService
					.criarProgramaComPredefinido(
						this.aluno.id,
						this.codigoColaborador,
						programaPredefinidoId,
						chaveOrigem
					)
					.subscribe((result) => {
						this.modal.close({
							alunoId: this.aluno.id,
							programa: result,
							novo: false,
						});
					});
			} else if (renovarAtual && this.permissaoProgramaTreino.incluir) {
				this.programaService
					.renovarProgramaAtual(this.aluno.id, this.codigoColaborador)
					.subscribe({
						error: (error) => {
							if (error.error.meta.message.includes("obrigatorio.nome")) {
								this.snotifyService.error(
									this.traducoes.getLabel("nomeProgramaVazio")
								);
							} else if (
								error.error.meta.message.includes("obrigatorio.dataInicio")
							) {
								this.snotifyService.error(
									this.traducoes.getLabel("dataInicioProgramaVazio")
								);
							} else if (
								error.error.meta.message.includes("obrigatorio.dataTermino")
							) {
								this.snotifyService.error(
									this.traducoes.getLabel("dataTerminoProgramaVazio")
								);
							} else if (
								error.error.meta.message.includes("obrigatorio.diasPorSemana")
							) {
								this.snotifyService.error(
									this.traducoes.getLabel("diasPorSemanaProgramaVazio")
								);
							} else if (
								error.error.meta.message.includes("datainicio.maior.datafim")
							) {
								this.snotifyService.error(
									this.traducoes.getLabel("dataInicioMaiorDataFim")
								);
							} else if (
								error.error.meta.message.includes(
									"obrigatorio.diasSemanaMaior7"
								)
							) {
								this.snotifyService.error(
									this.traducoes.getLabel("diasSemanaMaior7")
								);
							} else if (
								error.error.meta.message.includes("obrigatorio.diasPorSemana")
							) {
								this.snotifyService.error(
									this.traducoes.getLabel("diasSemanaVazio")
								);
							} else {
								this.snotifyService.error(error.error.meta.message);
							}
						},
						next: (response) => {
							this.modal.close({
								alunoId: this.aluno.id,
								programa: response,
								novo: false,
							});
						},
					});
			} else if (this.permissaoProgramaTreino.incluir) {
				const programa: ProgramaCriar = {
					alunoId: this.aluno.id,
					colaboradorId: this.codigoColaborador,
				};
				this.programaService.criarPrograma(programa).subscribe((result) => {
					this.modal.close({
						alunoId: this.aluno.id,
						programa: result,
						novo: true,
					});
				});
			} else {
				this.snotifyService.error(this.traducoes.getLabel("userSemPermission"));
			}
		} else {
			this.snotifyService.error(this.traducoes.getLabel("userSemPermission"));
		}
	}

	private carregarPermissoes() {
		this.permissaoProgramaTreino = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO
		);
		this.permissaoAtribuirProgramaTreinoPreDefinido =
			this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.ATRIBUIR_PROGRAMA_TREINO_PRE_DEFINIDO
			);
		this.permissaoPrescricaoDeTreinoPorIa =
			this.sessionService.funcionalidades.get(
				PerfilAcessoFuncionalidadeNome.PRESCRICAO_DE_TREINO_POR_IA
			);
	}

	inputSearchPrograma() {
		clearTimeout(this.timeout);
		this.timeout = setTimeout(() => {
			this.programaService
				.obterProgramaPreDefinidosSlim(
					this.pesquisa,
					this.configsRede.treinoPredefinidoFranqueadora,
					this.configsRede.chaveFranqueadora
				)
				.subscribe((response) => {
					this.programasPreDefinidos = response;
					this.cd.detectChanges();
				});
		}, 500);
	}

	get configsRede(): ConfigsTreinoRede {
		let configsTreinoRede = this.sessionService.configsTreinoRede;
		if (!configsTreinoRede) {
			configsTreinoRede = {
				empresaLogadoIsFranqueadora: false,
				chaveFranqueadora: "",
				configTreinoFranqueadora: false,
				treinoPredefinidoFranqueadora: false,
			};
		}
		return configsTreinoRede;
	}

	treinoPorIA() {
		this.router.navigate(["treino", "cadastros", "programa", "anamnese"], {
			queryParams: { cod: this.cod, mat: this.mat },
		});
		this.modal.close();
	}

	get permitirCriarTreinoAutomatizadoIA() {
		return this.treinoConfigService.configuracoesIa
			.permitir_criar_treino_automatizado_ia;
	}
}
