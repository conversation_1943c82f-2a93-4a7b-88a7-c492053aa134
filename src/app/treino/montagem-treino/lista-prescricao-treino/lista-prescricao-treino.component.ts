import {
	ChangeDetector<PERSON><PERSON>,
	Component,
	EventE<PERSON>ter,
	OnDestroy,
	OnInit,
	Output,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import { debounceTime, map } from "rxjs/operators";
import {
	Prescricao,
	TreinoApiAlunosService,
	TreinoApiColaboradorService,
	TreinoApiProgramaService,
	UsuarioBase,
} from "treino-api";
import {
	DataFiltro,
	FilterComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridColumnConfig,
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { TreinoConfigCacheService } from "../../../base/configuracoes/configuration.service";
import { TreinoContainerComponent } from "../../../pessoas/perfil-cliente/treino/treino-container/treino-container.component";
import { ConfigurarFichaService } from "../configurar-ficha.service";
import { TipoColaboradorEnum } from "./tipo-colaborador-enum";
import { TreinoStateService } from "./treino-state.service";
import { Observable, Subscription, zip } from "rxjs";
import { ActivatedRoute } from "@angular/router";
import { NgbDropdown } from "@ng-bootstrap/ng-bootstrap";

@Component({
	providers: [TreinoContainerComponent],
	selector: "pacto-lista-prescricao-treino",
	templateUrl: "./lista-prescricao-treino.component.html",
	styleUrls: ["./lista-prescricao-treino.component.scss"],
})
export class ListaPrescricaoTreinoComponent implements OnInit, OnDestroy {
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	ready = false;
	lista: Array<Prescricao> = [];
	filterConfig: GridFilterConfig;
	baseFilter: DataFiltro = {};
	endpointUrl;
	logUrl;
	filters;
	listaStatus = [];
	subscription: Subscription;
	data: any = {
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		content: 0,
		page: 0,
	};
	@ViewChild("messageErrorConfigPrescricao", { static: true })
	messageErrorConfigPrescricao;
	searchControl = new FormControl();
	filtroPrincipalControl = new FormControl();
	filtroSecundarioControl = new FormControl();
	filtroSituacaoAluno = new FormControl();
	filtroOrigemAcao = new FormControl();
	filtroOrdenacaoControl = new FormControl();
	itemSelecionado: Prescricao;
	permissaoProgramaTreino;
	nomeBusca;
	permissaoAtribuirProgramaTreinoPreDefinido;
	programasPreDefinidos;
	permissaoTempoAprovacaoAutomatica;
	@ViewChild("sucessoCreate", { static: true }) sucessoCreate;
	ngbPage = 1;
	pageSizeControl: FormControl = new FormControl();
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 20, label: "20" },
		{ id: 30, label: "30" },
	];
	routeParams;
	buttonIA: boolean = false;
	fc = new FormGroup({
		id: new FormControl(null, Validators.required),
		emRevisaoProfessor: new FormControl(false),
	});
	private aprovadosAutomaticamente: Set<number> = new Set<number>();

	constructor(
		private colaboradorService: TreinoApiColaboradorService,
		private programaService: TreinoApiProgramaService,
		private rest: RestService,
		private appModal: ModalService,
		private alunoService: TreinoApiAlunosService,
		private configurarFichaService: ConfigurarFichaService,
		private configurationService: TreinoConfigCacheService,
		private snotify: SnotifyService,
		public sessionService: SessionService,
		private treinoComponent: TreinoContainerComponent,
		private cd: ChangeDetectorRef,
		private treinoState: TreinoStateService,
		private route: ActivatedRoute,
		private activatedRoute: ActivatedRoute
	) {}

	ngOnInit() {
		this.initAll();
		this.listar();
		this.route.queryParams.subscribe((params) => {
			if (params["fromModalPendencias"] !== undefined) {
				this.filtroSecundarioControl.patchValue([
					{ nome: "Sem treino", id: "SEM_TREINO" },
					{ nome: "Vencido", id: "VENCIDO" },
				]);
			}
		});
		this.subscription = this.treinoState.update$.subscribe((programa) => {
			if (programa) {
				this.itemSelecionado.situacaoTreino = "ATIVO";
				this.itemSelecionado.andamento = 0;
				this.itemSelecionado.inicio = new Date(
					programa.inicio
				).toLocaleDateString("pt-BR", {
					day: "2-digit",
					month: "2-digit",
					year: "numeric",
				});
				this.itemSelecionado.fim = new Date(
					programa.termino
				).toLocaleDateString("pt-BR", {
					day: "2-digit",
					month: "2-digit",
					year: "numeric",
				});
				this.itemSelecionado.programa = programa.nome;
				setTimeout(() => {
					this.cd.detectChanges();
				});
			}
		});
		this.endpointUrl = this.rest.buildFullUrl("programas/lista-prescricaoV2");
		this.logUrl = this.rest.buildFullUrl(
			"log/listar-log-exportacao/exportacaoPrescricaoTreino"
		);

		zip(this.getProfessor()).subscribe(() => {
			this.configFilters();
			this.ready = true;
			this.cd.detectChanges();
		});
		this.configFilters();
	}

	ngOnDestroy(): void {
		this.subscription.unsubscribe();
	}

	initAll() {
		this.pageSizeControl.setValue(5);
		this.data.size = 5;
		this.montarListaFiltroSecundario();
		this.routeParams = this.activatedRoute.snapshot.queryParams;
		if (this.routeParams["prof_id"] && this.routeParams["prof_nome"]) {
			this.filtroPrincipalControl.setValue({
				id: `prof${this.routeParams["prof_id"]}`,
				nome: this.routeParams["prof_nome"],
			});
		} else if (
			this.routeParams["primario_id"] &&
			this.routeParams["primario_nome"]
		) {
			this.filtroPrincipalControl.setValue({
				id: `prof${this.routeParams["primario_id"]}`,
				nome: this.routeParams["primario_nome"],
			});
		} else {
			this.filtroPrincipalControl.setValue({
				id: "TODOS_ALUNOS",
				nome: "Todos os alunos",
			});
		}
		if (
			this.routeParams["secundario_id"] &&
			this.routeParams["secundario_nome"]
		) {
			this.filtroSecundarioControl.setValue([
				{
					id: this.routeParams["secundario_id"],
					nome: this.routeParams["secundario_nome"],
				},
			]);
		}
		if (this.routeParams["situacao_aluno"]) {
			this.filtroSituacaoAluno.setValue({
				id: "SITUACAO_ALUNO",
				nome: this.routeParams["situacao_aluno"],
			});
		}
		if (this.routeParams["origem_acao"]) {
			this.filtroOrigemAcao.setValue({
				id: "ORIGEM_ACAO",
				nome: this.routeParams["origem_acao"],
			});
		} else {
			this.filtroOrigemAcao.setValue({
				id: "ORIGEM_ACAO",
				nome: "TELA_PRESCRICAO_TREINO",
			});
		}
		this.searchControl.valueChanges.pipe(debounceTime(500)).subscribe(() => {
			this.filtrar();
		});
	}

	montarListaFiltroSecundario() {
		this.listaStatus = [
			{ nome: "Sem treino", id: "SEM_TREINO" },
			{ nome: "Vencido", id: "VENCIDO" },
			{ nome: "Em dia", id: "EM_DIA" },
			{ nome: "Treinos futuros", id: "TREINO_FUTURO" },
			{ nome: "A vencer", id: "A_VENCER" },
			{ nome: "A Aprovar", id: "A_APROVAR" },
		];
		if (
			this.filtroPrincipalControl.value &&
			this.filtroPrincipalControl.value.id === "COLABORADORES"
		) {
			for (const tipo of Object.keys(TipoColaboradorEnum)) {
				this.listaStatus.push({
					nome: this.notificacoesTranslate.getLabel(
						"tipocol_" + TipoColaboradorEnum[tipo]
					),
					id: "TIPO_COL_" + TipoColaboradorEnum[tipo],
				});
			}
		}
	}

	revisarPrograma(item: Prescricao) {
		this.itemSelecionado = item;
		this.treinoComponent.carregarEdicaoFicha(item.codigoPrograma);
	}

	get permitirAprovarTreinoAutomatizadoIA(): (item: Prescricao) => boolean {
		return (item: Prescricao): boolean => {
			const habilitarAprovacaoProfessor =
				this.configurationService.configuracoesIa
					.habilitar_obrigatoriedade_aprovacao_professor;

			if (habilitarAprovacaoProfessor) {
				return false;
			}

			const tempoAprovacaoAutomatica =
				this.configurationService.configuracoesIa.tempo_aprovacao_automatica !==
					undefined &&
				this.configurationService.configuracoesIa.tempo_aprovacao_automatica !==
					null
					? this.configurationService.configuracoesIa.tempo_aprovacao_automatica
					: 0;

			const agora = new Date();
			const dataLancamento = new Date(item.datalancamento);
			const limiteAprovacao = new Date(
				dataLancamento.getTime() + tempoAprovacaoAutomatica * 60 * 1000
			);

			return (
				agora <= limiteAprovacao ||
				this.aprovadosAutomaticamente.has(item.codigoPrograma)
			);
		};
	}

	verificarAprovacaoAutomatica() {
		const habilitarAprovacaoProfessor =
			this.configurationService.configuracoesIa
				.habilitar_obrigatoriedade_aprovacao_professor;

		this.lista.forEach((item) => {
			const tempoAprovacaoAutomatica =
				this.configurationService.configuracoesIa.tempo_aprovacao_automatica !==
					undefined &&
				this.configurationService.configuracoesIa.tempo_aprovacao_automatica !==
					null
					? this.configurationService.configuracoesIa.tempo_aprovacao_automatica
					: 0;

			const agora = new Date();
			const dataLancamento = new Date(item.datalancamento);
			const limiteAprovacao = new Date(
				dataLancamento.getTime() + tempoAprovacaoAutomatica * 60 * 1000
			);

			if (habilitarAprovacaoProfessor) {
				return;
			}

			if (
				item.geradoPorIA &&
				item.revisadoProfessor &&
				agora >= limiteAprovacao &&
				!this.aprovadosAutomaticamente.has(item.codigoPrograma)
			) {
				this.aprovarPrograma(item);
				item.revisadoProfessor = false;
				this.aprovadosAutomaticamente.add(item.codigoPrograma);
			}
		});

		this.cd.detectChanges();
	}

	aprovarPrograma(item: Prescricao, isManual: boolean = false) {
		this.itemSelecionado = item;

		this.programaService
			.aprovarPrograma(
				this.itemSelecionado.codigoPrograma,
				this.getDto(this.itemSelecionado)
			)
			.subscribe((response) => {
				if (response) {
					if (isManual) {
						this.snotify.success("Programa aprovado com sucesso.");
					}
					item.revisadoProfessor = false;
					this.aprovadosAutomaticamente.add(item.codigoPrograma);
					this.cd.detectChanges();
				} else {
					this.snotify.error("Erro na aprovação do programa.");
				}
			});
	}

	private getDto(item: Prescricao) {
		const dto = this.fc.getRawValue();
		dto.id = item.codigoPrograma;
		dto.emRevisaoProfessor = false;
		return dto;
	}

	criarPrograma(item: Prescricao) {
		this.itemSelecionado = item;

		if (item.tipo === "Colaborador") {
			this.treinoComponent.criarProgramaColaborador(item.codigoColaborador);
		} else {
			this.treinoComponent.callCriarProgramaMatricula(
				item.matricula,
				item.situacaoTreino
			);
		}
	}

	filtrar() {
		this.ngbPage = 1;
		this.listar();
	}

	listar() {
		let filtroSecundario = this.filtroSecundarioControl.value;
		if (this.filtroSecundarioControl.value == null) {
			filtroSecundario = [
				{
					id: "TODOS",
					nome: "Todos",
				},
			];
		}
		this.data.size = this.pageSizeControl.value;
		this.data.page = this.ngbPage;
		this.filters = {
			search: this.searchControl.value,
			primario: this.filtroPrincipalControl.value,
			pessoaProfessorLogado:
				this.sessionService.loggedUser.professorResponse.codigoPessoa,
			secundario: filtroSecundario,
			terciario: this.filtroSituacaoAluno.value,
			quartenario: this.filtroOrigemAcao.value,
			ordenacao: this.filtroOrdenacaoControl.value,
		};

		this.aprovadosAutomaticamente.clear();

		this.programaService
			.listaPrescricaoV2(this.data, this.filters)
			.subscribe((data) => {
				this.lista = data.content;
				this.data.content = data.content.length;
				this.data.totalElements = data.totalElements;
				this.verificarAprovacaoAutomatica();
				this.cd.detectChanges();
			});
	}

	getImagemAluno(imagem: string) {
		if (imagem) {
			return "url(" + imagem + ")";
		} else {
			return "url(assets/images/default-user-icon.png)";
		}
	}

	obterColunasRelatorio() {
		const colunas = [];
		const nome: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "Aluno",
			campo: "nome",
			titulo: "Aluno",
			ordenavel: true,
			defaultVisible: true,
		};
		const matricula: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "matricula",
			campo: "matricula",
			titulo: "matricula",
			ordenavel: true,
			defaultVisible: true,
		};

		const programa: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "programa",
			campo: "programa",
			titulo: "programa",
			ordenavel: true,
			defaultVisible: true,
		};

		const inicio: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "Inic. Programa",
			campo: "Inic. Programa",
			titulo: "Inic. Programa",
			ordenavel: true,
			defaultVisible: true,
		};

		const fim: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "Venc. Programa",
			campo: "Venc. Programa",
			titulo: "Venc. Programa",
			ordenavel: true,
			defaultVisible: true,
		};
		const ultimoAcesso: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "Último Acesso",
			campo: "Último Acesso",
			titulo: "Último Acesso",
			ordenavel: true,
			defaultVisible: true,
		};
		const professor: PactoDataGridColumnConfig = {
			inputType: "text",
			mostrarTitulo: true,
			visible: true,
			nome: "Professor",
			campo: "professor",
			titulo: "professor",
			ordenavel: true,
			defaultVisible: true,
		};
		colunas.push(matricula);
		colunas.push(nome);
		colunas.push(programa);
		colunas.push(inicio);
		colunas.push(fim);
		colunas.push(professor);
		colunas.push(ultimoAcesso);
		return colunas;
	}

	getFiltersShare(): DataFiltro {
		this.baseFilter.filters = this.filters;
		this.baseFilter.configs = {};
		return this.baseFilter;
	}

	pageChangeHandler(page) {
		if (page) {
			this.ngbPage = page;
			this.listar();
		}
	}

	filtroPrincipalSelectBuilder: SelectFilterParamBuilder = (term) => {
		const routeParams = this.activatedRoute.snapshot.queryParams;
		if (
			this.nomeBusca == null &&
			routeParams["primario_id"] &&
			routeParams["primario_nome"] &&
			term == null
		) {
			this.nomeBusca = routeParams["primario_nome"];
		} else {
			this.nomeBusca = term;
		}
		return {
			page: "0",
			size: "50",
			filters: JSON.stringify({
				situacoes: ["ATIVO"],
				quicksearchValue: this.nomeBusca,
				quicksearchFields: ["nome"],
			}),
		};
	};

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		if (response.content) {
			response.content.forEach((i) => {
				if (i.nome.startsWith("label_")) {
					i.nome = this.notificacoesTranslate.getLabel(i.nome);
				}
			});
		}
		return response.content;
	};

	get _rest() {
		return this.rest;
	}

	abrirPerfilAluno(item) {
		if (this.sessionService.isTreinoIndependente()) {
			window
				.open("cadastros/alunos/perfil/" + item + "%3Forigem%3Dbi", "_blank")
				.focus();
		} else {
			window.open("pessoas/perfil-v2/" + item + "/contratos", "_blank").focus();
		}
	}

	get permitirCriarTreinoAutomatizadoIA() {
		return this.configurationService.configuracoesIa
			.habilitar_obrigatoriedade_aprovacao_professor;
	}

	@Output() filterConfigUpdate: EventEmitter<any> = new EventEmitter();
	@ViewChild("filterDropdown", { static: false }) filterDropdown: NgbDropdown;
	@ViewChild("filtroButton", { static: false }) filtroButton: FilterComponent;
	private temporaryFilters;
	private filtro;
	// professores: any[] = [];
	professores: ApiResponseList<UsuarioBase> = {
		content: [],
	};

	professoresB: ApiResponseList<any> = {
		content: [],
	};

	private configFilters() {
		const valorPrimario = this.filtroPrincipalControl.value;
		const filtroPrimarioId = valorPrimario ? valorPrimario.id : null;
		const valorSituacaoTreino =
			this.filtroSecundarioControl.value == null
				? ""
				: this.filtroSecundarioControl.value[0];
		const filtroSituacaoTreinoId = valorSituacaoTreino
			? valorSituacaoTreino.id
			: null;
		const aulaListaFilterTableString = localStorage.getItem(
			"aulaListaFilterTable"
		);
		const aulaListaFilterTable = aulaListaFilterTableString
			? JSON.parse(aulaListaFilterTableString)
			: null;
		const localStorageFilters = aulaListaFilterTable
			? JSON.parse(aulaListaFilterTable.filters)
			: [];

		this.filterConfig = {
			filters: [
				{
					name: "situacaoaluno",
					label: "Situação do aluno",
					type: GridFilterType.MANY,
					options: [
						{ value: "AA", label: "Aula avulsa" },
						{ value: "AE", label: "Atestado médico" },
						{ value: "AT", label: "Ativo" },
						{ value: "AV", label: "A vencer" },
						{ value: "CA", label: "Cancelado" },
						{ value: "DE", label: "Desistente" },
						{ value: "DEP", label: "Dependente" },
						{ value: "DI", label: "Diaria" },
						{ value: "CR", label: "Férias" },
						{ value: "FR", label: "Freepass" },
						{ value: "GD", label: "Gogood" },
						{ value: "GY", label: "Gympass" },
						{ value: "IN", label: "Inativo" },
						{ value: "NO", label: "Normal" },
						{ value: "TP", label: "TotalPass" },
						{ value: "TR", label: "Trancado" },
						{ value: "VE", label: "Vencido" },
						{ value: "VI", label: "Visitante" },
					],
					initialValue: localStorageFilters["situacaoaluno"] || ["AT"],
				},
				{
					name: "primario",
					label: "Carteira",
					type: GridFilterType.MANY,
					options: this.professores.content.map((item) => ({
						value: item.id,
						label: item.nome,
					})),
					initialValue: localStorageFilters["primario"] || [filtroPrimarioId],
				},
				{
					name: "situacaoTreino",
					label: "Situação do treino",
					type: GridFilterType.MANY,
					options: this.listaStatus.map((item) => ({
						value: item.id,
						label: item.nome,
					})),
					initialValue: localStorageFilters["situacaoTreino"] || [
						filtroSituacaoTreinoId,
					],
				},
				{
					name: "ordenacao",
					label: "Ordenação",
					type: GridFilterType.ONE_CHOICE,
					options: [
						{ value: "ALUNO_ASC", label: "Nome (A-Z)" },
						{ value: "ALUNO_DESC", label: "Nome (Z-A)" },
						{ value: "DATAFIM_ASC", label: "Data Final - Crescente" },
						{ value: "DATAFIM_DESC", label: "Data Final - Decrescente" },
					],
					initialValue: ["ALUNO_ASC"],
				},
			],
		};
	}

	filterHandler(filter) {
		this.filterDropdown.close();
		if (filter.filters.primario.length > 0) {
			const listaFiltro = [];
			filter.filters.primario.forEach((item) => {
				if (item != null) {
					listaFiltro.push({
						id: item,
					});
				}
			});

			if (listaFiltro.length > 0) {
				this.filtroPrincipalControl.setValue(listaFiltro);
			}
		}

		if (filter.filters.situacaoTreino.length > 0) {
			const listaFiltro = [];
			filter.filters.situacaoTreino.forEach((item) => {
				if (item != null) {
					listaFiltro.push({
						id: item,
					});
				}
			});

			if (listaFiltro.length > 0) {
				this.filtroSecundarioControl.setValue(listaFiltro);
			} else {
				this.filtroSecundarioControl.setValue([
					{
						id: "TODOS",
					},
				]);
			}
		} else {
			this.filtroSecundarioControl.setValue([
				{
					id: "TODOS",
				},
			]);
		}

		if (filter.filters.situacaoaluno.length > 0) {
			const listaFiltro = [];
			filter.filters.situacaoaluno.forEach((item) => {
				if (item != null) {
					listaFiltro.push({
						nome: "situacao_cliente_prescricao_treino",
						id: item,
					});
				}
			});
			this.filtroSituacaoAluno.setValue(listaFiltro);
		} else {
			this.filtroSituacaoAluno.setValue([]);
		}
		if (filter.filters.ordenacao.length > 0) {
			this.filtroOrdenacaoControl.setValue(filter.filters.ordenacao[0]);
		} else {
			this.filtroOrdenacaoControl.setValue("ALUNO_ASC");
		}
		this.filters = {
			search: "",
			primario: this.filtroPrincipalControl.value,
			pessoaProfessorLogado:
				this.sessionService.loggedUser.professorResponse.codigoPessoa,
			secundario: this.filtroSecundarioControl.value,
			terciario: this.filtroSituacaoAluno.value,
			quartenario: this.filtroOrigemAcao.value,
			ordenacao: this.filtroOrdenacaoControl.value,
		};
		this.data.size = this.pageSizeControl.value;
		this.data.page = this.ngbPage - 1;
		this.aprovadosAutomaticamente.clear();
		this.programaService
			.listaPrescricaoV2(this.data, this.filters)
			.subscribe((data) => {
				this.lista = data.content;
				this.data.content = data.content.length;
				this.data.totalElements = data.totalElements;
				this.verificarAprovacaoAutomatica();
				this.cd.detectChanges();
			});

		this.filtroButton.close();
	}

	private getProfessor(): Observable<any> {
		const parametros = {
			page: "0",
			size: "100",
			filters: JSON.stringify({
				situacoes: ["ATIVO"],
				quicksearchValue: null,
				quicksearchFields: ["nome"],
			}),
		};
		const professores$ = this.programaService.listaFiltro(parametros).pipe(
			map((dados) => {
				dados.content.forEach((professor: any) => {
					if (professor.nome.startsWith("label_")) {
						professor.nome = this.notificacoesTranslate.getLabel(
							professor.nome
						);
					}
					professor.value = professor.id;
					professor.label = professor.nome;
				});
				this.professores = dados;
				return true;
			})
		);
		return professores$;
	}
}
