import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { Subscription } from "rxjs";
import { TreinoBiStateService } from "../treino-bi-home-v2/treino-bi-state.service";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { PerfilAcessoFuncionalidadeNome } from "treino-api";
import { TreinoBiAvaliacaoAcompanhamento } from "@treino-core/treino-bi/treino-bi2.model";
import {
	ModalRelatorioConfig,
	relatorios,
} from "../indicadores-grid/relatorios.config";
import {
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
	PactoDataGridConfigDto,
} from "ui-kit";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { Router } from "@angular/router";

declare var moment;

@Component({
	selector: "pacto-treino-bi-avaliacao-acompanhamento",
	templateUrl: "./treino-bi-avaliacao-acompanhamento.component.html",
	styleUrls: ["./treino-bi-avaliacao-acompanhamento.component.scss"],
})
export class TreinoBiAvaliacaoAcompanhamentoComponent implements OnInit {
	@Input() professorFiltro;
	@Input() professorCodigoPessoaFiltro;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("notaCelula", { static: true }) notaCelula;
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;

	config: PactoDataGridConfigDto;
	relatorio: RelatorioComponent;
	avaliacaoAcompanhamento: TreinoBiAvaliacaoAcompanhamento;
	subscription: Subscription;

	title1estrela: string;
	title2estrelas: string;
	title3estrelas: string;
	title4estrelas: string;
	title5estrelas: string;
	titleAvaliacaoTotal: string;
	podeVisualizarMedia: boolean;

	constructor(
		private cd: ChangeDetectorRef,
		private state: TreinoBiStateService,
		private session: SessionService,
		private snotifyService: SnotifyService,
		private modal: ModalService,
		private rest: RestService,
		private router: Router
	) {}

	ngOnInit() {
		this.verificarPermissao();
		this.subscription = this.state.update$.subscribe((ready) => {
			if (ready) {
				this.setupData();
			}
		});
	}

	private setupData() {
		this.avaliacaoAcompanhamento = this.state.avaliacaoAcompanhamento;
		if (this.avaliacaoAcompanhamento) {
			this.loadTitle();
		}
		this.cd.detectChanges();
	}

	private loadTitle() {
		const titulos = [
			{
				key: "nr1EstrelaAcompanhamento",
				singular: "1 aluno deu 1 estrela",
				plural: "alunos deram 1 estrela",
				nenhum: "Nenhum aluno deu 1 estrela",
				titleVar: "title1estrela",
			},
			{
				key: "nr2EstrelasAcompanhamento",
				singular: "1 aluno deu 2 estrelas",
				plural: "alunos deram 2 estrelas",
				nenhum: "Nenhum aluno deu 2 estrelas",
				titleVar: "title2estrelas",
			},
			{
				key: "nr3EstrelasAcompanhamento",
				singular: "1 aluno deu 3 estrelas",
				plural: "alunos deram 3 estrelas",
				nenhum: "Nenhum aluno deu 3 estrelas",
				titleVar: "title3estrelas",
			},
			{
				key: "nr4EstrelasAcompanhamento",
				singular: "1 aluno deu 4 estrelas",
				plural: "alunos deram 4 estrelas",
				nenhum: "Nenhum aluno deu 4 estrelas",
				titleVar: "title4estrelas",
			},
			{
				key: "nr5EstrelasAcompanhamento",
				singular: "1 aluno deu 5 estrelas",
				plural: "alunos deram 5 estrelas",
				nenhum: "Nenhum aluno deu 5 estrelas",
				titleVar: "title5estrelas",
			},
		];

		titulos.forEach((t) => {
			const count = this.avaliacaoAcompanhamento[t.key] || 0;
			if (count === 0) {
				this[t.titleVar] = t.nenhum;
			} else if (count === 1) {
				this[t.titleVar] = t.singular;
			} else {
				this[t.titleVar] = `${count} ${t.plural}`;
			}
		});

		const totalCount =
			this.avaliacaoAcompanhamento.nrAvaliacoesAcompanhamento || 0;
		if (totalCount === 1) {
			this.titleAvaliacaoTotal = "1 avaliação";
		} else {
			this.titleAvaliacaoTotal = `${totalCount} avaliações`;
		}
	}

	private verificarPermissao() {
		const temPermissao = this.session.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.AVALIACAO_MEDIA_ALUNOS
		);
		this.podeVisualizarMedia = !!temPermissao;
	}

	ClickHandler(name: string, numeroIndicador: number) {
		if (this.podeVisualizarMedia) {
			try {
				const rota = this.router.url;
				localStorage.removeItem("config_table__" + rota);
			} catch (e) {
				console.log("Problema ao limpar filtro do dash");
			}
			this.openModalHandler(relatorios[name], numeroIndicador);
		} else {
			this.snotifyService.warning(
				this.notificacoesTranslate.getLabel("sem-permissao")
			);
		}
	}

	private openModalHandler(
		item: ModalRelatorioConfig,
		numeroIndicador: number
	) {
		if (!item) {
			console.error("Configuração de relatório não encontrada para:", item);
			return;
		}

		const codigoPessoa = this.session.loggedUser.professorResponse.codigoPessoa;
		const tipoBusca = this.identificarTipoBusca(item.title);

		const pctModal = this.modal.open(
			this.traducoes.getLabel(item.title),
			RelatorioComponent,
			PactoModalSize.LARGE,
			"modal-mxl"
		);

		this.relatorio = pctModal.componentInstance;
		this.relatorio.rowClick.subscribe(($item) => {
			window
				.open(
					`cadastros/alunos/perfil/${$item.matricula}%3Forigem%3Dbi`,
					"_blank"
				)
				.focus();
		});

		this.config = {
			endpointUrl: this.rest.buildFullUrl(
				`${item.endpoint}/${tipoBusca}/${codigoPessoa}`
			),
			pagination: item.pagination,
			quickSearch: true,
			exportButton: false,
			rowClick: true,
			columns: [],
		};

		item.columns.forEach((column) => {
			const columnConfig: any = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: true,
				visible: true,
				ordenavel: column.ordenavel,
				celula: null,
				valueTransform:
					column.date && column.value !== "dataVigenciaAteAjustadaApresentar"
						? (d) => (d === "-" ? d : moment(d).format("HH:MM - DD/MM/YY"))
						: null,
			};
			this.config.columns.push(columnConfig);
		});

		this.relatorio.table = new PactoDataGridConfig(this.config);
		this.relatorio.baseFilter = {
			filters: {
				professorId: this.professorFiltro,
				codigoPessoa: this.professorCodigoPessoaFiltro,
			},
		};
	}

	private identificarTipoBusca(indicador: string): number {
		if (indicador.includes("avaliacaoAcompanhamentoTotal")) return 0;
		if (indicador.includes("avaliacaoAcompanhamento1")) return 1;
		if (indicador.includes("avaliacaoAcompanhamento2")) return 2;
		if (indicador.includes("avaliacaoAcompanhamento3")) return 3;
		if (indicador.includes("avaliacaoAcompanhamento4")) return 4;
		return 5;
	}
}
