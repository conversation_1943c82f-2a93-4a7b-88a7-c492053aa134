<pacto-cat-card-plain *ngIf="avaliacaoAcompanhamento">
	<div>
		<div
			class="type-h5 div-padding-bottom-30"
			i18n="@@treino-bi:avaliacao-media-acompanhamento">
			Avaliação do acompanhamento
		</div>
	</div>

	<div class="star-wrapper row div-padding-bottom-30">
		<div
			(click)="
				ClickHandler(
					'avaliacaoAcompanhamento1',
					avaliacaoAcompanhamento?.nr1EstrelaAcompanhamento
				)
			"
			class="margin-element div-star cursor-pointer"
			title="{{ title1estrela }}">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoAcompanhamento?.nr1EstrelaAcompanhamento"
					[tamanho]="45"></pacto-star>
			</div>
		</div>
		<div
			(click)="
				ClickHandler(
					'avaliacaoAcompanhamento2',
					avaliacaoAcompanhamento?.nr2EstrelasAcompanhamento
				)
			"
			class="margin-element div-star cursor-pointer"
			title="{{ title2estrelas }}">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoAcompanhamento?.nr2EstrelasAcompanhamento"
					[tamanho]="45"></pacto-star>
			</div>
		</div>
		<div
			(click)="
				ClickHandler(
					'avaliacaoAcompanhamento3',
					avaliacaoAcompanhamento?.nr3EstrelasAcompanhamento
				)
			"
			class="margin-element div-star cursor-pointer"
			title="{{ title3estrelas }}">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoAcompanhamento?.nr3EstrelasAcompanhamento"
					[tamanho]="45"></pacto-star>
			</div>
		</div>
		<div
			(click)="
				ClickHandler(
					'avaliacaoAcompanhamento4',
					avaliacaoAcompanhamento?.nr4EstrelasAcompanhamento
				)
			"
			class="margin-element div-star cursor-pointer"
			title="{{ title4estrelas }}">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoAcompanhamento?.nr4EstrelasAcompanhamento"
					[tamanho]="45"></pacto-star>
			</div>
		</div>
		<div
			(click)="
				ClickHandler(
					'avaliacaoAcompanhamento5',
					avaliacaoAcompanhamento?.nr5EstrelasAcompanhamento
				)
			"
			class="margin-element div-star cursor-pointer"
			title="{{ title5estrelas }}">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoAcompanhamento?.nr5EstrelasAcompanhamento"
					[tamanho]="45"></pacto-star>
			</div>
		</div>
	</div>
	<div class="center">total</div>
	<div class="center font-25-bold">
		<span
			(click)="
				ClickHandler(
					'avaliacaoAcompanhamentoTotal',
					avaliacaoAcompanhamento?.nrAvaliacoesAcompanhamento
				)
			"
			class="cursor-pointer"
			i18n-title="@@treino-bi:avaliacao-total-acompanhamento"
			title="{{ titleAvaliacaoTotal }}">
			{{ avaliacaoAcompanhamento?.nrAvaliacoesAcompanhamento }} avaliações
		</span>
	</div>
</pacto-cat-card-plain>

<ng-template #notaCelula let-item="item">
	<pacto-star
		[preenchidas]="item.nota - 1"
		[repeticoes]="5"
		[tamanho]="35"></pacto-star>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span xingling="avaliacaoAcompanhamento1Title">
		Alunos que avaliaram o acompanhamento com 1 estrela
	</span>
	<span xingling="avaliacaoAcompanhamento2Title">
		Alunos que avaliaram o acompanhamento com 2 estrelas
	</span>
	<span xingling="avaliacaoAcompanhamento3Title">
		Alunos que avaliaram o acompanhamento com 3 estrelas
	</span>
	<span xingling="avaliacaoAcompanhamento4Title">
		Alunos que avaliaram o acompanhamento com 4 estrelas
	</span>
	<span xingling="avaliacaoAcompanhamento5Title">
		Alunos que avaliaram o acompanhamento com 5 estrelas
	</span>
	<span xingling="avaliacaoAcompanhamentoTotalTitle">
		Todas as avaliações de acompanhamento
	</span>
	<ng-template xingling="matricula">Matrícula</ng-template>
	<ng-template xingling="nomeAluno">Nome</ng-template>
	<ng-template xingling="dataHoraInicioApresentar">Hora</ng-template>
	<ng-template xingling="comentario">Comentário</ng-template>
	<ng-template xingling="professorCarteiraApresentar">Professor</ng-template>
	<ng-template xingling="nota">Nota</ng-template>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="sem-permissao">
		A visualização detalhada está liberada apenas para os usuários que possuem a
		permissão habilitada no seu perfil de acesso.
	</span>
</pacto-traducoes-xingling>
