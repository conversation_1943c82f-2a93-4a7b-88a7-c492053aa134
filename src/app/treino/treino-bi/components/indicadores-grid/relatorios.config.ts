export interface ModalRelatorioConfig {
	title: string;
	pagination?: boolean;
	endpoint: string;
	columns: {
		value: any;
		date?: boolean;
		ordenavel?: boolean;
	}[];
}

export const relatorios: { [relatorio: string]: ModalRelatorioConfig } = {
	totalAlunos: {
		title: "totalAlunosTitle",
		endpoint: "treino-bi/alunos-total",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "situacao", ordenavel: false },
			{ value: "nomeProfessor" },
		],
	},
	alunosAtivos: {
		title: "alunosAtivosTitle",
		endpoint: "treino-bi/alunos-ativos",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	todosAlunosAtivos: {
		title: "todosAlunosAtivosTitle",
		endpoint: "bi-app/ativos",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	usamApp: {
		title: "usamAppTitle",
		endpoint: "bi-app/usam-app",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	naoUsamApp: {
		title: "naoUsamAppTitle",
		endpoint: "bi-app/nao-usam-app",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	inativosApp: {
		title: "inativosAppTitle",
		endpoint: "bi-app/inativos-app",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	alunosInativos: {
		title: "alunosInativosTitle",
		endpoint: "treino-bi/alunos-inativos",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	alunosVisitantes: {
		title: "alunosVisitantesTitle",
		endpoint: "treino-bi/alunos-visitantes",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeProfessor" },
			{ value: "nomeAbreviado" },
		],
	},
	treinosVencidos: {
		title: "treinoVencidoTitle",
		endpoint: "treino-bi/alunos-treino-vencido",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
			{ value: "dataPrograma", date: true, ordenavel: true },
			{ value: "dataUltimoacesso", date: true, ordenavel: false },
		],
	},
	alunoSemTreino: {
		title: "alunoSemTreinoTitle",
		endpoint: "treino-bi/alunos-ativo-sem-treino",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	treinoAVencer: {
		title: "treinoAVencerTitle",
		endpoint: "treino-bi/alunos-treino-a-renovar",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
			{ value: "dataPrograma", date: true, ordenavel: true },
			{ value: "dataUltimoacesso", date: true, ordenavel: false },
		],
	},
	contratoAVencer: {
		title: "contratoAVencerTitle",
		endpoint: "treino-bi/alunos-vencer-30-dias",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nome" },
			{ value: "nomeProfessor" },
			{
				value: "dataVigenciaAteAjustadaApresentar",
				date: true,
				ordenavel: false,
			},
		],
	},
	treinoEmDia: {
		title: "treinoEmDiaTitle",
		endpoint: "treino-bi/alunos-treino-em-dia",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
			{ value: "dataPrograma", date: true, ordenavel: true },
			{ value: "dataUltimoacesso", date: true, ordenavel: false },
		],
	},
	alunosComTreino: {
		title: "treinoComTreinoTitle",
		endpoint: "treino-bi/alunos-ativos-treino",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	contratoRenovado: {
		title: "contratoRenovadoTitle",
		endpoint: "treino-bi/alunos-renovaram",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	avaliacaoMediaTreino1: {
		title: "avaliacaoMediaTreino1Title",
		endpoint: "treino-bi/avaliacao-treino",
		columns: [
			{ value: "matricula" },
			{ value: "nomeAluno" },
			{ value: "dataHoraInicioApresentar", date: true },
			{ value: "comentario" },
			{ value: "professorCarteiraApresentar" },
		],
	},
	avaliacaoMediaTreino2: {
		title: "avaliacaoMediaTreino2Title",
		endpoint: "treino-bi/avaliacao-treino",
		columns: [
			{ value: "matricula" },
			{ value: "nomeAluno" },
			{ value: "dataHoraInicioApresentar", date: true },
			{ value: "comentario" },
			{ value: "professorCarteiraApresentar" },
		],
	},
	avaliacaoMediaTreino3: {
		title: "avaliacaoMediaTreino3Title",
		endpoint: "treino-bi/avaliacao-treino",
		columns: [
			{ value: "matricula" },
			{ value: "nomeAluno" },
			{ value: "dataHoraInicioApresentar", date: true },
			{ value: "comentario" },
			{ value: "professorCarteiraApresentar" },
		],
	},
	avaliacaoMediaTreino4: {
		title: "avaliacaoMediaTreino4Title",
		endpoint: "treino-bi/avaliacao-treino",
		columns: [
			{ value: "matricula" },
			{ value: "nomeAluno" },
			{ value: "dataHoraInicioApresentar", date: true },
			{ value: "comentario" },
			{ value: "professorCarteiraApresentar" },
		],
	},
	avaliacaoMediaTreino5: {
		title: "avaliacaoMediaTreino5Title",
		endpoint: "treino-bi/avaliacao-treino",
		columns: [
			{ value: "matricula" },
			{ value: "nomeAluno" },
			{ value: "dataHoraInicioApresentar", date: true },
			{ value: "comentario" },
			{ value: "professorCarteiraApresentar" },
		],
	},
	avaliacaoTotalTreino: {
		title: "avaliacaoTotalTreinoTitle",
		endpoint: "treino-bi/avaliacao-treino",
		columns: [
			{ value: "matricula" },
			{ value: "nomeAluno" },
			{ value: "dataHoraInicioApresentar", date: true },
			{ value: "comentario" },
			{ value: "professorCarteiraApresentar" },
			{ value: "nota" },
		],
	},
	avaliacaoAcompanhamento1: {
		title: "avaliacaoAcompanhamento1Title",
		endpoint: "treino-bi/avaliacao-acompanhamento",
		pagination: true,
		columns: [
			{ value: "matricula", ordenavel: true },
			{ value: "nomeAluno", ordenavel: true },
			{ value: "dataHoraInicioApresentar", date: true, ordenavel: true },
			{ value: "comentario", ordenavel: false },
			{ value: "professorCarteiraApresentar", ordenavel: true },
			{ value: "nota", ordenavel: true },
		],
	},
	avaliacaoAcompanhamento2: {
		title: "avaliacaoAcompanhamento2Title",
		endpoint: "treino-bi/avaliacao-acompanhamento",
		pagination: true,
		columns: [
			{ value: "matricula", ordenavel: true },
			{ value: "nomeAluno", ordenavel: true },
			{ value: "dataHoraInicioApresentar", date: true, ordenavel: true },
			{ value: "comentario", ordenavel: false },
			{ value: "professorCarteiraApresentar", ordenavel: true },
			{ value: "nota", ordenavel: true },
		],
	},
	avaliacaoAcompanhamento3: {
		title: "avaliacaoAcompanhamento3Title",
		endpoint: "treino-bi/avaliacao-acompanhamento",
		pagination: true,
		columns: [
			{ value: "matricula", ordenavel: true },
			{ value: "nomeAluno", ordenavel: true },
			{ value: "dataHoraInicioApresentar", date: true, ordenavel: true },
			{ value: "comentario", ordenavel: false },
			{ value: "professorCarteiraApresentar", ordenavel: true },
			{ value: "nota", ordenavel: true },
		],
	},
	avaliacaoAcompanhamento4: {
		title: "avaliacaoAcompanhamento4Title",
		endpoint: "treino-bi/avaliacao-acompanhamento",
		pagination: true,
		columns: [
			{ value: "matricula", ordenavel: true },
			{ value: "nomeAluno", ordenavel: true },
			{ value: "dataHoraInicioApresentar", date: true, ordenavel: true },
			{ value: "comentario", ordenavel: false },
			{ value: "professorCarteiraApresentar", ordenavel: true },
			{ value: "nota", ordenavel: true },
		],
	},
	avaliacaoAcompanhamento5: {
		title: "avaliacaoAcompanhamento5Title",
		endpoint: "treino-bi/avaliacao-acompanhamento",
		pagination: true,
		columns: [
			{ value: "matricula", ordenavel: true },
			{ value: "nomeAluno", ordenavel: true },
			{ value: "dataHoraInicioApresentar", date: true, ordenavel: true },
			{ value: "comentario", ordenavel: false },
			{ value: "professorCarteiraApresentar", ordenavel: true },
			{ value: "nota", ordenavel: true },
		],
	},
	avaliacaoAcompanhamentoTotal: {
		title: "avaliacaoAcompanhamentoTotalTitle",
		endpoint: "treino-bi/avaliacao-acompanhamento",
		pagination: true,
		columns: [
			{ value: "matricula", ordenavel: true },
			{ value: "nomeAluno", ordenavel: true },
			{ value: "dataHoraInicioApresentar", date: true, ordenavel: true },
			{ value: "comentario", ordenavel: false },
			{ value: "professorCarteiraApresentar", ordenavel: true },
			{ value: "nota", ordenavel: true },
		],
	},
	alunosSemAcompanhamento: {
		title: "alunosSemAcompanhamentoTitle",
		endpoint: "treino-bi/alunos-sem-acompanhamento",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "urlFoto" },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	alunosEmAcompanhamento: {
		title: "alunosEmAcompanhamentoTitle",
		endpoint: "treino-bi/alunos-em-acompanhamento",
		columns: [
			{ value: "matricula", ordenavel: false },
			{ value: "urlFoto" },
			{ value: "nomeAbreviado" },
			{ value: "nomeProfessor" },
		],
	},
	professoresPorPerfilAcesso: {
		title: "professoresPorPerfilAcesso",
		endpoint: "perfis-acesso/consultar-professores-por-perfil",
		columns: [
			{ value: "id", ordenavel: false },
			{ value: "nome", ordenavel: true },
		],
	},
};
