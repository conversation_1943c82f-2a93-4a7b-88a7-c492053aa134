import {
	Component,
	OnInit,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	ViewChild,
	Input,
	OnDestroy,
	TemplateRef,
} from "@angular/core";
import { PactoColor, PactoIcon, PactoDataGridButtonConfig } from "ui-kit";
import { TreinoBiStateService } from "../treino-bi-home-v2/treino-bi-state.service";
import {
	TreinoBiTreinamento,
	TreinoBiCarteira,
} from "@treino-core/treino-bi/treino-bi2.model";
import { SessionService } from "@base-core/client/session.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import {
	RelatorioComponent,
	PactoDataGridConfig,
	PactoDataGridConfigDto,
	TraducoesXinglingComponent,
} from "ui-kit";

declare var moment;

import { relatorios, ModalRelatorioConfig } from "./relatorios.config";
import { RestService } from "@base-core/rest/rest.service";
import { Subscription } from "rxjs";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";
import { ShareComponent } from "src/app/share/share.component";
import { TreinoApiBiService, ProgramaAcompanhamento } from "treino-api";
import { SnotifyService } from "ng-snotify";
import { ModalAcompanharTreinoComponent } from "../modal-acompanhar-treino/modal-acompanhar-treino.component";
import { Router } from "@angular/router";

@Component({
	selector: "pacto-indicadores-grid",
	templateUrl: "./indicadores-grid.component.html",
	styleUrls: ["./indicadores-grid.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IndicadoresGridComponent implements OnInit, OnDestroy {
	@Input() professorFiltro;
	@Input() professorCodigoPessoaFiltro;
	@Input() professorData;
	@ViewChild("tooltipIniciarAcompanhamento", { static: true })
	tooltipIniciarAcompanhamento;
	@ViewChild("tooltipAcompanharTreino", { static: true })
	tooltipAcompanharTreino;
	@ViewChild("iniciarModalMsg", { static: true }) iniciarModalMsg;
	@ViewChild("inseridoModalMsg", { static: true }) inseridoModalMsg;
	@ViewChild("semProgramaVigenteModalMsg", { static: true })
	semProgramaVigenteModalMsg;
	@ViewChild("imageCelula", { static: true }) imageCelula;
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	rawDataTable: any[];
	config: PactoDataGridConfigDto;
	relatorio: RelatorioComponent;

	constructor(
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private modal: ModalService,
		private rest: RestService,
		private snotifyService: SnotifyService,
		private treinoBiService: TreinoApiBiService,
		private state: TreinoBiStateService,
		private configuratoiService: TreinoConfigCacheService,
		private router: Router
	) {}

	treinamento: TreinoBiTreinamento;
	carteira: TreinoBiCarteira;
	integradoZW = true;
	subscription: Subscription;
	endpointShare: string;
	codigosAlunosAcompanhados: string = null;

	treinoVencidosPerc = null;
	semTreinoPerc = null;
	aVencerPerc = null;
	contratoAVencerPerc = null;
	programaEmDiaPerc = null;
	ativoComTreinoPerc = null;
	renovadosNosUltimos30Dias = null;
	totalAlunosAtivos;
	totalRenovacoesCarteira;

	ngOnInit() {
		this.integradoZW = this.session.integracaoZW;
		this.subscription = this.state.update$.subscribe((ready) => {
			if (ready) {
				this.setupData();
			}
		});
	}

	ngOnDestroy() {
		this.subscription.unsubscribe();
	}

	get tempoMedio() {
		if (this.treinamento) {
			return this.treinamento.tempoPermanenciaPrograma.medio;
		} else {
			return null;
		}
	}

	get hintAlunosInativos() {
		let response = "";
		if (this.integradoZW) {
			const somente_aluno_contrato_desistente =
				this.configuratoiService.configuracoesGestao
					.somente_aluno_contrato_desistente;
			if (
				somente_aluno_contrato_desistente &&
				JSON.parse(somente_aluno_contrato_desistente)
			) {
				response =
					'São contabilizados os alunos IN DE vinculados ao professor. Obs: A configuração "Considerar alunos IN DE" ' +
					"esta marcada, então o total de ativos + inativos não será igual ao total de alunos";
			} else {
				response =
					"São contabilizados os alunos vinculados ao professor e na seguinte situações (IN DE, IN VE, CA E TR). Não são considerados as seguintes situações (AE, CR)";
			}
		} else {
			response =
				"São contabilizados os alunos vinculados ao professor e na seguinte situação (IN)";
		}
		return response;
	}

	cardClickHandler(name, numeroIndicador) {
		try {
			const rota = this.router.url;
			localStorage.removeItem("config_table__" + rota);
		} catch (e) {
			console.log("problema ao limpar filtro dash");
		}
		if (name === "alunosSemAcompanhamento") {
			this.openModalHandlerAction(relatorios[name]);
		} else if (name === "alunosEmAcompanhamento") {
			this.openModalHandlerAction(relatorios[name]);
		} else if (name === "alunoSemTreino") {
			let params;
			if (this.professorData !== null && this.professorData !== undefined) {
				params = {
					primario_nome: this.professorData.nome,
					primario_id: this.professorData.codigoProfessorsintetico,
					secundario_nome: "Sem treino",
					secundario_id: "SEM_TREINO",
					situacao_aluno: "AT",
					origem_acao: "BI_TREINO",
				};
				this.router.navigate(["treino/montagem-programa/prescricao"], {
					queryParams: params,
				});
			} else {
				params = {
					secundario_nome: "Sem treino",
					secundario_id: "SEM_TREINO",
					situacao_aluno: "AT",
					origem_acao: "BI_TREINO",
				};
				this.router.navigate(["treino/montagem-programa/prescricao"], {
					queryParams: params,
				});
			}
		} else if (name === "treinosVencidos") {
			let params;
			if (this.professorData !== null && this.professorData !== undefined) {
				params = {
					primario_nome: this.professorData.nome,
					primario_id: this.professorData.codigoProfessorsintetico,
					secundario_nome: "Vencido",
					secundario_id: "VENCIDO",
					situacao_aluno: "AT",
					origem_acao: "BI_TREINO",
				};
			} else {
				params = {
					secundario_nome: "Vencido",
					secundario_id: "VENCIDO",
					situacao_aluno: "AT",
					origem_acao: "BI_TREINO",
				};
			}
			this.router.navigate(["treino/montagem-programa/prescricao"], {
				queryParams: params,
			});
		} else if (name === "treinoAVencer") {
			let params;
			if (this.professorData !== null && this.professorData !== undefined) {
				params = {
					primario_nome: this.professorData.nome,
					primario_id: this.professorData.codigoProfessorsintetico,
					secundario_nome: "A vencer",
					secundario_id: "A_VENCER",
					situacao_aluno: "AT",
					origem_acao: "BI_TREINO",
				};
			} else {
				params = {
					secundario_nome: "A vencer",
					secundario_id: "A_VENCER",
					situacao_aluno: "AT",
					origem_acao: "BI_TREINO",
				};
			}
			this.router.navigate(["treino/montagem-programa/prescricao"], {
				queryParams: params,
			});
		} else {
			this.openModalHandler(relatorios[name], numeroIndicador);
		}
	}

	private setupData() {
		this.treinamento = this.state.treinamento;
		this.carteira = this.state.carteira;
		this.totalRenovacoesCarteira = this.carteira.totalRenovacoesCarteira;
		this.totalAlunosAtivos =
			this.treinamento.alunosAtivosSemTreino +
			this.treinamento.alunosAtivosComTreino;

		this.treinoVencidosPerc = this.calcPerc(
			this.treinamento.alunosProgramaVencidos,
			this.treinamento.alunosAtivosComTreino
		);

		this.semTreinoPerc = this.calcPerc(
			this.treinamento.alunosAtivosSemTreino,
			this.totalAlunosAtivos
		);

		this.aVencerPerc = this.calcPerc(
			this.treinamento.alunosProgramaRenovar,
			this.treinamento.alunosAtivosComTreino
		);

		this.contratoAVencerPerc = this.calcPerc(
			this.carteira.aVencerZW,
			this.carteira.ativos
		);

		this.programaEmDiaPerc = this.calcPerc(
			this.treinamento.alunosAtivosProgramaEmDia,
			this.treinamento.alunosAtivosComTreino
		);

		this.ativoComTreinoPerc = this.calcPerc(
			this.treinamento.alunosAtivosComTreino,
			this.totalAlunosAtivos
		);

		if (this.carteira) {
			this.renovadosNosUltimos30Dias = Math.trunc(
				(this.carteira.taxaRenovacaoZW * this.carteira.ativos) / 100
			);
		}
		this.cd.detectChanges();
	}

	private openModalHandlerAction(item: ModalRelatorioConfig) {
		const codigoPessoa = this.session.loggedUser.professorResponse.codigoPessoa;
		this.endpointShare = this.rest.buildFullUrl(
			item.endpoint + "/" + codigoPessoa
		);
		const pctModal = this.modal.open(
			this.traducoes.getLabel(item.title),
			RelatorioComponent,
			PactoModalSize.LARGE
		);
		const relatorio: RelatorioComponent = pctModal.componentInstance;
		relatorio.telaId = item.title;
		relatorio.sessionService = this.session;
		relatorio.iconClick.subscribe(($item) => {
			if ($item.iconName === "situacao") {
				this.iniciarAcompanharTreino($item.row);
			} else if ($item.iconName === "acompanhamento") {
				this.acompanharTreino($item.row);
			}
		});
		let toolTipe =
			item.title == "alunosEmAcompanhamentoTitle"
				? this.tooltipAcompanharTreino.nativeElement.innerHTML
				: this.tooltipIniciarAcompanhamento.nativeElement.innerHTML;
		const config: PactoDataGridConfigDto = {
			endpointUrl: this.rest.buildFullUrl(item.endpoint + "/" + codigoPessoa),
			logUrl: this.rest.buildFullUrl("log/listar-log-exportacao/" + item.title),
			pagination: true,
			quickSearch: true,
			columns: [],
			actions: [
				{
					nome: "acompanhamento",
					iconClass: "fa fa-calendar-o",
					tooltipText: toolTipe,
				},
			],
		};
		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: false,
				visible: true,
				ordenavel: column.ordenavel,
				valueTransform: null,
				date: false,
				celula: null,
			};
			if (column.value === "urlFoto") {
				columnConfig.celula = this.imageCelula;
			}
			config.columns.push(columnConfig);
		});
		relatorio.table = new PactoDataGridConfig(config);
		this.cd.detectChanges();
		relatorio.baseFilter = {
			filters: {
				professorId: this.professorFiltro,
				codigoPessoa: this.professorCodigoPessoaFiltro,
			},
		};
	}

	iniciarAcompanharTreino(treino) {
		const modalSucessoMsg = this.iniciarModalMsg.nativeElement.innerHTML;
		const modalMsg = this.inseridoModalMsg.nativeElement.innerHTML;
		if (
			this.codigosAlunosAcompanhados == null ||
			!this.codigosAlunosAcompanhados.includes(treino.matricula)
		) {
			this.treinoBiService
				.iniciarAcompanhamento(treino.codigo, this.professorCodigoPessoaFiltro)
				.subscribe(() => {
					this.snotifyService.success(modalSucessoMsg);
					this.cd.detectChanges();
				});
		} else {
			this.snotifyService.error(modalMsg);
		}
	}

	acompanharTreino(row) {
		const modalMsg = this.semProgramaVigenteModalMsg.nativeElement.innerHTML;
		this.treinoBiService
			.obterAlunoAcompanhamento(row.codigo)
			.subscribe((response: ProgramaAcompanhamento) => {
				if (response.programa == null) {
					this.snotifyService.error(modalMsg);
				} else {
					this.iniciarAcompanharTreino(row);
					const modalAcompanharTreino = this.modal.open(
						"Programa Predefinido",
						ModalAcompanharTreinoComponent,
						PactoModalSize.LARGE,
						"modal-acompanhar-treino"
					);
					modalAcompanharTreino.componentInstance.programaAcompanhamento =
						response;
					modalAcompanharTreino.componentInstance.professorAcompanhamento =
						this.professorCodigoPessoaFiltro;
					modalAcompanharTreino.componentInstance.alunoAcompanhamento =
						row.codigo;
				}
			});
	}

	private openModalHandler(
		item: ModalRelatorioConfig,
		numeroIndicador: number
	) {
		try {
			const rota = this.router.url;
			localStorage.removeItem("config_table__" + rota);
		} catch (e) {
			console.log("problema ao limpar filtro dash");
		}
		const codigoPessoa = this.session.loggedUser.professorResponse.codigoPessoa;
		this.endpointShare = this.rest.buildFullUrl(
			item.endpoint + "/" + codigoPessoa
		);
		const pctModal = this.modal.open(
			this.traducoes.getLabel(item.title),
			RelatorioComponent,
			PactoModalSize.LARGE
		);
		this.relatorio = pctModal.componentInstance;
		this.relatorio.telaId = item.title;
		this.relatorio.sessionService = this.session;
		if (this.session.isTreinoIndependente()) {
			this.relatorio.rowClick.subscribe(($item) => {
				window
					.open(
						"cadastros/alunos/perfil/" + $item.matricula + "%3Forigem%3Dbi",
						"_blank"
					)
					.focus();
			});
		} else {
			this.relatorio.rowClick.subscribe(($item) => {
				window
					.open("pessoas/perfil-v2/" + $item.matricula + "/contratos", "_blank")
					.focus();
			});
		}

		if (numeroIndicador > 0) {
			this.config = {
				endpointUrl: this.rest.buildFullUrl(item.endpoint + "/" + codigoPessoa),
				logUrl: this.rest.buildFullUrl(
					"log/listar-log-exportacao/" + item.title
				),
				pagination: item.pagination,
				quickSearch: true,
				exportButton: false,
				rowClick: true,
				columns: [],
			};
		} else {
			this.config = {
				endpointUrl: this.rest.buildFullUrl(item.endpoint + "/" + codigoPessoa),
				logUrl: this.rest.buildFullUrl(
					"log/listar-log-exportacao/" + item.title
				),
				pagination: item.pagination,
				quickSearch: true,
				exportButton: false,
				rowClick: true,
				columns: [],
			};
		}
		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: true,
				visible: true,
				ordenavel: column.ordenavel,
				valueTransform: null,
			};
			if (
				column.date &&
				!(column.value === "dataVigenciaAteAjustadaApresentar")
			) {
				columnConfig.valueTransform = (d) =>
					d === "-" ? d : moment(d).format("DD/MM/YYYY");
			}
			this.config.columns.push(columnConfig);
		});
		this.relatorio.table = new PactoDataGridConfig(this.config);
		this.relatorio.baseFilter = {
			filters: {
				professorId: this.professorFiltro,
				codigoPessoa: this.professorCodigoPessoaFiltro,
			},
		};
		this.relatorio.btnClick.subscribe(() => {
			this.openModalShare(item);
		});
	}

	private openModalShare(item: ModalRelatorioConfig) {
		const pctModal = this.modal.open(
			"Compartilhar",
			ShareComponent,
			PactoModalSize.MEDIUM
		);
		const relatorio: RelatorioComponent = pctModal.componentInstance;
		const config: PactoDataGridConfigDto = {
			endpointUrl: this.endpointShare,
			columns: [],
		};
		item.columns.forEach((column) => {
			const columnConfig = {
				nome: column.value,
				titulo: this.traducoes.getTemplate(column.value),
				buscaRapida: true,
				visible: true,
				ordenavel: column.ordenavel,
				valueTransform: null,
			};
			if (
				column.date &&
				!(column.value === "dataVigenciaAteAjustadaApresentar")
			) {
				columnConfig.valueTransform = (d) =>
					d === "-" ? d : moment(d).format("DD/MM/YYYY");
			}
			config.columns.push(columnConfig);
		});
		relatorio.table = new PactoDataGridConfig(config);
		const filtroModal = this.relatorio.fetchFiltros();
		const filtroProf = {
			filters: {
				professorId: this.professorFiltro,
			},
		};
		relatorio.baseFilter = Object.assign(filtroModal, filtroProf);
		relatorio.baseFilter.size = 999999;
	}

	/**
	 * @return 21% or null if not able to calculate.
	 */
	private calcPerc(part: number, whole: number): number {
		if (isNaN(part) || isNaN(whole) || whole === 0) {
			return null;
		} else {
			const percent = part / whole;
			return Math.round(percent * 100);
		}
	}

	get PactoIcon() {
		return PactoIcon;
	}

	get PactoColor() {
		return PactoColor;
	}

	getImagemAluno(imagem: string) {
		if (imagem) {
			return "url(" + imagem + ")";
		} else {
			return "url(assets/images/default-user-icon.png)";
		}
	}

	public closeHandler(): void {}
}
