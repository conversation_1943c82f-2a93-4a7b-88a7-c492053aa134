<pacto-cat-card-plain>
	<div>
		<div
			class="type-h5 div-padding-bottom-30"
			i18n="@@treino-bi:avaliacao-media-treino-alunos">
			Avaliação do treino do aluno
		</div>
	</div>
	<div class="star-wrapper row div-padding-bottom-30">
		<div
			(click)="
				ClickHandler('avaliacaoMediaTreino1', avaliacaoTreino?.nr1estrela)
			"
			class="margin-element div-star cursor-pointer"
			i18n-title="@@treino-bi:avaliacao-1-estrela"
			title="{{ avaliacaoTreino?.nr1estrela }} alunos deram 1 estrela">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoTreino?.nr1estrela"
					[tamanho]="45"></pacto-star>
			</div>
		</div>

		<div
			(click)="
				ClickHandler('avaliacaoMediaTreino2', avaliacaoTreino?.nr2estrelas)
			"
			class="margin-element div-star cursor-pointer"
			i18n-title="@@treino-bi:avaliacao-2-estrelas"
			title="{{ avaliacaoTreino?.nr2estrelas }} alunos deram 2 estrelas">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoTreino?.nr2estrelas"
					[tamanho]="45"></pacto-star>
			</div>
		</div>

		<div
			(click)="
				ClickHandler('avaliacaoMediaTreino3', avaliacaoTreino?.nr3estrelas)
			"
			class="margin-element div-star cursor-pointer"
			i18n-title="@@treino-bi:avaliacao-3-estrelas"
			title="{{ avaliacaoTreino?.nr3estrelas }} alunos deram 3 estrelas">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoTreino?.nr3estrelas"
					[tamanho]="45"></pacto-star>
			</div>
		</div>

		<div
			(click)="
				ClickHandler('avaliacaoMediaTreino4', avaliacaoTreino?.nr4estrelas)
			"
			class="margin-element div-star cursor-pointer"
			i18n-title="@@treino-bi:avaliacao-4-estrelas"
			title="{{ avaliacaoTreino?.nr4estrelas }} alunos deram 4 estrelas">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoTreino?.nr4estrelas"
					[tamanho]="45"></pacto-star>
			</div>
		</div>

		<div
			(click)="
				ClickHandler('avaliacaoMediaTreino5', avaliacaoTreino?.nr5estrelas)
			"
			class="margin-element div-star cursor-pointer"
			i18n-title="@@treino-bi:avaliacao-5-estrelas"
			title="{{ avaliacaoTreino?.nr5estrelas }} alunos deram 5 estrelas">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoTreino?.nr5estrelas"
					[tamanho]="45"></pacto-star>
			</div>
		</div>
	</div>

	<div class="center">total</div>
	<div class="center font-25-bold">
		<span
			(click)="
				ClickHandler(
					'avaliacaoTotalTreino',
					avaliacaoTreino?.nrAvaliacoesTreino
				)
			"
			class="cursor-pointer"
			i18n="@@treino-bi:avaliacao-0-estrela">
			{{ avaliacaoTreino?.nrAvaliacoesTreino }} avaliações
		</span>
	</div>
</pacto-cat-card-plain>

<ng-template #notaCelula let-item="item">
	<pacto-star
		[preenchidas]="item.nota - 1"
		[repeticoes]="5"
		[tamanho]="35"></pacto-star>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span xingling="avaliacaoMediaTreino1Title">{{ title1estrela }}</span>
	<span xingling="avaliacaoMediaTreino2Title">{{ title2estrelas }}</span>
	<span xingling="avaliacaoMediaTreino3Title">{{ title3estrelas }}</span>
	<span xingling="avaliacaoMediaTreino4Title">{{ title4estrelas }}</span>
	<span xingling="avaliacaoMediaTreino5Title">{{ title5estrelas }}</span>
	<span xingling="avaliacaoTotalTreinoTitle">{{ titleAvaliacaoTotal }}</span>

	<ng-template xingling="matricula">Matrícula</ng-template>
	<ng-template xingling="nomeAluno">Nome</ng-template>
	<ng-template xingling="dataHoraInicioApresentar">Hora</ng-template>
	<ng-template xingling="comentario">Comentário</ng-template>
	<ng-template xingling="professorCarteiraApresentar">Professor</ng-template>
	<ng-template xingling="nota">Nota</ng-template>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="sem-permissao">
		A visualização detalhada está liberada apenas para os usuários que possuem a
		permissão habilitada no seu perfil de acesso.
	</span>
</pacto-traducoes-xingling>
