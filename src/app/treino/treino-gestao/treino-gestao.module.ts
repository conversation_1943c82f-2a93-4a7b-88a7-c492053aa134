import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";

import { CarteiraProfessoresComponent } from "./carteira-professores/carteira-professores.component";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { TreinoGestaoRoutingModule } from "./treino-gestao.routing.module";
import { AtividadeProfessoresComponent } from "./atividade-professores/atividade-professores.component";
import { RankingProfessoresComponent } from "./ranking-professores/ranking-professores.component";
import { ConfiguracaoRankingProfessorComponent } from "./ranking-professores/configuracao-ranking-professor/configuracao-ranking-professor.component";
import { AndamentoComponent } from "src/app/base/gestao/andamento/andamento.component";
import { PersonaisComponent } from "./personais/personais.component";
import { AcompanhamentoPersonalComponent } from "./acompanhamento-personal/acompanhamento-personal.component";
import { CreditoPersonalComponent } from "./credito-personal/credito-personal.component";
import { ColaboradoresPersonalComponent } from "./colaboradores-personal/colaboradores-personal.component";
import { ListaConfiguracaoRankingComponent } from "./ranking-professores/lista-configuracao-ranking/lista-configuracao-ranking.component";
import { ConfeteComponent } from "./confete/confete.component";
import { PodiumComponent } from "./podium/podium.component";
import { DetalhesProfessorComponent } from "./ranking-professores/detalhes-professor/detalhes-professor.component";
import { ProfessoresAlunosAvisoMedicoComponent } from "./professores-alunos-aviso-medico/professores-alunos-aviso-medico.component";
import { ExecucoesTreinoComponent } from "./execucoes-treino/execucoes-treino.component";

@NgModule({
	imports: [BaseSharedModule, TreinoGestaoRoutingModule, CommonModule],
	declarations: [
		CarteiraProfessoresComponent,
		AtividadeProfessoresComponent,
		ProfessoresAlunosAvisoMedicoComponent,
		RankingProfessoresComponent,
		ConfiguracaoRankingProfessorComponent,
		ListaConfiguracaoRankingComponent,
		DetalhesProfessorComponent,
		ConfeteComponent,
		PodiumComponent,
		AndamentoComponent,
		PersonaisComponent,
		AcompanhamentoPersonalComponent,
		CreditoPersonalComponent,
		ColaboradoresPersonalComponent,
		ExecucoesTreinoComponent,
	],
	entryComponents: [
		ConfiguracaoRankingProfessorComponent,
		ListaConfiguracaoRankingComponent,
		PodiumComponent,
		DetalhesProfessorComponent,
		ConfeteComponent,
	],
})
export class TreinoGestaoModule {}
