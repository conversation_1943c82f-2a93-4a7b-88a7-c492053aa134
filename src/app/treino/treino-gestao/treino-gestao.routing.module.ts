import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";

import { CarteiraProfessoresComponent } from "./carteira-professores/carteira-professores.component";
import { AtividadeProfessoresComponent } from "./atividade-professores/atividade-professores.component";
import { RankingProfessoresComponent } from "./ranking-professores/ranking-professores.component";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import { AndamentoComponent } from "src/app/base/gestao/andamento/andamento.component";
import { GestaoGeralGuard } from "@base-core/guards/gestao-geral.guard";
import { PersonaisComponent } from "./personais/personais.component";
import { AcompanhamentoPersonalComponent } from "./acompanhamento-personal/acompanhamento-personal.component";
import { CreditoPersonalComponent } from "./credito-personal/credito-personal.component";
import { ColaboradoresPersonalComponent } from "./colaboradores-personal/colaboradores-personal.component";
import { ProfessoresAlunosAvisoMedicoComponent } from "./professores-alunos-aviso-medico/professores-alunos-aviso-medico.component";
import { ExecucoesTreinoComponent } from "./execucoes-treino/execucoes-treino.component";

const funcionalidades = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.RANKING_PROFESSORES,
	true
);

const routes: Routes = [
	{
		path: "carteira-professores",
		component: CarteiraProfessoresComponent,
		canActivate: [GestaoGeralGuard],
	},
	{
		path: "atividade-professores",
		component: AtividadeProfessoresComponent,
		canActivate: [GestaoGeralGuard],
	},
	{
		path: "professores-alunos-aviso-medico",
		component: ProfessoresAlunosAvisoMedicoComponent,
		canActivate: [GestaoGeralGuard],
	},
	{
		path: "ranking-professores",
		component: RankingProfessoresComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
	{
		path: "andamento-programas",
		component: AndamentoComponent,
		canActivate: [GestaoGeralGuard],
	},
	{
		path: "execucoes-treino",
		component: ExecucoesTreinoComponent,
	},
	{
		path: "personais",
		component: PersonaisComponent,
	},
	{
		path: "acompanhamento-personal",
		component: AcompanhamentoPersonalComponent,
	},
	{
		path: "gestao-credito",
		component: CreditoPersonalComponent,
	},
	{
		path: "colaboradores-personal",
		component: ColaboradoresPersonalComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
})
export class TreinoGestaoRoutingModule {}
