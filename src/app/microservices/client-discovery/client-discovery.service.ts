import { SessionLogin } from "./../autenticacao/session-login.model";
import { Inject, Injectable, LOCALE_ID } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { ClientDiscoveryData, ServiceMap } from "./client-discovery.model";
import { ApiResponseSingle } from "@base-core/rest/rest.model";
import { environment } from "src/environments/environment";

import { Observable, of } from "rxjs";
import { catchError, map, pluck, tap } from "rxjs/operators";

@Injectable({
	providedIn: "root",
})
export class ClientDiscoveryService {
	domains: string[] = [];

	constructor(private http: HttpClient, @Inject(LOCALE_ID) private locale) {}

	get cache(): ClientDiscoveryData {
		return JSON.parse(sessionStorage.getItem("cache"));
	}

	set cache(cache: ClientDiscoveryData) {
		sessionStorage.setItem("cache", JSON.stringify(cache));
	}

	/**
	 * Descobre url de aplicaçãoes sem detalhes relacionados a chave
	 */
	public discoverUrls(): Observable<ClientDiscoveryData> {
		const url = `${environment.discoveryMsUrl}/find`;

		return this.http.get<ApiResponseSingle<ClientDiscoveryData>>(url).pipe(
			pluck("content"),
			tap((data) => {
				if (!data.serviceUrls.loginAppUrl) {
					this.log("Login URL não definidos no discovery");
					throw new Error("Resultado do discovery inválido");
				}
				return data;
			}),
			catchError(() => of(null))
		);
	}

	/**
	 * Caso não seja fornecido um token, é necessário informar uma chave.
	 */
	public discover(
		chave?: string,
		token?: string
	): Observable<ClientDiscoveryData> {
		let url;
		let headers = {};
		if (this.cache) {
			return of(this.cache);
		}
		if (!chave) {
			throw Error("Necessário definir a chave para comunicar com o discovery");
		}
		if (!token) {
			throw Error("Necessário definir o token para comunicar com o discovery");
		}
		url = `${environment.discoveryMsUrl}/find/${chave}`;
		headers = { Authorization: `Bearer ${token}` };
		return this.http
			.get<ApiResponseSingle<ClientDiscoveryData>>(url, { headers })
			.pipe(
				pluck("content"),
				map((data) => {
					if (data) {
						if (!data.modulosHabilitados) {
							data.modulosHabilitados = [];
						}
						if (!data.empresas) {
							data.empresas = [];
						}
					}
					return data;
				}),
				tap((data) => this.discoveryDataValid(data)),
				tap((data) => {
					this.cache = data;
					this.populateDomainList(data.serviceUrls);
				}),
				catchError((err) => {
					if (err.message && err.message.includes("discoveryDataValid")) {
						throw err;
					}
					return of(null);
				})
			);
	}

	linkZw(usuarioOamd: string, empresaId): Observable<any> {
		const url = `${environment.discoveryMsUrl}/linkzw`;
		let loginUrl = this.getUrlMap().loginAppUrl;
		if (
			environment.newLoginEnabled &&
			this.getUrlMap().loginFrontUrl !== undefined &&
			this.getUrlMap().loginFrontUrl !== ""
		) {
			loginUrl = `${this.getUrlMap().loginFrontUrl}/${this.locale}`;
		}

		const params: any = {};
		params.usuarioOamd = usuarioOamd;
		params.urlLogin = loginUrl;
		return this.http
			.get(url, {
				params,
				headers: {
					Authorization: `Bearer ${localStorage.getItem("apiToken")}`,
					empresaId,
				},
			})
			.pipe(
				map((response: ApiResponseSingle<string>) => {
					return response.content;
				})
			);
	}

	moduloUrl(moduloCode: string, empresaId): Observable<any> {
		const url = `${environment.discoveryMsUrl}/modulo-url/${moduloCode}`;
		const params: any = {};
		params.empresaId = empresaId;
		this.getObterEmailUsuarioLocalStorage();
		if (
			this.getObterEmailUsuarioLocalStorage() &&
			this.getObterEmailUsuarioLocalStorage().emailusuario
		) {
			params.emailUsuario =
				this.getObterEmailUsuarioLocalStorage().emailusuario;
		}
		return this.http
			.get(url, {
				params,
				headers: {
					Authorization: `Bearer ${localStorage.getItem("apiToken")}`,
				},
			})
			.pipe(
				map((response: ApiResponseSingle<string>) => {
					return response.content;
				})
			);
	}

	getObterEmailUsuarioLocalStorage(): any {
		const pactoZwString: string = localStorage.getItem("pactoZw");
		let pactoZw: any;
		if (pactoZwString) {
			try {
				pactoZw = JSON.parse(pactoZwString);
				return pactoZw;
			} catch (error) {
				console.error("Erro ao parsear pactoZw do localStorage:", error);
				pactoZw = null;
			}
		}
		return null;
	}

	getUrlMap(): ServiceMap {
		if (this.cache) {
			return this.cache.serviceUrls;
		} else {
			return null;
		}
	}

	isUsarChatMovDesk(): boolean {
		return (
			this.cache &&
			this.cache.utilizarMoviDesk &&
			this.cache.utilizarChatMoviDesk
		);
	}

	isUsarChatOctadesk(): boolean {
		return this.cache && this.cache.utilizarOctadesk;
	}

	isUsarChatGymbot(): boolean {
		return this.cache && this.cache.utilizarGymbot;
	}

	getIdChatMovDesk(): string {
		if (this.cache && this.cache.grupoChatMovidesk) {
			return this.cache.grupoChatMovidesk;
		} else {
			return "52429C99928D447E8E6D43F0B1F08F02";
		}
	}

	private populateDomainList(trustedUrls: object) {
		const domains = [];
		for (const key in trustedUrls) {
			if (trustedUrls.hasOwnProperty(key)) {
				const url = trustedUrls[key];
				domains.push(this.domainFromUrl(url));
			}
		}
		this.domains = domains;
	}

	private domainFromUrl(url) {
		let result = "";
		let match;
		if (url) {
			match = url.match(
				/^(?:https?:\/\/)?(?:[^@\n]+@)?(?:www\.)?([^:\/\n\?\=]+)/im
			);
			result = match[1];
		}
		return result;
	}

	private discoveryDataValid(dto: ClientDiscoveryData) {
		const modulos = dto.modulosHabilitados;
		const empresas = dto.empresas && dto.empresas.length;
		const treino = dto.serviceUrls.treinoApiUrl;
		const personagem = dto.serviceUrls.personagemMsUrl;
		const graduacao = dto.serviceUrls.graduacaoMsUrl;
		const relatorioFull = dto.serviceUrls.relatorioFull;

		if (!modulos) {
			this.log("Modulos Habilitados não definidos no discovery");
			throw new Error(
				"discoveryDataValid - Modulos Habilitados não definidos no discovery"
			);
		}
		if (!empresas) {
			this.log("Empresas não definidas no discovery");
			throw new Error("discoveryDataValid - Nenhuma empresa ativa na chave");
		}
		if (!treino) {
			this.log("Url da API do Treino não definida no discovery");
			throw new Error(
				"discoveryDataValid - Url da API do Treino não definida no discovery"
			);
		}
		if (!graduacao) {
			this.log("Url da API do Graduação não definida no discovery");
			throw new Error(
				"discoveryDataValid - Url da API do Graduação não definida no discovery"
			);
		}
		if (!relatorioFull) {
			this.log("Url da API do relatorioFull não definida no discovery");
		}
	}

	private log(message) {
		console.log(`>>>> SPA SETUP ERROR: ${message}`);
	}
}
