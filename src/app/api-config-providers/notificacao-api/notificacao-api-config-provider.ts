import { Injectable } from "@angular/core";
import {
	NotificacaoApiConfig,
	NotificacaoApiConfigProviderBaseService,
} from "notificacao-api";
import { Observable, of } from "rxjs";
import { ClientDiscoveryService, SessionService } from "sdk";

@Injectable()
export class NotificacaoApiConfigProvider extends NotificacaoApiConfigProviderBaseService {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<NotificacaoApiConfig> {
		return of({
			baseUrl: this.discoveryService.getUrlMap().notificacaoMs,
			authToken: this.sessionService.token,
		});
	}
}
