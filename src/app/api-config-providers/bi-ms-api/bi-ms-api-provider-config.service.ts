import { Injectable } from "@angular/core";
import { BiMsApiConfig, BiMsApiConfigProviderBase } from "bi-ms-api";
import { Observable, of } from "rxjs";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "../../microservices/client-discovery/client-discovery.service";

@Injectable()
export class BiMsApiProviderConfigService extends BiMsApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<BiMsApiConfig> {
		const empresaId = this.sessionService.empresaId;
		const baseUrl = this.discoveryService.getUrlMap().biMsUrl;
		const authToken = localStorage.getItem("apiToken");

		return of({
			authToken,
			baseUrl,
			empresaId: empresaId ? `${empresaId}` : null,
		});
	}
}
