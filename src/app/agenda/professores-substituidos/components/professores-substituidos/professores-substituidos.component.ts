import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";

import { TreinoApiColaboradorService } from "treino-api";
import { GridFilterConfig, GridFilterType, PactoDataGridConfig } from "ui-kit";

import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { RestService } from "@base-core/rest/rest.service";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "pacto-professores-substituidos",
	templateUrl: "./professores-substituidos.component.html",
	styleUrls: ["./professores-substituidos.component.scss"],
})
export class ProfessoresSubstituidosComponent implements OnInit {
	// TABLE LABEL
	@ViewChild("diaTitulo", { static: true }) diaTitulo;
	@ViewChild("horarioTitulo", { static: true }) horarioTitulo;
	@ViewChild("aulaTitulo", { static: true }) aulaTitulo;
	@ViewChild("substituidoTitulo", { static: true }) substituidoTitulo;
	@ViewChild("substitutoTitulo", { static: true }) substitutoTitulo;
	@ViewChild("justificativaTitulo", { static: true }) justificativaTitulo;

	// FILTER LABELS
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;
	@ViewChild("professorLabel", { static: true }) professorLabel;

	// CELULA ITENS
	@ViewChild("celulaDia", { static: true }) celulaDia;

	constructor(
		private rest: RestService,
		private colaboradorService: TreinoApiColaboradorService,
		public sessionService: SessionService,
		private cd: ChangeDetectorRef
	) {}

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	ready = false;

	private professores: Array<any>;

	ngOnInit() {
		this.fetchFilterData().subscribe(() => {
			this.configTable();
			this.configFilters();
			this.ready = true;
			this.cd.detectChanges();
		});
	}

	private fetchFilterData(): Observable<any> {
		const professores$ = this.colaboradorService.obterTodosColaboradores().pipe(
			map((data) => {
				data.content.forEach((professor: any) => {
					professor.value = professor.id;
					professor.label = professor.nome;
				});
				this.professores = data.content;
				return true;
			})
		);
		return professores$;
	}

	private configTable() {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl("professores/substituidos"),
			logUrl: this.rest.buildFullUrl(
				"log/listar-log-exportacao/professoresSubstituidos"
			),
			exportButton: false,
			pagination: true,
			rowClick: false,
			columns: [
				{
					nome: "dataSubstituicao",
					titulo: this.diaTitulo,
					ordenavel: true,
					defaultVisible: true,
					celula: this.celulaDia,
				},
				{
					nome: "horario",
					titulo: this.horarioTitulo,
					ordenavel: false,
					defaultVisible: true,
				},
				{
					nome: "aula",
					titulo: this.aulaTitulo,
					ordenavel: false,
					defaultVisible: true,
				},
				{
					nome: "substituido",
					titulo: this.substituidoTitulo,
					ordenavel: false,
					defaultVisible: true,
				},
				{
					nome: "substituto",
					titulo: this.substitutoTitulo,
					ordenavel: false,
					defaultVisible: true,
				},
				{
					nome: "justificativa",
					titulo: this.justificativaTitulo,
					ordenavel: true,
					defaultVisible: true,
				},
			],
		});
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "dataInicio",
					label: this.dataInicioLabel,
					type: GridFilterType.DATE_POINT,
					initialValue: new Date(
						new Date().setDate(new Date().getDate() - 30)
					).valueOf(),
				},
				{
					name: "dataFim",
					label: this.dataFimLabel,
					type: GridFilterType.DATE_POINT,
					initialValue: new Date().valueOf(),
				},
				{
					name: "colaboradorIds",
					label: this.professorLabel,
					type: GridFilterType.MANY,
					options: this.professores,
				},
			],
		};
	}
}
