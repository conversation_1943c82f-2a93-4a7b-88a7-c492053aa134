<!-- TABLE LABELS -->
<ng-template #tableTitulo i18n="@@relatorio-atividade-professores:titulo">
	Professor<PERSON> substituídos
</ng-template>
<ng-template #diaTitulo i18n="@@relatorio-atividade-professores:nome:coluna">
	Dia
</ng-template>
<ng-template
	#horarioTitulo
	i18n="@@relatorio-atividade-professores:novos:coluna">
	Horario
</ng-template>
<ng-template
	#aulaTitulo
	i18n="@@relatorio-atividade-professores:renovados:coluna">
	Aula
</ng-template>
<ng-template
	#substituidoTitulo
	i18n="@@relatorio-atividade-professores:revisados:coluna">
	Substituído
</ng-template>
<ng-template
	#substitutoTitulo
	i18n="@@relatorio-atividade-professores:acompanhados:coluna">
	Substituto
</ng-template>
<ng-template
	#justificativaTitulo
	i18n="@@relatorio-atividade-professores:revisar:coluna">
	Justificativa
</ng-template>

<!-- FILTER LABELS -->
<ng-template
	#dataInicioLabel
	i18n="@@relatorio-atividade-professores:data-inicio:filtro">
	Data Início
</ng-template>
<ng-template
	#dataFimLabel
	i18n="@@relatorio-atividade-professores:data-fim:filtro">
	Data Fim
</ng-template>
<ng-template
	#professorLabel
	i18n="@@relatorio-ranking-professores:professores:filtro">
	Professores
</ng-template>

<pacto-cat-layout-v2>
	<div class="table-wrapper pacto-shadow">
		<pacto-relatorio
			#relatorio
			*ngIf="ready"
			[filterConfig]="filterConfig"
			[sessionService]="sessionService"
			[tableTitle]="tableTitulo"
			[table]="table"
			telaId="professoresSubstituidos"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<ng-template #celulaDia let-item="item">
	<span>{{ item.data | date : "shortDate" }}</span>
</ng-template>
