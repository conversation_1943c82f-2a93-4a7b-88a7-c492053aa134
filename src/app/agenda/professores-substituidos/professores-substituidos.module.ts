import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { RouterModule, Routes } from "@angular/router";
import { ProfessoresSubstituidosComponent } from "./components/professores-substituidos/professores-substituidos.component";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";

const funcionalidades = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO,
	true
);

const routes: Routes = [
	{
		path: "",
		component: ProfessoresSubstituidosComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
];

@NgModule({
	declarations: [ProfessoresSubstituidosComponent],
	imports: [RouterModule.forChild(routes), CommonModule, BaseSharedModule],
})
export class ProfessoresSubstituidosModule {}
