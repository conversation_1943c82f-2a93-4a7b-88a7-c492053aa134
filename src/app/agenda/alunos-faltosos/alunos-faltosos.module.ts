import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { AlunosFaltososComponent } from "./components/alunos-faltosos/alunos-faltosos.component";
import { ConfigurarDiasBloqueioComponent } from "./components/configurar-dias-bloqueio/configurar-dias-bloqueio.component";
import { RouterModule, Routes } from "@angular/router";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { PerfilAcessoGuard } from "@base-core/guards/perfil-acesso.guard";
import {
	PerfilAcessoFuncionalidade,
	PerfilAcessoFuncionalidadeNome,
} from "treino-api";

const funcionalidades = new PerfilAcessoFuncionalidade(
	PerfilAcessoFuncionalidadeNome.RELATORIOS_TREINO,
	true
);

const routes: Routes = [
	{
		path: "",
		component: AlunosFaltososComponent,
		canActivate: [PerfilAcessoGuard],
		data: {
			funcionalidade: funcionalidades,
		},
	},
];

@NgModule({
	declarations: [AlunosFaltososComponent, ConfigurarDiasBloqueioComponent],
	imports: [RouterModule.forChild(routes), CommonModule, BaseSharedModule],
	entryComponents: [ConfigurarDiasBloqueioComponent],
})
export class AlunosFaltososModule {}
