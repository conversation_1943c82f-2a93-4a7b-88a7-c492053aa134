import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";

import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";
import { HttpClient } from "@angular/common/http";
import { TreinoApiAgendamentoService } from "treino-api";

@Component({
	selector: "pacto-niveis-edit-modal",
	templateUrl: "./configurar-dias-bloqueio.component.html",
	styleUrls: ["./configurar-dias-bloqueio.component.scss"],
})
export class ConfigurarDiasBloqueioComponent implements OnInit {
	@ViewChild("camposObrigatorios", { static: true }) camposObrigatorios;
	@ViewChild("campoNumerico", { static: true }) campoNumerico;
	@ViewChild("inputOrdem", { static: true }) inputOrdem;
	@ViewChild("inativarModalMsg", { static: true }) inativarModalMsg;
	statusFormulario = false;
	verificado = false;
	timerMask = [/[0-9]/, /[0-9]/, /[0-9]/];
	id;
	formGroup: FormGroup = new FormGroup({
		qtdFaltasBloqueioAluno: new FormControl("", [Validators.required]),
		qtdTempoBloqueioAluno: new FormControl("", [Validators.required]),
	});

	constructor(
		private snotifyService: SnotifyService,
		private rest: RestService,
		private openModal: NgbActiveModal,
		private http: HttpClient,
		private agendamentoService: TreinoApiAgendamentoService
	) {}

	ngOnInit() {
		this.buscaConfiguracoes();
	}

	dismiss() {
		this.verificado = false;
		this.openModal.dismiss();
	}

	close() {
		if (
			this.validForm() &&
			this.validNumber(this.formGroup.get("qtdFaltasBloqueioAluno").value) &&
			this.validNumber(this.formGroup.get("qtdTempoBloqueioAluno").value)
		) {
			this.verificado = false;
			this.cadastrarConfiguracao();
			this.openModal.close(this.formGroup.getRawValue());
		}
	}

	buscaConfiguracoes() {
		const endpointUrl = this.rest.buildFullUrlTreino(
			"agenda/buscar-configuracao-dias-bloqueio"
		);
		this.http.get(endpointUrl).subscribe((resultado: any) => {
			this.formGroup
				.get("qtdFaltasBloqueioAluno")
				.setValue(resultado.content.qtd_faltas_bloqueio_aluno);
			this.formGroup
				.get("qtdTempoBloqueioAluno")
				.setValue(resultado.content.qtd_tempo_bloqueio_aluno);
		});
	}

	cadastrarConfiguracao() {
		const config = {
			qtd_faltas_bloqueio_aluno: this.formGroup.get("qtdFaltasBloqueioAluno")
				.value,
			qtd_tempo_bloqueio_aluno: this.formGroup.get("qtdTempoBloqueioAluno")
				.value,
		};
		this.agendamentoService
			.cadastrarConfiguracao(config)
			.subscribe((retorno) => {
				let modalMsg = this.inativarModalMsg.nativeElement.innerHTML;
				this.snotifyService.success(modalMsg);
			});
	}

	get urlLog() {
		return this.rest.buildFullUrl(`log/niveis/${this.id}`);
	}

	private validForm() {
		if (!this.formGroup.valid) {
			const camposObrigatorios =
				this.camposObrigatorios.nativeElement.innerHTML;
			this.snotifyService.error(camposObrigatorios);
			return false;
		} else {
			return true;
		}
	}

	private validNumber(numero) {
		if (this.formGroup.get("qtdFaltasBloqueioAluno").value) {
			if (
				!isNaN(parseFloat(numero)) &&
				isFinite(numero) &&
				this.formGroup.get("qtdFaltasBloqueioAluno")
			) {
				return true;
			} else {
				const campoNumerico = this.campoNumerico.nativeElement.innerHTML;
				this.inputOrdem.focus();
				this.snotifyService.error(campoNumerico);
				return false;
			}
		} else {
			this.formGroup.removeControl("qtdFaltasBloqueioAluno");
			return true;
		}
	}
}
