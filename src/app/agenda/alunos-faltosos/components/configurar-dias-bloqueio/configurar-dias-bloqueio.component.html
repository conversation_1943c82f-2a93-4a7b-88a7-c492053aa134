<div>
	<div class="modal-header">
		<h4 class="modal-title">
			<span i18n="@@crud-niveis:modal:create:title" style="font-weight: bold">
				Configurar dias de bloqueio
			</span>
		</h4>
		<button
			type="button"
			class="close modal-item"
			aria-label="Close"
			(click)="dismiss()">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	<div class="modal-body">
		<div class="row">
			<div class="col-lg-12">
				<span i18n="@@crud-niveis:modal:create:title">
					Defina quantas faltas impedirá o aluno de realizar um novo agendamento
					e o tempo de espera que ele precisa cumprir até ser liberado para
					agendar outra aula.
				</span>
			</div>
		</div>
		<div class="row" style="margin-top: 25px">
			<div class="col-md-6">
				<span style="font-weight: bold">
					Qtd. de faltas para gerar bloqueio
				</span>
				<pacto-input-number
					[id]="'tolerancia-aula-input'"
					mensagem=""
					[placeholder]="'Número de faltas'"
					[control]="
						formGroup.get('qtdFaltasBloqueioAluno')
					"></pacto-input-number>
			</div>

			<div class="col-md-6">
				<span style="font-weight: bold">Tempo de bloqueio</span>
				<pacto-input-number
					[id]="'tolerancia-aula-input'"
					[placeholder]="'Dias'"
					mensagem=""
					[control]="
						formGroup.get('qtdTempoBloqueioAluno')
					"></pacto-input-number>
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<pacto-log *ngIf="id" [url]="urlLog"></pacto-log>
		<button
			type="button"
			class="btn btn-secondary modal-item"
			(click)="dismiss()"
			i18n="@@buttons:cancelar">
			Cancelar
		</button>
		<button
			type="button"
			id="gravarCadastroNivel"
			class="btn btn-primary"
			(click)="close()"
			i18n="@@buttons:salvar">
			Cadastrar bloqueio
		</button>
	</div>
</div>
<span [hidden]="true" #inativarModalMsg i18n="@@crud-alunos:inativar:modal:msg">
	Configuração registrada com sucesso.
</span>
<span
	[hidden]="true"
	#camposObrigatorios
	i18n="@@crud-niveis:mensagem:campos-obrigatorios">
	Campos obrigatorio não preenchido!
</span>
<span
	[hidden]="true"
	#campoNumerico
	i18n="@@crud-niveis:mensagem:campo-numerico">
	Este campo deve ser informado somente números.
</span>
