<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		class="first"
		[breadcrumbConfig]="{
			categoryName: 'RELATORIOS',
			menu: 'Relatório de alunos faltosos'
		}"></pacto-breadcrumbs>

	<div *ngIf="true" class="table-wrapper pacto-shadow">
		<div class="tituloTabela">
			<span>Lista de alunos faltosos</span>
			<i
				class="pct pct-info"
				style="color: #0a64ff; margin-left: 5px"
				[ds3Tooltip]="tooltipProfessor"
				[tooltipPosition]="'right'"
				[tooltipIndicator]="'left'"></i>
			<span [ds3Tooltip]="tooltipProfessor" [tooltipPosition]="'top'"></span>
		</div>
		<pacto-relatorio
			#tableData
			[tableTitle]="titulo"
			[tableDescription]="subtitulo"
			[table]="table"
			[filterConfig]="filterConfig"
			(btnClick)="btnClickHandler($event)"
			telaId="alunosfaltosos"
			[sessionService]="sessionService"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>
<!--title table-->
<ng-template #titulo>
	<span i18n="@@relatorio-aula-excluida:title"></span>
</ng-template>
<ng-template #subtitulo>
	<span i18n="@@relatorio-aula-excluida:description"></span>
</ng-template>
<!--End title table-->

<!--table columns-->
<ng-template #nomeColumnName>
	<span>Nome do aluno</span>
</ng-template>
<ng-template #dataFaltaColumnName>
	<span>Data da falta</span>
</ng-template>
<ng-template #modalidadeColumnName>
	<span>Modalidade</span>
</ng-template>
<ng-template #aulaColumnName>
	<span i18n="@@relatorio-aula-excluida:table:dataExclusao:title">Aula</span>
</ng-template>

<!--Celulas para formatação-->
<ng-template #professorCelula let-item="item">
	{{ item.professor?.nome }}
</ng-template>
<ng-template #dataAulaCelula let-item="item">
	{{ item.dataAulaDia | date : "dd/MM/yyyy" : "UTC" }}
</ng-template>
<ng-template #btnConfigDiasLabel i18n="@@buttons:adicionar">
	Configurar dias de bloqueio
</ng-template>
<!--fim-->

<ng-template #dataInicioLabel i18n="@@execucoes-treino:data-inicio:filtro">
	Data início
</ng-template>
<ng-template #dataFimLabel i18n="@@execucoes-treino:data-fim:filtro">
	Data fim
</ng-template>
<ng-template
	#professorLabel
	i18n="@@@@relatorio-aula-excluida:professor-label:filtro">
	Professor
</ng-template>
<ng-template #tooltipProfessor>
	<div>
		Definição de falta: aluno estava incluído na aula, mas não foi realizado o
		seu checkin de nenhuma maneira (manual pelo professor ou acesso pela
		catraca).
	</div>
</ng-template>
