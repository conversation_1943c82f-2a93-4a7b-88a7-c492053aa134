import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import {
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
} from "ui-kit";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";
import { ModalService } from "@base-core/modal/modal.service";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import { TreinoApiAgendamentoService } from "treino-api";
import { ready } from "jquery";
import { ConfigurarDiasBloqueioComponent } from "../configurar-dias-bloqueio/configurar-dias-bloqueio.component";

@Component({
	selector: "pacto-relatorio-alunos-faltosos",
	templateUrl: "./alunos-faltosos.component.html",
	styleUrls: ["./alunos-faltosos.component.scss"],
})
export class AlunosFaltososComponent implements OnInit {
	@ViewChild("removeModalTitle", { static: true }) removeModalTitle;
	@ViewChild("removeModalBody", { static: true }) removeModalBody;
	@ViewChild("removeSuccess", { static: true }) removeSuccess;
	@ViewChild("removeErro", { static: true }) removeErro;
	@ViewChild("nomeColumnName", { static: true }) nomeColumnName;
	@ViewChild("dataFaltaColumnName", { static: true }) dataFaltaColumnName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("modalidadeColumnName", { static: true }) modalidadeColumnName;
	@ViewChild("aulaColumnName", { static: true }) aulaColumnName;
	@ViewChild("dataDaFaltaLabel", { static: true }) dataDaFaltaLabel;
	@ViewChild("btnConfigDiasLabel", { static: true }) btnConfigDiasLabel;
	@ViewChild("createSuccess", { static: true }) createSuccess;
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;
	endpointShare: any;

	constructor(
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private modal: NgbModal,
		private rest: RestService,
		private modalService: ModalService,
		private snotifyService: SnotifyService,
		private agendamentoservice: TreinoApiAgendamentoService
	) {}

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;

	ngOnInit() {
		this.configTable();
		this.configFilters();
		this.cd.detectChanges();
	}

	private fetchData() {
		this.tableData.reloadData();
	}

	private configTable() {
		this.endpointShare = this.rest.buildFullUrlTreino(
			"agenda/relatorio/alunos-faltosos"
		);
		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlTreino(
				"agenda/relatorio/alunos-faltosos"
			),
			quickSearch: true,
			buttons: {
				conteudo: this.btnConfigDiasLabel,
				nome: "add",
				id: "btn-Config-DiasLabel",
			},
			columns: [
				{
					nome: "nomeAluno",
					titulo: this.nomeColumnName,
					buscaRapida: true,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "dataFalta",
					titulo: this.dataFaltaColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "nomeModalidade",
					titulo: this.modalidadeColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "nomeTurma",
					titulo: this.aulaColumnName,
					buscaRapida: false,
					visible: true,
					ordenavel: true,
				},
			],
		});
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "dataInicio",
					label: this.dataInicioLabel,
					type: GridFilterType.DATE_POINT,
				},
				{
					name: "dataFim",
					label: this.dataFimLabel,
					type: GridFilterType.DATE_POINT,
				},
			],
		};
	}

	btnClickHandler($event) {
		const modalref = this.modal.open(ConfigurarDiasBloqueioComponent, {
			centered: true,
			windowClass: "larguraModal",
		});
		modalref.componentInstance.selectedColor = null;
		modalref.result.then((result) => {});
	}
}
