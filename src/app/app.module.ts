import { AcessoSistemaApiProviderConfigService } from "@adm/api-config-providers/acesso-sistema-api-provider-config.service";
import { FinanceiroMsApiConfigProviderService } from "@adm/api-config-providers/financeiro-ms-api-config-provider.service";
import { RelatorioApiConfigProviderService } from "@adm/api-config-providers/relatorio-api-config-provider.service";
import { ClubeVantagesApiProviderConfigService } from "@adm/clube-vantagens/providers/clube-vantages-api-provider-config.service";
import { AmChartsModule } from "@amcharts/amcharts3-angular";
import {
	CommonModule,
	CurrencyPipe,
	DatePipe,
	DecimalPipe,
	registerLocaleData,
} from "@angular/common";
import { HTTP_INTERCEPTORS, HttpClientModule } from "@angular/common/http";
import localeEn from "@angular/common/locales/en";
import localeEs from "@angular/common/locales/es";
import localePt from "@angular/common/locales/pt";
import {
	LOCALE_ID,
	NO_ERRORS_SCHEMA,
	NgModule,
	ErrorHandler,
} from "@angular/core";
import { FormsModule } from "@angular/forms";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import {
	NavigationEnd,
	Router,
	RouteReuseStrategy,
	RouterModule,
} from "@angular/router";

import { MatNativeDateModule } from "@angular/material";
import { BaseCoreModule } from "@base-core/base-core.module";
import { GlobalErrorHandler } from "@base-core/services/global-error-handler.service";
import { BaseSharedModule } from "@base-shared/base-shared.module";
import { NgbActiveModal, NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { AcessoSistemaApiConfigProviderBase } from "acesso-sistema-api";
import { AdmLegadoApiConfigProviderBase } from "adm-legado-api";
import { ClubeVantagensApiConfigProviderBase } from "clube-vantagens-api";
import {
	FinanceiroMsApiConfigProviderBase,
	FinanceiroMsApiModule,
} from "financeiro-ms-api";
import {
	SnotifyModule,
	SnotifyPosition,
	SnotifyService,
	ToastDefaults,
} from "ng-snotify";
// Other elements
import { SessionService } from "@base-core/client/session.service";
import { AddIdEmpresaHeadersInterceptor } from "@base-core/rest/add-id-empresa-headers.interceptor";
import { EntityConlictInteceptor } from "@base-core/rest/entity-conlict.inteceptor";
import { ExpiredTokenInterceptor } from "@base-core/rest/expired-token.interceptor";
import { LoaderInterceptor } from "@base-core/rest/loader.interceptor";
import { MessageNotTratedInteceptor } from "@base-core/rest/message-not-trated.inteceptor";
import { GoogleCloudErrorInterceptor } from "@base-core/rest/google-cloud-error.interceptor";
import { ShareInterceptor } from "@base-core/rest/share.interceptor";
import { RECAPTCHA_LANGUAGE, RecaptchaModule } from "ng-recaptcha";
import { DragulaModule, DragulaService } from "ng2-dragula";
import { RelatorioApiConfigProviderBase } from "relatorio-api";
import { AppRoutingModule } from "src/app/app.routing.module";
import { NotificacaoApiConfigProviderBaseService } from "notificacao-api";
import { AdmLegadoApiConfigProviderService } from "./api-config-providers/adm-legado-api-config-provider.service";
import { NotificacaoApiConfigProvider } from "./api-config-providers/notificacao-api/notificacao-api-config-provider";
import { AppComponent } from "./app-component/app.component";
import { LoaderComponent } from "./loader/loader.component";
import { MenuV2RootComponent } from "./menu-v2-root/menu-v2-root.component";
import { TokenInterceptor } from "./microservices/client-discovery/token.interceptor";
import { ShareComponent } from "./share/share.component"; // Other elements

// API adapters
import { AdmCoreApiConfigProviderBase, AdmCoreApiModule } from "adm-core-api";
import { AdmMsApiConfigProviderBase, AdmMsApiModule } from "adm-ms-api";
import { CrmApiConfigProviderBase, CrmApiModule } from "crm-api";
import {
	IntegracaoGympassApiConfigProviderBase,
	IntegracaoGympassApiModule,
} from "integracao-gympass-api";
import {
	LoginAppApiConfigProviderBase,
	LoginAppApiModule,
} from "login-app-api";
import {
	MidiaSocialApiConfigProviderBase,
	MidiaSocialApiModule,
} from "midia-social-api";
import {
	MsPactopayApiConfigProviderBase,
	MsPactopayApiModule,
} from "ms-pactopay-api";
import { PactopayApiConfigProviderBase, PactopayApiModule } from "pactopay-api";
import {
	PessoaMsApiConfigProviderBase,
	PessoaMsApiModule,
} from "pessoa-ms-api";
import { ProdutoApiConfigProviderBase, ProdutoApiModule } from "produto-api";
import { TreinoApiConfigProviderBase, TreinoApiModule } from "treino-api";
import {
	ZwPactopayApiConfigProviderBase,
	ZwPactopayApiModule,
} from "zw-pactopay-api";
import {
	ZwServletApiConfigProviderBase,
	ZwServletApiModule,
} from "zw-servlet-api";
import { AdmCoreApiConfigProvider } from "./api-config-providers/adm-core-api-config-provider.service";
import { AdmMsApiConfigProviderService } from "./api-config-providers/adm-ms-api-config-provider.service";
import { CrmApiConfigProviderService } from "./api-config-providers/crm-api-config-provider.service";
import { IntegracaoGympassApiConfigProvider } from "./api-config-providers/integracao-gympass-api-config-provider.service";
import { LoginAppApiConfigProviderService } from "./api-config-providers/login-app-api-config-provider.service";
import { MidiaSocialApiConfigProviderService } from "./api-config-providers/midia-social-api-config-provider.service";
import { MsPactopayApiConfigProvider } from "./api-config-providers/ms-pactopay-api-config-provider.service";
import { PactopayApiConfigProvider } from "./api-config-providers/pactopay-api-config-provider.service";
import { PessoaMsApiConfigProviderService } from "./api-config-providers/pessoa-ms-api-config-provider.service";
import { ProdutoApiConfigProviderService } from "./api-config-providers/produto-api-config-provider.service";
import { TreinoApiConfigProvider } from "./api-config-providers/treino-api-config-provider.service";
import { ZwPactopayApiConfigProvider } from "./api-config-providers/zw-pactopay-api-config-provider.service";
import { ZwServletApiConfigProvider } from "./api-config-providers/zw-servlet-api-config-provider.service";
// import {CURRENT_MODULE, PactoLayoutModule, PactoLayoutSDKWrapper, PlataformModuleConfig} from "pacto-layout";
// import {TreinoSdkWrapperService} from "./services/treino-sdk-wrapper.service";
import {
	CadastroAuxApiConfigProviderBase,
	CadastroAuxApiModule,
} from "cadastro-aux-api";
import {
	MarketingApiConfigProviderBase,
	MarketingApiModule,
} from "marketing-api";
import {
	CURRENT_MODULE,
	PactoLayoutModule,
	PactoLayoutSDKWrapper,
	PlataformModuleConfig,
} from "pacto-layout";
import { PlanoApiConfigProviderBase, PlanoApiModule } from "plano-api";
import { CadastroAuxApiConfigProviderService } from "./api-config-providers/cadastro-aux-api-config-provider.service";
import { MarketingApiProviderConfigService } from "./api-config-providers/marketing-api-provider-config.service";
import { PlanoApiConfigProviderService } from "./api-config-providers/plano-api-config-provider.service";
import { HintModule } from "./hint/hint.module";
import { PrintComponent } from "./print/print.component";
import { IpService } from "./services/ip.service";
import { TreinoSdkWrapperService } from "./services/treino-sdk-wrapper.service";
import { NgxImageCompressService } from "ngx-image-compress-legacy";
import { PactoApiConfigProviderBase, PactoApiModule } from "pacto-api";
import { PactoApiConfigProviderService } from "./api-config-providers/pacto-api-config-provider.service";
import { NgxMaskModule } from "ngx-mask";
import { CustomRouteReuseStrategy } from "./providers/custom-route-reuse-strategy-provider";
import { BiMsApiProviderConfigService } from "./api-config-providers/bi-ms-api/bi-ms-api-provider-config.service";
import { BiMsApiConfigProviderBase } from "bi-ms-api";
import { DialogConfigModule } from "@adm/configuracao/components/dialog/dialog-config.module";

declare var window;

ToastDefaults.toast.timeout = 3000;
ToastDefaults.toast.position = SnotifyPosition.rightTop;

// Configurations
registerLocaleData(localePt, "pt");
registerLocaleData(localeEs, "es");
registerLocaleData(localeEn, "en");

@NgModule({
	imports: [
		CommonModule,
		HintModule,
		MatNativeDateModule,
		BrowserAnimationsModule,
		HttpClientModule,
		RouterModule.forRoot([], { paramsInheritanceStrategy: "always" }),
		AmChartsModule,
		AppRoutingModule,
		FormsModule,
		NgbModule,
		BaseCoreModule,
		BaseSharedModule,
		SnotifyModule,
		DragulaModule,
		TreinoApiModule,
		ZwPactopayApiModule,
		ZwServletApiModule,
		PactopayApiModule,
		AdmCoreApiModule,
		MsPactopayApiModule,
		CrmApiModule,
		RecaptchaModule,
		ProdutoApiModule,
		AdmMsApiModule,
		LoginAppApiModule,
		MidiaSocialApiModule,
		PactoLayoutModule,
		PessoaMsApiModule,
		MarketingApiModule,
		IntegracaoGympassApiModule,
		PlanoApiModule,
		CadastroAuxApiModule,
		PactoApiModule,
		FinanceiroMsApiModule,
		DialogConfigModule,
		NgxMaskModule.forRoot(),
	],
	schemas: [NO_ERRORS_SCHEMA],
	exports: [SnotifyModule, MenuV2RootComponent],
	entryComponents: [ShareComponent],
	declarations: [
		MenuV2RootComponent,
		AppComponent,
		ShareComponent,
		LoaderComponent,
		PrintComponent,
	],
	providers: [
		SnotifyService,
		IpService,
		DatePipe,
		CurrencyPipe,
		DecimalPipe,
		NgxImageCompressService,
		{ provide: "SnotifyToastConfig", useValue: ToastDefaults },
		{ provide: LOCALE_ID, useValue: "pt" },
		{
			provide: HTTP_INTERCEPTORS,
			useClass: AddIdEmpresaHeadersInterceptor,
			multi: true,
		},
		{
			provide: HTTP_INTERCEPTORS,
			useClass: LoaderInterceptor,
			multi: true,
		},
		{ provide: HTTP_INTERCEPTORS, useClass: ShareInterceptor, multi: true },
		{
			provide: HTTP_INTERCEPTORS,
			useClass: ExpiredTokenInterceptor,
			multi: true,
		},
		{ provide: HTTP_INTERCEPTORS, useClass: TokenInterceptor, multi: true },
		{
			provide: HTTP_INTERCEPTORS,
			useClass: EntityConlictInteceptor,
			multi: true,
		},
		{
			provide: HTTP_INTERCEPTORS,
			useClass: MessageNotTratedInteceptor,
			multi: true,
		},
		{
			provide: HTTP_INTERCEPTORS,
			useClass: GoogleCloudErrorInterceptor,
			multi: true,
		},
		DragulaService,
		NgbActiveModal,
		{
			provide: TreinoApiConfigProviderBase,
			useClass: TreinoApiConfigProvider,
		},
		{
			provide: ZwPactopayApiConfigProviderBase,
			useClass: ZwPactopayApiConfigProvider,
		},
		{
			provide: ZwServletApiConfigProviderBase,
			useClass: ZwServletApiConfigProvider,
		},
		{
			provide: PactopayApiConfigProviderBase,
			useClass: PactopayApiConfigProvider,
		},
		{
			provide: RelatorioApiConfigProviderBase,
			useClass: RelatorioApiConfigProviderService,
		},
		{
			provide: FinanceiroMsApiConfigProviderBase,
			useClass: FinanceiroMsApiConfigProviderService,
		},
		{
			provide: AdmCoreApiConfigProviderBase,
			useClass: AdmCoreApiConfigProvider,
		},
		{
			provide: AdmMsApiConfigProviderBase,
			useClass: AdmMsApiConfigProviderService,
		},
		{
			provide: MsPactopayApiConfigProviderBase,
			useClass: MsPactopayApiConfigProvider,
		},
		{
			provide: CrmApiConfigProviderBase,
			useClass: CrmApiConfigProviderService,
		},
		{
			provide: RECAPTCHA_LANGUAGE,
			useFactory: (locale: string) => locale,
			deps: [LOCALE_ID],
		},
		{
			provide: ProdutoApiConfigProviderBase,
			useClass: ProdutoApiConfigProviderService,
		},
		{
			provide: AdmMsApiConfigProviderBase,
			useClass: AdmMsApiConfigProviderService,
		},
		{
			provide: AdmMsApiConfigProviderBase,
			useClass: AdmMsApiConfigProviderService,
		},
		{
			provide: LoginAppApiConfigProviderBase,
			useClass: LoginAppApiConfigProviderService,
		},
		{
			provide: MidiaSocialApiConfigProviderBase,
			useClass: MidiaSocialApiConfigProviderService,
		},
		{
			provide: PessoaMsApiConfigProviderBase,
			useClass: PessoaMsApiConfigProviderService,
		},
		{
			provide: IntegracaoGympassApiConfigProviderBase,
			useClass: IntegracaoGympassApiConfigProvider,
		},
		{
			provide: PlanoApiConfigProviderBase,
			useClass: PlanoApiConfigProviderService,
		},
		{
			provide: ClubeVantagensApiConfigProviderBase,
			useClass: ClubeVantagesApiProviderConfigService,
		},
		{
			provide: AdmLegadoApiConfigProviderBase,
			useClass: AdmLegadoApiConfigProviderService,
		},
		{
			provide: AcessoSistemaApiConfigProviderBase,
			useClass: AcessoSistemaApiProviderConfigService,
		},
		{ provide: PactoLayoutSDKWrapper, useClass: TreinoSdkWrapperService },
		{
			provide: CURRENT_MODULE,
			useValue: PlataformModuleConfig.TREINO,
			multi: true,
		},
		{
			provide: MarketingApiConfigProviderBase,
			useClass: MarketingApiProviderConfigService,
		},
		{
			provide: AdmMsApiConfigProviderBase,
			useClass: AdmMsApiConfigProviderService,
		},
		{
			provide: PlanoApiConfigProviderBase,
			useClass: PlanoApiConfigProviderService,
		},
		{
			provide: CadastroAuxApiConfigProviderBase,
			useClass: CadastroAuxApiConfigProviderService,
		},
		{
			provide: PactoApiConfigProviderBase,
			useClass: PactoApiConfigProviderService,
		},
		{
			provide: BiMsApiConfigProviderBase,
			useClass: BiMsApiProviderConfigService,
		},
		{
			provide: NotificacaoApiConfigProviderBaseService,
			useClass: NotificacaoApiConfigProvider,
		},
		{
			provide: ErrorHandler,
			useClass: GlobalErrorHandler,
		},
	],
	bootstrap: [AppComponent],
})
export class AppModule {
	constructor(private router: Router, private session: SessionService) {}
}
