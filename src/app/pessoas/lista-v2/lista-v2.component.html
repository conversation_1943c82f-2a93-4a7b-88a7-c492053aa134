<pacto-cat-layout-v2>
	<!-- <div *ngIf="!recursoTelaAlunoPadraoEmpresa" class="toast-beta">
		<div class="toast-text">
			<span>
				<i class="pct pct-info icone"></i>
				Você está experimentando a nova lista de pessoas pois escolheu utilizar
				a versão mais recente da tela de cliente. Fique à vontade para alternar
				entre a nova e antiga versão, ajustando suas preferências diretamente na
				tela do cliente.
			</span>
		</div>
		<div class="toast-button">
			<pacto-cat-button
				(click)="openFeedbackForm()"
				label="Enviar Feedback"
				size="LARGE"
				type="OUTLINE_NO_BORDER"></pacto-cat-button>
		</div>
	</div> -->
	<div class="content-title">
		<pacto-breadcrumbs
			[back]="false"
			[breadcrumbConfig]="{
				categoryName: '',
				menu: 'Pessoas',
				menuLink: ['cadastros', 'alunos', 'listagem']
			}"
			class="first"></pacto-breadcrumbs>
		<div></div>
	</div>
	<div class="table-wrapper pacto-shadow">
		<div class="linha-interacao row">
			<span class="title-relatorio">Pessoas cadastradas</span>
			<div class="resume-filters">
				<ng-container *ngIf="appliedFilters && appliedFilters.length > 0">
					<span class="resume-filters-title">Filtros aplicados:</span>
					<ng-container *ngFor="let item of appliedFilters">
						<ds3-status
							color="outlined"
							*ngIf="!!item && !!item.value && !!item.value">
							<span ds3Tooltip="{{ item.label }}">
								{{ item.value }}
							</span>
						</ds3-status>
					</ng-container>
				</ng-container>
				<div
					*ngIf="totalize > 0"
					[ngClass]="[
						'resume-filters-total',
						totalize > 5000 ? 'too-much' : ''
					]">
					<span class="resume-filters-total-label">Total:</span>
					<span class="resume-filters-total-value">{{ totalize }}</span>
					<button
						size="sm"
						color="secondary"
						ds3-icon-button
						*ngIf="totalize > 5000 && false"
						(click)="setBasicFilters()">
						<i
							ds3Tooltip="Mais de 5000 registros na pesquisa, clique para aplicar filtros basicos no relatorio e melhorar a performance"
							class="resume-filters-total-too-much pct pct-alert-triangle"></i>
					</button>
				</div>
			</div>
			<div class="searchBar col-7">
				<pacto-cat-form-input
					[control]="formGroup.get('parametro')"
					icon="pct pct-search"
					iconPosition="before"
					placeholder="Busca por nome, cpf, matricula, e e-mail"></pacto-cat-form-input>
			</div>
			<div class="buttons col-5">
				<div *ngIf="permissaoExportar9_66" class="button-share">
					<pacto-share-button
						[endpoint]="endpointUrlListaPessoas + '&imprimir=true'"
						[filtros]="getFiltersShare()"
						[sessionService]="sessionService"
						telaId="listaPessoas"
						titulo="Lista de Pessoas"></pacto-share-button>
				</div>
				<div class="button-filter">
					<button
						(click)="openFilter()"
						*ngIf="isAplicouFiltro()"
						ds3-flat-button>
						<i class="pct pct-filter"></i>
					</button>
					<button
						(click)="openFilter()"
						*ngIf="!isAplicouFiltro()"
						ds3-outlined-button>
						<i class="pct pct-filter"></i>
					</button>
				</div>

				<div class="button-mais">
					<div
						[autoClose]="'outside'"
						[placement]="'bottom-right'"
						class="dropdown"
						ngbDropdown>
						<div class="dropdown__btn-wrapper" ngbDropdownToggle>
							<pacto-cat-button
								class="dropdownbutton"
								height="38px"
								label="Mais"
								size="LARGE"
								type="PRIMARY"></pacto-cat-button>
							<span class="icon-drop">
								<i class="pct pct-chevron-down"></i>
							</span>
						</div>
						<div class="dropdown__content conteudo-lista-mais" ngbDropdownMenu>
							<button
								(click)="cadastrarNovoColaborador()"
								class="drowpdown-item-list item-lista-mais"
								ngbDropdownItem>
								Cadastrar novo colaborador
							</button>
							<!-- <button ngbDropdownItem class="drowpdown-item-list" (click)="convidarNovoColaborador()">
								Convidar novo colaborador
							</button> -->
							<button
								(click)="cadastrarNovoCliente()"
								class="drowpdown-item-list item-lista-mais"
								ngbDropdownItem>
								Cadastrar novo cliente
							</button>
							<button
								(click)="relatorioDePessoa()"
								class="drowpdown-item-list item-lista-mais"
								ngbDropdownItem>
								Relatório de pessoa
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
		<pacto-relatorio
			#tableDataRef
			(filterChangeEvent)="filterChangeEvent($event)"
			(iconClick)="iconClickFn($event)"
			(pageChangeEvent)="pageEvent($event)"
			(pageSizeChange)="sizeEvent($event)"
			(rowClick)="goToUserPage($event, 'user')"
			(sortEvent)="sortEvent($event)"
			(loadedData)="updateResumes()"
			[customFormGroup]="formGroup"
			[filterContentRef]="filterContentRef"
			[isSideFilter]="true"
			[itensPerPage]="itensPerPage"
			[persistirFiltros]="false"
			[showBtnAdd]="false"
			[showShare]="false"
			[table]="tableData"
			actionTitulo="Ações"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<!-- celulas customizadas -->
<ng-template #nomeCell let-item="item">
	<div class="pessoa">
		<div class="fotoCell">
			<img
				[src]="item.urlFoto || 'assets/images/default-user-icon.png'"
				alt=""
				class="foto-pessoa" />
		</div>
		<div class="nomeCell capitalize">
			{{ item.nome | captalize : true }}
		</div>
	</div>
</ng-template>
<ng-template #tipoCell let-item="item">
	<div class="tipoCell capitalize">
		<span *ngIf="item.tipos">{{ item.tipos[0] }}</span>
		<span
			*ngIf="item.tipos && item.tipos.length > 1"
			[darkTheme]="true"
			[pactoCatTolltip]="extraTipoRef"
			class="extra-entry">
			(+{{ item.tipos.length - 1 }})
		</span>
		<ng-template #extraTipoRef>
			<p *ngFor="let extraTipo of item.tipos" class="extraTipoEntry capitalize">
				{{ extraTipo }}
			</p>
		</ng-template>
	</div>
</ng-template>
<ng-template #eMailCell let-item="item">
	<div class="eMailCell">
		<span *ngIf="item.emails && item.emails.length === 0">-</span>
		<span *ngIf="item.emails">{{ item.emails[0] }}</span>
		<span
			*ngIf="item.emails && item.emails.length > 1"
			[darkTheme]="true"
			[pactoCatTolltip]="extraEmailRef"
			class="extra-entry">
			(+{{ item.emails.length - 1 }})
		</span>
		<ng-template #extraEmailRef>
			<p *ngFor="let extraEmail of item.emails" class="extraEmailEntry">
				{{ extraEmail }}
			</p>
		</ng-template>
	</div>
</ng-template>
<ng-template #empresaCell let-item="item">
	<div class="empresaCell capitalize">
		<span *ngFor="let empresa of item.empresas">
			{{
				(empresa.nomeCurto ? empresa.nomeCurto : empresa.nome)
					| captalize : true
			}}
		</span>
		<!-- <span *ngIf="item.empresas">
			{{ item.empresas[0].nome | captalize : true }}
		</span>
		<span
			*ngIf="item.empresas && item.empresas.length > 1"
			[darkTheme]="true"
			[pactoCatTolltip]="extraEmpresaRef"
			class="extra-entry">
			(+{{ item.empresas.length - 1 }} )
		</span>
		<ng-template #extraEmpresaRef>
			<p
				*ngFor="let extraEmpresa of item.empresas"
				class="extraEmpresaEntry capitalize">
				{{ extraEmpresa.nome | captalize : true }}
			</p>
		</ng-template> -->
	</div>
</ng-template>
<ng-template #situacoesCell let-item="item">
	<div class="pills">
		<span
			*ngIf="item?.situacaoCliente"
			[class]="
				'situacao-aluno alongado primario ' + item?.situacaoCliente | lowercase
			"
			[darkTheme]="true"
			[pactoCatTolltip]="getTextTooltip('aluno', item?.situacaoCliente)"
			class="status-pill">
			{{ getSituacao(item?.situacaoCliente) | captalize : true }}
		</span>
		<span
			*ngIf="
				item?.situacaoClienteContrato &&
				item?.situacaoClienteContrato !== 'VI' &&
				item?.situacaoClienteContrato !== 'TR'
			"
			[class]="
				'situacao-contrato alongado secundario ' + item?.situacaoClienteContrato
					| lowercase
			"
			[darkTheme]="true"
			[pactoCatTolltip]="
				getTextTooltip('contrato', item?.situacaoClienteContrato)
			"
			class="status-pill">
			{{ getSituacao(item?.situacaoClienteContrato) | captalize : true }}
		</span>
		<span
			*ngIf="item?.gympass || item?.totalpass || item?.gogood"
			[class]="
				'situacao-pass alongado terciario ' +
				(item?.gympass ? 'gympass' : item?.totalpass ? 'totalPass' : 'goGood')
			"
			[darkTheme]="true"
			[pactoCatTolltip]="
				getTextTooltip(
					'outro',
					item?.gympass ? 'GY' : item?.totalpass ? 'TP' : 'GD'
				)
			">
			{{ item?.gympass ? "WellHub" : item?.totalpass ? "TotalPass" : "GoGood" }}
		</span>
		<span
			*ngIf="item?.freepass || item?.freepass"
			[class]="'situacao-pass alongado terciario freepass'"
			[darkTheme]="true"
			[pactoCatTolltip]="getTextTooltip('outro', 'PE')">
			Freepass
		</span>
		<span
			*ngIf="item?.dependente"
			[class]="'situacao-pass alongado terciario dependente'"
			[darkTheme]="true"
			[pactoCatTolltip]="getTextTooltip('outro', 'DEP')">
			Dependente
		</span>
		<span *ngIf="!item?.situacaoCliente">-</span>
	</div>
	<div class="pills">
		<span
			*ngIf="item?.situacaoCliente"
			[class]="
				'situacao-aluno abreviado primario ' + item?.situacaoCliente | lowercase
			"
			[darkTheme]="true"
			[pactoCatTolltip]="getTextTooltip('aluno', item?.situacaoCliente)"
			class="status-pill">
			{{ item?.situacaoCliente | uppercase }}
		</span>
		<span
			*ngIf="
				item?.situacaoClienteContrato &&
				item?.situacaoClienteContrato !== 'VI' &&
				item?.situacaoClienteContrato !== 'TR'
			"
			[class]="
				'situacao-contrato abreviado secundario ' +
					item?.situacaoClienteContrato | lowercase
			"
			[darkTheme]="true"
			[pactoCatTolltip]="
				getTextTooltip('contrato', item?.situacaoClienteContrato)
			"
			class="status-pill">
			{{ item?.situacaoClienteContrato | uppercase }}
		</span>
		<span
			*ngIf="item?.gympass || item?.totalpass || item?.gogood"
			[class]="
				'situacao-pass abreviado terciario ' +
				(item?.gympass ? 'gympass' : item?.totalpass ? 'totalPass' : 'goGood')
			"
			[darkTheme]="true"
			[pactoCatTolltip]="
				getTextTooltip(
					'outro',
					item?.gympass ? 'GY' : item?.totalpass ? 'TP' : 'GD'
				)
			">
			{{ item?.gympass ? "WB" : item?.totalpass ? "TP" : "GD" }}
		</span>
		<span
			*ngIf="item?.freepass || item?.freepass"
			[class]="'situacao-pass abreviado terciario freepass'"
			[darkTheme]="true"
			[pactoCatTolltip]="getTextTooltip('outro', 'PE')">
			PE
		</span>
		<span
			*ngIf="item?.dependente"
			[class]="'situacao-pass abreviado terciario dependente'"
			[darkTheme]="true"
			[pactoCatTolltip]="getTextTooltip('outro', 'DEP')">
			DP
		</span>
		<span *ngIf="!item?.situacaoCliente">-</span>
	</div>
</ng-template>

<ng-template #situacoesCellCol let-item="item">
	<div class="pills">
		<span
			*ngIf="item?.situacaoColaborador"
			[darkTheme]="true"
			[pactoCatTolltip]="'Situação do colaborador'">
			{{ getSituacao(item?.situacaoColaborador) | captalize : true }}
		</span>
		<span *ngIf="!item?.situacaoColaborador">-</span>
	</div>
</ng-template>
<!-- traduções -->

<!--tooltip icons-->
<span #tooltipAtivar [hidden]="true" i18n="@@crud-alunos:ativar:tooltip-icon">
	Ativar
</span>
<span
	#tooltipInativar
	[hidden]="true"
	i18n="@@crud-alunos:inativar:tooltip-icon">
	Inativar
</span>
<span #tooltipRemover [hidden]="true" i18n="@@crud-alunos:remover:tooltip-icon">
	Remover
</span>
<ng-template #filterContentRef>
	<div class="side-filter">
		<div class="side-filter-title pct-title-4">
			Filtros
			<button (click)="closeSideFilter()" ds3-icon-button>
				<i class="pct pct-x"></i>
			</button>
		</div>
		<ds3-diviser></ds3-diviser>
		<div cdkScrollable class="side-filter-content" matDialogContent>
			<section class="tipo">
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('tipos')"
					[options]="tipoOptions"
					label="Tipo"></pacto-cat-multi-select-filter>
			</section>
			<section *ngIf="permiteTodasEmpresas()" class="empresa">
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('empresas')"
					[options]="empresaOptions"
					label="Empresa"></pacto-cat-multi-select-filter>
			</section>
			<section class="">
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('situacoes')"
					[options]="getSituacoesOptionsSort()"
					label="Situação"></pacto-cat-multi-select-filter>
			</section>
			<section class="telefone">
				<pacto-cat-form-input
					[control]="formGroup.get('telefone')"
					[textMask]="{ mask: foneMask, guide: false }"
					label="Telefone"></pacto-cat-form-input>
			</section>
			<section class="numero-de-contrato">
				<pacto-cat-form-input
					[control]="formGroup.get('contrato')"
					[textMask]="{ mask: numberMask, guide: false }"
					label="Número de contrato"></pacto-cat-form-input>
			</section>
			<section class="responsavel">
				<pacto-cat-form-input
					[control]="formGroup.get('responsavel')"
					label="Nome do Responsável"></pacto-cat-form-input>
			</section>
			<section class="responsavel">
				<pacto-cat-form-input
					[control]="formGroup.get('cpfresponsavel')"
					[textMask]="{ mask: cpfMask, guide: false }"
					label="CPF do Responsável"></pacto-cat-form-input>
			</section>
			<section class="treino">
				<pacto-cat-select-filter
					[control]="formGroup.get('treino')"
					[options]="treinoOptions"
					label="Treino"></pacto-cat-select-filter>
			</section>
			<section
				*ngIf="permissaoVisualizarAlunosOutrasCarteiras"
				class="professores">
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('professores')"
					[options]="professoresOptions"
					label="Professores"></pacto-cat-multi-select-filter>
			</section>
			<section
				*ngIf="permissaoVisualizarAlunosOutrasCarteiras"
				class="consultores">
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('consultores')"
					[options]="consultoresOptions"
					label="Consultores"></pacto-cat-multi-select-filter>
			</section>
			<section class="emails">
				<pacto-cat-form-input
					[control]="formGroup.get('email')"
					label="Emails"></pacto-cat-form-input>
			</section>
			<section class="cpf">
				<pacto-cat-form-input
					[control]="formGroup.get('cpf')"
					[textMask]="{ mask: cpfMask, guide: false }"
					label="CPF"></pacto-cat-form-input>
			</section>
			<section class="rg">
				<pacto-cat-form-input
					[control]="formGroup.get('rg')"
					[textMask]="{ mask: numberMask, guide: false }"
					label="RG"></pacto-cat-form-input>
			</section>
			<section class="rne">
				<pacto-cat-form-input
					[control]="formGroup.get('rne')"
					[textMask]="{ mask: numberMask, guide: false }"
					label="RNE"></pacto-cat-form-input>
			</section>
			<section class="passaporte">
				<pacto-cat-form-input
					[control]="formGroup.get('passaporte')"
					label="Passaporte"></pacto-cat-form-input>
			</section>
			<section class="matricula">
				<pacto-cat-form-input
					[control]="formGroup.get('matricula')"
					[enableClearInput]="true"
					[textMask]="{ mask: numberMask, guide: false }"
					label="Matrícula"></pacto-cat-form-input>
			</section>
			<section class="categoria">
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('categorias')"
					[options]="categoriaOptions"
					label="Categoria"></pacto-cat-multi-select-filter>
			</section>
			<section class="classificacao">
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('classificacoes')"
					[options]="classificacaoOptions"
					label="Classificação"></pacto-cat-multi-select-filter>
			</section>
			<section class="grupo">
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('grupos')"
					[options]="grupoOptions"
					label="Grupo"></pacto-cat-multi-select-filter>
			</section>
			<section class="codigo-de-acesso-alternativo">
				<pacto-cat-form-input
					[control]="formGroup.get('codigoDeAcessoAlternativo')"
					[textMask]="{ mask: numberMask, guide: false }"
					label="Código de acesso alternativo"></pacto-cat-form-input>
			</section>
			<section class="codigo-de-pessoa">
				<pacto-cat-form-input
					[control]="formGroup.get('pessoa')"
					[textMask]="{ mask: numberMask, guide: false }"
					label="Código de pessoa"></pacto-cat-form-input>
			</section>
			<section class="placa-do-veiculo">
				<pacto-cat-form-input
					[control]="formGroup.get('placa')"
					label="Placa do veículo"></pacto-cat-form-input>
			</section>
			<section class="profissao">
				<pacto-cat-multi-select-filter
					[control]="formGroup.get('profissoes')"
					[options]="profissaoOptions"
					label="Profissão"></pacto-cat-multi-select-filter>
			</section>
			<section class="consultar-por">
				<pacto-cat-select-filter
					[control]="formGroup.get('tipoDeConsulta')"
					[options]="tipoDeConsultaOptions"
					label="Consultar por"></pacto-cat-select-filter>
			</section>
			<!-- <p>Colunas Visiveis</p>
			<section class="colunas-visiveis">
				<pacto-cat-button
						size="
						small"
						[full]="true"
						label="Nome"
						(click)="columnDisplayHandler('nome')"
						[type]="colunasVisiveis.nome ? 'PRIMARY' : 'OUTLINE'"
						class="button-column"
				></pacto-cat-button>
				<pacto-cat-button
						size="small"
						[full]="true"
						label="Tipo"
						(click)="columnDisplayHandler('tipo')"
						[type]="colunasVisiveis.tipo ? 'PRIMARY' : 'OUTLINE'"
						class="button-column"
				></pacto-cat-button>
				<pacto-cat-button
						size="small"
						[full]="true"
						label="E-mail"
						(click)="columnDisplayHandler('emails')"
						[type]="colunasVisiveis.emails ? 'PRIMARY' : 'OUTLINE'"
						class="button-column"
				></pacto-cat-button>
				<pacto-cat-button
						size="small"
						[full]="true"
						label="Empresa"
						(click)="columnDisplayHandler('empresas')"
						[type]="colunasVisiveis.empresas ? 'PRIMARY' : 'OUTLINE'"
						class="button-column"
				></pacto-cat-button>
				<pacto-cat-button
						size="small"
						[full]="true"
						label="Situação"
						(click)="columnDisplayHandler('situacoes')"
						[type]="colunasVisiveis.situacoes ? 'PRIMARY' : 'OUTLINE'"
						class="button-column"
				></pacto-cat-button>
			</section> -->
		</div>
		<ds3-diviser></ds3-diviser>
		<div class="side-filter-actions" matDialogActions>
			<button (click)="limparFiltros()" ds3-outlined-button>
				Limpar filtros
			</button>
			<button (click)="consultar()" ds3-flat-button>Aplicar filtros</button>
		</div>
	</div>
</ng-template>

<pacto-traducoes-xingling #traducao>
	<span
		i18n="@@lista-v2:sem-permissao-exportar"
		xingling="sem-permissao-exportar">
		Você não possui a permissão
	</span>
</pacto-traducoes-xingling>
