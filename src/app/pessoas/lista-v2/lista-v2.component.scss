@import "~src/assets/scss/pacto/plataforma-import.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var.scss";

::ng-deep {
	.linha-interacao {
		width: 100%;
		padding: 16px 0px;
		margin: 0px;
		display: flex;
		justify-content: space-between;
		align-items: baseline;

		.title-relatorio {
			@extend .pct-title5;
			color: var(--color-typography-default-title);
			width: 20%;
			margin: 0px;
			padding-left: 16px;
		}

		.resume-filters {
			// background-color: var(--color-background-plane-3);
			border-radius: 8px;
			padding: 0px 16px 8px;
			display: flex;
			justify-content: flex-end;
			flex-direction: row;
			align-items: center;
			flex-wrap: wrap;
			gap: 8px;
			margin: 8px;
			width: calc(80% - 16px);

			&-title {
				@extend .pct-title5;
				color: var(--color-typography-default-title);
			}

			&-total {
				margin-left: 32px;

				&-label {
					@extend .pct-title5;
					color: var(--color-typography-default-title);
				}

				&-value {
					@extend .pct-overline2;
					padding: 0px 0px 0px 4px;
					color: var(--color-typography-default-title);
				}
				&.too-much {
					i.pct {
						font-size: 22px;
						padding-left: 4px;
					}
					i,
					span {
						padding: 5px 0px;
						vertical-align: bottom;
						color: var(--color-support-red-6);
					}
				}
			}
		}

		// height: 21px;
		// margin-bottom: 0px 0px 16px 0px;
		// display: flex;
		// justify-content: flex-end;
		// align-items: center;
		// column-gap: 4px;

		// span.label {
		// 	@extend .pct-title5;
		// 	color: var(--color-support-blue-3);
		// }

		// span.value {
		// 	@extend .pct-overline2;
		// 	color: var(--color-support-blue-3);
		// }
		// }

		.searchBar {
			border-radius: 18px;

			pacto-cat-form-input {
				.aux-wrapper {
					input {
						border-radius: 8px;
					}
				}
			}
		}

		.buttons {
			display: flex;
			align-items: center;
			justify-content: flex-end;

			.button-share {
				pacto-share-button {
					div {
						#relatorio-exportar {
							border: 1px solid #1e60fa;
							border-radius: 4px;
							background-color: #fff !important;
							color: #1e60fa;

							.icon-drop {
								border-color: #1e60fa;
							}
						}
					}

					.btn {
						height: 40px;
						padding-left: 20px;

						.btn-share-label {
							@extend .pct-btn-default1;
						}

						&.pacto-primary {
							display: flex;
							align-items: center;
							margin-left: 0;

							.btn-share-label {
								margin-right: 20px;
							}
						}
					}

					&::ng-deep {
						.form-control {
							min-height: initial;
						}
					}
				}
			}

			.button-column {
				button {
					margin: 3px 1px 0px 2px;
					border-radius: 100px;
				}
			}

			.button-filter {
				padding: 0 10px;
				font-size: 12px;

				button {
					position: relative;
				}

				.button-filter-badge {
					position: absolute;
					top: 4px;
					right: 4px;
					width: 16px;
					height: 16px;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 10px;
					border-radius: 30px;
					border: 1px solid $actionDefaultAble04;
				}

				// pacto-custom-data-grid-filter {
				// 	&.Primary {
				// 		.dropdown-toggle {
				// 			padding: 7px 45px 7px 14px;
				// 			border: 1px solid #fff;
				// 			border-radius: 4px;
				// 			background-color: #1E60FA !important;
				// 			color: #fff;

				// 			.icon-drop {
				// 				height: 38px !important;
				// 				border-left: 1px solid #fff;
				// 				padding: 0px 15px;
				// 			}
				// 		}
				// 	}

				// 	&.Outline {
				// 		.dropdown-toggle {
				// 			padding: 7px 45px 7px 14px;
				// 			border: 1px solid #1E60FA;
				// 			border-radius: 4px;
				// 			background-color: #fff !important;
				// 			color: #1E60FA;

				// 			.icon-drop {
				// 				height: 38px !important;
				// 				border-left: 1px solid #1E60FA;
				// 				padding: 0px 15px;
				// 			}
				// 		}
				// 	}
				// }
			}

			.button-mais {
				.dropdownbutton > .pacto-primary {
					background-color: #1e60fa;
				}

				.dropdown {
					display: flex;
					align-items: center;
					background-color: #1e60fa;
					border-radius: 4px;
					cursor: pointer;

					.dropdown-toggle {
						display: flex;
						align-items: center;
					}

					button {
						border: none;
						padding-right: 61px;
					}

					.icon-drop {
						position: absolute;
						top: 0;
						bottom: 0;
						right: 0;
						border-left: 1px solid white;
						padding: 0px 12px;
						color: #fff;
						display: flex;
						align-items: center;
					}
				}

				.conteudo-lista-mais {
					padding: 3px 6px;
					box-shadow: 0px 6px 12px 0px #e4e5e6;
				}

				.item-lista-mais {
					padding: 12px 16px;
					@extend .pct-btn-default2;
					cursor: pointer;
					text-align: left;
					color: #1e60fa;

					&:last-of-type {
						border: none !important;
					}
				}
			}
		}
	}

	table.table tr {
		font-size: 12px !important;
	}
}

.extra-entry {
	padding-left: 3px;
	color: $azul;
}

.extraTipoEntry,
.extraEmailEntry,
.extraEmpresaEntry {
	&:first-of-type {
		display: none;
	}
}

.pessoa {
	display: flex;
	align-items: center;

	.foto-pessoa {
		width: 24px;
		border-radius: 50%;
		height: 24px;
	}

	.nomeCell {
		color: $azul;
		padding-left: 8px;
		font-family: Poppins;
	}
}

.pills {
	display: flex;
	align-items: center;
	flex-direction: row;
	justify-content: center;

	.status-pill {
		@extend .type-caption-rounded;
		padding: 5px 16px;
		color: $cinza06;
		background-color: $cinza01;
		border-radius: 50px;
		margin: 0 5px 0 0;

		&.visitante,
		&.vi,
		&.normal,
		&.no,
		&.ativo,
		&.at {
			color: #163e9c;
			background-color: #bccdf5;
		}

		&.renovado,
		&.re,
		&.dependente,
		&.de,
		&.freePass,
		&.fr,
		&.gympass,
		&.gy,
		&.diaria,
		&.di {
			color: #107040;
			background-color: #bcf5d9;
		}

		&.inativo,
		&.in,
		&.cancelado,
		&.ca,
		&.desistente,
		&.de,
		&.vencido,
		&.ve {
			background-color: #f5bcca;
			color: #9c1638;
		}

		&.trancado,
		&.tr,
		&.atestado-m-dico,
		&.ferias,
		&.fe {
			background-color: #e4e5e7;
			color: #a1a4aa;
		}

		&.a-vencer,
		&.av {
			background-color: #f5e7bc;
			color: #9c7b16;
		}
	}
}

.toast-beta {
	background-color: #fafafa;
	border-radius: 5px;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20px;

	.toast-text {
		color: #797d86;

		span {
			.icone {
				font-size: 18px;
				padding: 8px;
			}

			strong {
				//styleName: Button/Default/2;
				font-family: Poppins;
				font-size: 12px;
				font-weight: 600;
				line-height: 12px;
				letter-spacing: 0.25px;
				text-align: left;
			}

			//styleName: Overline/2;
			font-family: Nunito Sans;
			font-size: 12px;
			font-weight: 400;
			line-height: 16px;
			letter-spacing: 0px;
			text-align: left;
		}
	}

	.toast-button {
		padding: 6px;
	}
}

.capitalize {
	text-transform: capitalize;
}

.situacao-aluno,
.situacao-pass,
.situacao-contrato {
	@extend .type-caption-rounded;
	padding: 5px 16px;
	color: $cinza06;
	background-color: $cinza01;
	border-radius: 50px;
	margin: 0 4px 0 0;

	&.alongado {
		width: 136px;
		height: 25px;
		display: block;
	}

	&.abreviado {
		width: 48px;
		height: 25px;
		display: none;
	}

	@media (max-width: 1919px) {
		&.alongado {
			display: none;
		}

		&.abreviado {
			display: block;
		}
	}

	&.vi.primario {
		color: #163e9c;
		background-color: #bccdf5;
	}

	&.at.primario {
		color: #037d03;
		background-color: #b4fdb4;
	}

	&.in.primario {
		color: #7d0303;
		background-color: #fdb4b4;
	}

	&.tr.primario {
		color: #797d86;
		background-color: #e4e5e7;
	}

	&.no.secundario {
		color: #0a4326;
		background-color: #8fefbf;
	}

	&.di.secundario {
		color: #0a4326;
		background-color: #63e9a6;
	}

	&.pe.secundario {
		color: #0a4326;
		background-color: #1dc973;
	}

	&.av.secundario {
		color: #705810;
		background-color: #efd78f;
	}

	&.ve.secundario {
		color: #705810;
		background-color: #e9c763;
	}

	&.tv.secundario {
		color: #705810;
		background-color: #e2b736;
	}

	&.ca.secundario {
		color: #701028;
		background-color: #f5bcca;
	}

	&.de.secundario {
		color: #701028;
		background-color: #ef8fa7;
	}

	&.in.secundario {
		color: #701028;
		background-color: #e96384;
	}

	&.ae.secundario {
		color: #105870;
		background-color: #63c7e9;
	}

	&.cr.secundario {
		color: #105870;
		background-color: #8fd7ef;
	}

	&.gympass.terciario {
		color: #9c5316;
		background-color: #f5d6bc;
	}

	&.totalPass.terciario {
		color: #9c5316;
		background-color: #efba8f;
	}

	&.freepass.terciario {
		color: #0a4326;
		background-color: #1dc973;
	}

	&.dependente.terciario {
		color: #ffffff;
		background-color: #0a4326;
	}
}

.content-title {
	display: grid;
	grid-template-columns: 1fr 1fr;
	align-items: center;

	:first-child {
		justify-self: start;
		/* Alinhamento esquerdo */
	}

	:last-child {
		justify-self: end;
		/* Alinhamento direito */
	}
}

::ng-deep.side-filter-panel .mat-dialog-container {
	padding: 8px 16px !important;
}

.side-filter-content {
	ds3-form-field {
		margin-bottom: 16px;
		display: block;
	}
}

.side-filter {
	display: flex;
	align-content: space-between;
	height: 100%;
	justify-content: flex-start;
	align-items: center;
	flex-direction: column;

	.side-filter-title {
		height: 5%;
		width: 100%;
		position: static;
		display: flex;
		align-items: baseline;
		justify-content: space-between;
		flex-direction: row;
		margin-bottom: 4px;
	}

	.side-filter-content {
		height: 100%;
		width: 100%;
		overflow-y: auto;
		overflow-x: hidden;
		padding: 8px 0px;

		pacto-cat-form-input::ng-deep input,
		pacto-cat-multi-select-filter::ng-deep .current-value {
			border-radius: 8px;
			border-color: var(--color-support-gray-3);
			background-color: var(--color-background-plane-2);
		}

		ds3-form-field {
			margin-bottom: 16px;
		}

		.colunas-visiveis {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			justify-content: center;
			flex-direction: row;
			gap: 6px;

			pacto-cat-button {
				width: calc(50% - 6px);
			}
		}
	}

	.side-filter-actions {
		margin-top: 12px;
		width: 100%;
		position: static;
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		gap: 6px;

		button {
			width: 100%;
		}
	}
}

::ng-deep pacto-cat-form-input {
	margin: 0 !important;
}
