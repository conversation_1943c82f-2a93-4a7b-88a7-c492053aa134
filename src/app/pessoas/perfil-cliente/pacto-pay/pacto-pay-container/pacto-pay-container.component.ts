import {
	animate,
	state,
	style,
	transition,
	trigger,
} from "@angular/animations";
import { DatePipe } from "@angular/common";
import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import { AdmCoreApiClienteService, ClienteDadosPessoais } from "adm-core-api";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import {
	AlunoPactoPay,
	MsPactoPayApiCobrancaService,
	MsPactopayApiConvenioCobrancaService,
} from "ms-pactopay-api";
import { SnotifyService } from "ng-snotify";
import { RelatorioCobrancaComponent } from "src/app/cobranca/components/relatorio-cobranca/relatorio-cobranca.component";
import {
	PactoDataGridConfig,
	PactoModalRef,
	PactoModalSize,
	TraducoesXinglingComponent,
} from "ui-kit";
import { RelatorioComponent } from "../../../../../../projects/ui/src/lib/components/relatorio/relatorio.component";
import { ParametrosDaCobrancaComponent } from "../../../../cobranca/components/acoes-de-detalhamento/parametros-da-cobranca/parametros-da-cobranca.component";
// tslint:disable-next-line:max-line-length
import { ModalEmailReciboCancelamentoCobrancaGetcardComponent } from "../../../../cobranca/components/modal-email-recibo-cancelamento-cobranca-getcard/modal-email-recibo-cancelamento-cobranca-getcard.component";
import { ModalReciboCancelamentoCobrancaGetcardComponent } from "../../../../cobranca/components/modal-recibo-cancelamento-cobranca-getcard/modal-recibo-cancelamento-cobranca-getcard.component";
import { FormaDeCobrancaComponent } from "../autorizacao-de-cobranca/forma-de-cobranca/forma-de-cobranca.component";
import { getTipoCobrancaByCodigo } from "../classes/pactopay";

@Component({
	selector: "pacto-pacto-pay-container",
	templateUrl: "./pacto-pay-container.component.html",
	styleUrls: ["./pacto-pay-container.component.scss"],
	animations: [
		trigger("openClose", [
			state("true", style({ height: "*" })),
			state("false", style({ height: "0px" })),
			transition("false <=> true", animate("0.2s ease")),
		]),
	],
})
export class PactoPayContainerComponent implements OnInit {
	dataCobrancaParcelas = false;
	dataCobrancaCartaoVerificacao = false;

	tiposDeAutorizacaoPessoa;

	dataCobrancaCartao = false;
	dataCobrancaBoleto = false;
	dataCobrancaPix = false;
	dataCobrancaDebitoConta = false;
	dataCancelamentoCobrancaGetcard = false;
	hasDataCobranca = false;

	public allowsCheck = {
		tipoConvenioCobranca: 24,
	};

	public readonly tooltipDisabledMessage =
		"Este recurso é usado apenas para boletos Pjbank.";
	private matricula: string;
	public aluno: AlunoPactoPay;
	public dadosPessoais: ClienteDadosPessoais;
	public parcelasRelatorioConfig: PactoDataGridConfig;
	public cobrancasCartaoRelatorioConfig: PactoDataGridConfig;
	public cobrancasCartaoVerificacaoRelatorioConfig: PactoDataGridConfig;
	public cobrancasPixRelatorioConfig: PactoDataGridConfig;
	public cobrancasBoletoRelatorioConfig: PactoDataGridConfig;
	public cobrancasDebitoRelatorioConfig: PactoDataGridConfig;
	public cancelamentoCobrancaGetcardRelatorioConfig: PactoDataGridConfig;
	@ViewChild("actionBoletos", { static: false })
	public actionBoletos: TemplateRef<any>;
	@ViewChild("parcelasTemplate", { static: true })
	parcelasTemplate: TemplateRef<any>;
	@ViewChild("traducoes", { static: true })
	public traducao: TraducoesXinglingComponent;
	@ViewChild("celulaCodigo", { static: true })
	public celulaCodigo;
	@ViewChild("celulaConvenioCobranca", { static: true })
	public celulaConvenioCobranca;
	@ViewChild("celulaStatusDaTransacao", { static: true })
	public celulaStatusDaTransacao;
	@ViewChild("celulaStatusDoBoleto", { static: true })
	public celulaStatusDoBoleto;
	@ViewChild("celulaStatusDoPix", { static: true })
	public celulaStatusDoPix;
	@ViewChild("celulaStatusDaParcela", { static: true })
	public celulaStatusDaParcela;
	@ViewChild("celulaDetalharParcela", { static: true })
	public celulaDetalharParcela;
	@ViewChild("celulaAcoesCobranca", { static: true })
	public celulaAcoesCobranca;
	@ViewChild("celulaAcoesCobrancaVerifica", { static: true })
	public celulaAcoesCobrancaVerifica;
	@ViewChild("celulaOpcoesCancelamentoCobrancaGetcard", { static: true })
	public celulaOpcoesCancelamentoCobrancaGetcard;
	@ViewChild("tableCartao", { static: false })
	tableCartao: RelatorioComponent;
	@ViewChild("tableCartaoVerificacao", { static: false })
	tableCartaoVerificacao: RelatorioComponent;
	@ViewChild("tableBoleto", { static: false })
	tableBoleto: RelatorioComponent;
	@ViewChild("relatorio", { static: false })
	relatorio: RelatorioCobrancaComponent;
	@ViewChild("celulaDescricao", { static: true })
	public celulaDescricao;

	constructor(
		private readonly route: ActivatedRoute,
		private readonly rest: RestService,
		private readonly msPactopayApiCobranca: MsPactoPayApiCobrancaService,
		private readonly cd: ChangeDetectorRef,
		private readonly msPactopayApiConvenioCobranca: MsPactopayApiConvenioCobrancaService,
		private readonly sessionService: SessionService,
		private readonly admLegadoService: AdmLegadoTelaClienteService,
		private readonly admCoreApiClienteService: AdmCoreApiClienteService,
		private readonly snotifyService: SnotifyService,
		private readonly datePipe: DatePipe,
		private pactoModal: ModalService,
		private modalService: ModalService
	) {}

	ngOnInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.msPactopayApiCobranca
			.obterInformacoesPessoa(this.matricula)
			.subscribe((aluno) => {
				this.aluno = aluno;
				this.admCoreApiClienteService
					.dadosPessoais(this.matricula)
					.subscribe((response) => {
						this.dadosPessoais = response;
						this.obterTiposDeAutorizacaoPessoa();
					});
				this.cd.detectChanges();
			});
		this.initCobrancasCartaoConfig();
		this.initCobrancasCartaoVerificacaoConfig();
		this.initCobrancasDebitoConfig();
		this.initCancelamentoCobrancaGetcardConfig();
		this.initCobrancasPixConfig();
		this.initCobrancasBoletoConfig();
		this.initParcelasConfig();
	}

	// Propriedades para controlar o estado dos botões
	get hasSelectableItems(): boolean {
		if (
			!this.tableBoleto ||
			!this.tableBoleto.rawData ||
			!Array.isArray(this.tableBoleto.rawData)
		) {
			return false;
		}
		return this.tableBoleto.rawData.some(
			(item) => !this.disableCheckForItem(item)
		);
	}

	get hasSelectedItems(): boolean {
		if (
			!this.tableBoleto ||
			!this.tableBoleto.selectedItems ||
			!Array.isArray(this.tableBoleto.selectedItems)
		) {
			return false;
		}
		return this.tableBoleto.selectedItems.length > 0;
	}

	get shouldDisableButtons(): boolean {
		return !this.hasSelectableItems || !this.hasSelectedItems;
	}

	private disableCheckForItem(item: any): boolean {
		const propertyNames = Object.getOwnPropertyNames(this.allowsCheck);
		if (propertyNames.length > 0 && item) {
			const disable = [];
			for (const propertyName of propertyNames) {
				if (item.hasOwnProperty(propertyName)) {
					disable.push(item[propertyName] === this.allowsCheck[propertyName]);
				} else {
					disable.push(false);
				}
			}
			return disable.includes(false);
		}
		return false;
	}

	public reiniciarRelatorio(res) {
		if (
			res.status === "Salvo com sucesso!" ||
			res.status === "Excluído com sucesso!"
		) {
			this.relatorio.reloadData();
			this.cd.detectChanges();
		}
	}

	public openModalNovaFormaDeCobranca() {
		const modal: PactoModalRef = this.modalService.open(
			"Nova forma de cobrança",
			FormaDeCobrancaComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.editar = false;
		modal.componentInstance.pessoa = this.aluno.pessoa;
		modal.componentInstance.empresa = this.dadosPessoais.empresa.codigo;
		modal.componentInstance.sendModificacao.subscribe((res) =>
			this.reiniciarRelatorio(res)
		);
	}

	private initParcelasConfig(): void {
		this.parcelasRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlAdmCore(
				`parcelas/by-matricula/${this.matricula}`
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			actions: [],
			dataAdapterFn: (serveData) => {
				this.dataCobrancaParcelas = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "codigo",
					titulo: "Cód.",
					ordenavel: true,
					visible: true,
				},
				{
					nome: "descricao",
					titulo: "Descrição",
					ordenavel: true,
					visible: true,
				},
				{
					nome: "nrTentativas",
					titulo: "Tentativas",
					ordenavel: true,
					visible: true,
				},
				{
					nome: "dataVencimento",
					titulo: "Vencimento",
					ordenavel: true,
					visible: true,
					valueTransform: (v) => this.datePipe.transform(v, "dd/MM/yyyy"),
				},
				{
					nome: "valor",
					titulo: "Valor",
					ordenavel: true,
					visible: true,
					valueTransform: (v) => this.transformMoneyValue(v),
				},
				{
					nome: "",
					titulo: "Status",
					visible: true,
					ordenavel: true,
					celula: this.celulaStatusDaParcela,
					width: "13%",
				},
				{
					nome: "verMais",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.celulaDetalharParcela,
					// width:'10%'
				},
			],
		});
	}

	private initCobrancasCartaoConfig(): void {
		this.cobrancasCartaoRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPayMs(
				`cobranca/find-by-matricula/${this.matricula}/1`
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			actions: [],
			dataAdapterFn: (serveData) => {
				this.dataCobrancaCartao = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "codigo",
					titulo: "Cod.",
					visible: true,
					ordenavel: true,
					celula: this.celulaCodigo,
					width: "4%",
				},
				{
					nome: "movParcelas",
					titulo: "Descricao",
					visible: true,
					ordenavel: false,
					celula: this.celulaDescricao,
				},
				{
					nome: "titular",
					titulo: "Titular",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "cartao",
					titulo: "Cartão",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "qtd_parcelas",
					titulo: "Qtd. Parcelas",
					visible: true,
					ordenavel: true,
					width: "7%",
				},
				{
					nome: "data_registro",
					titulo: "Data",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.transformMoneyValue(v),
					width: "7%",
				},
				{
					nome: "situacao_descricao",
					titulo: "Status",
					visible: true,
					ordenavel: true,
					celula: this.celulaStatusDaTransacao,
				},
				{
					nome: "verMais",
					titulo: "Ações",
					visible: true,
					ordenavel: false,
					celula: this.celulaAcoesCobranca,
				},
			],
		});
	}

	private initCobrancasCartaoVerificacaoConfig(): void {
		this.cobrancasCartaoVerificacaoRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPayMs(
				`cobranca/find-by-matricula/${this.matricula}/1?verificacao=true`
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			actions: [],
			dataAdapterFn: (serveData) => {
				this.dataCobrancaCartaoVerificacao =
					serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "codigo",
					titulo: "Cod.",
					visible: true,
					ordenavel: true,
					celula: this.celulaCodigo,
					width: "5%",
				},
				{
					nome: "titular",
					titulo: "Titular",
					visible: true,
					ordenavel: true,
					width: "10%",
				},
				{
					nome: "cartao",
					titulo: "Cartão",
					visible: true,
					ordenavel: true,
					width: "6%",
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: true,
					width: "10%",
				},
				{
					nome: "qtd_parcelas",
					titulo: "Qtd. Parcelas",
					visible: true,
					ordenavel: true,
					width: "7%",
				},
				{
					nome: "data_registro",
					titulo: "Data",
					visible: true,
					ordenavel: true,
					width: "10%",
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: true,
					width: "10%",
					valueTransform: (v) => this.transformMoneyValue(v),
				},
				{
					nome: "situacao_descricao",
					titulo: "Status",
					visible: true,
					ordenavel: true,
					celula: this.celulaStatusDaTransacao,
					width: "10%",
				},
				{
					nome: "verMais",
					titulo: "Ações",
					visible: true,
					ordenavel: false,
					celula: this.celulaAcoesCobrancaVerifica,
					width: "10%",
				},
			],
		});
	}

	private initCobrancasBoletoConfig(): void {
		this.cobrancasBoletoRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPayMs(
				`cobranca/find-by-matricula/${this.matricula}/7`
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			valueRowCheck: "codigo",
			tooltipCheckbox: this.tooltipDisabledMessage,
			dataAdapterFn: (serveData) => {
				this.dataCobrancaBoleto = serveData.totalElements > 0 ? true : false;

				// Verificar se é pra exibir coluna dinâmica boleto remessa
				const temBoletoRemessaNaLista =
					serveData.content &&
					serveData.content.some((item) => item.tipo_cobranca === 1);
				const colunaCodRemessa =
					this.cobrancasBoletoRelatorioConfig.columns.find(
						(col) => col.nome === "codRemessa"
					);
				if (colunaCodRemessa) {
					colunaCodRemessa.visible = temBoletoRemessaNaLista;
				}

				// Verificar se é pra exibir coluna dinâmica boleto online
				const temBoletoOnlineNaLista =
					serveData.content &&
					serveData.content.some((item) => item.tipo_cobranca === 6);
				const colunaLinhaDigitavel =
					this.cobrancasBoletoRelatorioConfig.columns.find(
						(col) => col.nome === "linhaDigitavel"
					);
				if (colunaLinhaDigitavel) {
					colunaLinhaDigitavel.visible = temBoletoOnlineNaLista;
				}

				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "codigo",
					titulo: "Cód.",
					visible: true,
					ordenavel: true,
					width: "5%",
				},
				{
					nome: "codRemessa",
					titulo: "Cód. Remessa",
					visible: false,
					ordenavel: false,
					width: "20%",
				},
				{
					nome: "linhaDigitavel",
					titulo: "Linha Digitável",
					visible: false,
					ordenavel: false,
					width: "20%",
				},
				{
					nome: "movParcelas",
					titulo: "Parcelas",
					visible: true,
					ordenavel: true, // Como é um array, não faz sentido ordenar
					celula: this.parcelasTemplate, // Usa a função para exibir uma mini tabela com as parcelas
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.transformMoneyValue(v),
				},
				{
					nome: "data_vencimento",
					titulo: "Vencimento",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "recibo",
					titulo: "Recibo",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "situacao_descricao",
					titulo: "Status",
					visible: true,
					ordenavel: true,
					celula: this.celulaStatusDoBoleto,
				},
			],
			actions: [
				{
					nome: "Imprimir",
					iconClass: "pct pct-printer cor-azulim05",
					tooltipText: "Imprimir",
					showIconFn: (row) => row.podeImprimir,
					actionFn: (row) => this.imprimirBoleto(row),
				},
				{
					nome: "Enviar Email",
					iconClass: "pct pct-send cor-azulim05",
					tooltipText: "Enviar por email",
					showIconFn: (row) => row.podeImprimir,
					actionFn: (row) => this.enviarEmailBoleto(row),
				},
				{
					nome: "Remover parcela(s) da remessa",
					iconClass: "pct pct-x-circle cor-hellboy05",
					tooltipText: "Remover parcela(s) da remessa",
					showIconFn: (row) =>
						row.tipo_cobranca === 1 && row.podeRemoverParcelas,
					actionFn: (row) => this.removerParcelasBoleto(row),
				},
				{
					nome: "Detalhes",
					iconClass: "pct pct-eye",
					tooltipText: "Detalhes do boleto",
					showIconFn: (row) => row.podeVisualizarDetalhe,
					actionFn: (row) => this.detalhesBoleto(row),
				},
				{
					nome: "Cancelar",
					iconClass: "pct pct-x-circle cor-hellboy05",
					tooltipText: "Cancelar",
					showIconFn: (row) => row.podeCancelar,
					actionFn: (row) => this.cancelarBoleto(row),
				},
				{
					nome: "Sincronizar",
					iconClass: "pct pct-refresh-cw",
					tooltipText: "Sincronizar",
					showIconFn: (row) => row.podeSincronizar,
					actionFn: (row) => this.sincronizarBoleto(row),
				},
			],
		});
	}

	private initCobrancasDebitoConfig(): void {
		this.cobrancasDebitoRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPayMs(
				`cobranca/find-by-matricula/${this.matricula}/8`
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			actions: [],
			dataAdapterFn: (serveData) => {
				this.dataCobrancaDebitoConta =
					serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "codigo",
					titulo: "Cod.",
					visible: true,
					ordenavel: true,
					width: "5%",
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: true,
					width: "15%",
				},
				{
					nome: "qtd_parcelas",
					titulo: "Qtd. Parcelas",
					visible: true,
					ordenavel: true,
					width: "15%",
				},
				{
					nome: "data_registro",
					titulo: "Data",
					visible: true,
					ordenavel: true,
					width: "15%",
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: true,
					width: "15%",
					valueTransform: (v) => this.transformMoneyValue(v),
				},
				{
					nome: "situacao_descricao",
					titulo: "Status",
					visible: true,
					ordenavel: true,
					width: "15%",
					celula: this.celulaStatusDaTransacao,
				},
			],
		});
	}

	private initCancelamentoCobrancaGetcardConfig(): void {
		this.cancelamentoCobrancaGetcardRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPayMs(
				`tef-cobranca/cancelamento/find-by-matricula/${this.matricula}/9`
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			dataAdapterFn: (serveData) => {
				this.dataCancelamentoCobrancaGetcard =
					serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "identificador",
					titulo: "ID Transação",
					visible: true,
					ordenavel: true,
					width: "10%",
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: true,
					width: "15%",
					valueTransform: (v) => this.transformMoneyValue(v),
				},
				{
					nome: "parcelas",
					titulo: "Parcelas",
					visible: true,
					ordenavel: true,
					width: "15%",
				},
				{
					nome: "usuario",
					titulo: "Usuário Responsável",
					visible: true,
					ordenavel: true,
					width: "30%",
				},
				{
					nome: "data_registro",
					titulo: "Data",
					visible: true,
					ordenavel: true,
					width: "15%",
				},
			],
			actions: [
				{
					nome: "Visualizar os parâmetros de envio",
					iconClass: "pct pct-corner-up-right",
					tooltipText: "Visualizar os parâmetros de envio",
					showIconFn: (row) => true,
					actionFn: (row) =>
						this.abrirParametrosDeEnvioCancelamentoCobrancaGetcard(row),
				},
				{
					nome: "Visualizar os parâmetros de resposta",
					iconClass: "pct pct-corner-up-left",
					tooltipText: "Visualizar os parâmetros de resposta",
					showIconFn: (row) => true,
					actionFn: (row) =>
						this.abrirParametrosDeRespostaCancelamentoCobrancaGetcard(row),
				},
				{
					nome: "Imprimir",
					iconClass: "pct pct-printer cor-azulim05",
					tooltipText: "Imprimir",
					showIconFn: (row) => true,
					actionFn: (row) =>
						this.imprimirReciboCancelamentoCobrancaGetcard(row),
				},
				{
					nome: "Enviar Email",
					iconClass: "pct pct-send cor-azulim05",
					tooltipText: "Enviar por email",
					showIconFn: (row) => true,
					actionFn: (row) =>
						this.enviarEmailReciboCancelamentoCobrancaGetcard(row),
				},
			],
		});
	}

	private initCobrancasPixConfig(): void {
		this.cobrancasPixRelatorioConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPayMs(
				`cobranca/find-by-matricula/${this.matricula}/4`
			),
			quickSearch: false,
			exportButton: false,
			pagination: true,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			actions: [],
			dataAdapterFn: (serveData) => {
				this.dataCobrancaPix = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "codigo",
					titulo: "Cod.",
					visible: true,
					ordenavel: true,
					width: "5%",
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: true,
					width: "15%",
				},
				{
					nome: "qtd_parcelas",
					titulo: "Qtd. Parcelas",
					visible: true,
					ordenavel: true,
					width: "10%",
				},
				{
					nome: "data_registro",
					titulo: "Data",
					visible: true,
					ordenavel: true,
					width: "15%",
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: true,
					width: "13%",
					valueTransform: (v) => this.transformMoneyValue(v),
				},
				{
					nome: "situacao_descricao",
					titulo: "Status",
					visible: true,
					ordenavel: true,
					width: "15%",
					celula: this.celulaStatusDoPix,
				},
			],
		});
	}

	public transformMoneyValue(value: number = 0): string | number {
		return value.toLocaleString("pt-BR", {
			minimumFractionDigits: 2,
			style: "currency",
			currency: "BRL",
		});
	}

	public obterTipoCobrancaHint(codigo: number): string {
		if (!codigo) {
			return;
		}
		return getTipoCobrancaByCodigo(codigo, this.traducao).hint;
	}

	getCobrancaTooltip(cobranca: any): string {
		if (!cobranca) {
			return null;
		}

		// boleto remessa
		if (cobranca.tipo_cobranca === 1 || cobranca.tipo_cobranca === 6) {
			if (cobranca.tipo_cobranca === 1 && cobranca.situacao === 5) {
				return "Pago";
			}
			if (cobranca.situacao === 13) {
				return "Aguardando Pagamento";
			}
			if (cobranca.situacao === 6) {
				return "Cancelamento Solicitado";
			}
			if (cobranca.tipo_cobranca === 1 && cobranca.situacao === 16) {
				return "Sem Parcela Vinculada. Se o boleto for pago, o saldo irá para a conta corrente do aluno.";
			}
		}

		if (cobranca.tipo_cobranca === 6) {
			// boleto online
			if (cobranca.retorno_descricao) {
				return cobranca.retorno_descricao;
			}
			return null;
		}

		if (cobranca.tipo_cobranca === 3 && cobranca.retorno_descricao) {
			return cobranca.retorno_descricao;
		}

		if (
			cobranca.tipo_cobranca === 2 &&
			cobranca.retorno_descricao &&
			cobranca.retorno_descricao.length > 0
		) {
			return cobranca.retorno_descricao;
		}

		if (
			[1, 3].includes(cobranca.situacao) &&
			cobranca.retorno_codigo &&
			cobranca.retorno_codigo.length > 10
		) {
			return cobranca.retorno_codigo;
		}

		return null;
	}

	getCobrancaCodigo(cobranca: any): string {
		if (!cobranca) {
			return null;
		}

		if (cobranca.tipo_cobranca === 6) {
			// boleto
			if (cobranca.tipo_cobranca === 6 && cobranca.situacao === 1) {
				return "Erro";
			} else {
				return cobranca.situacao_descricao;
			}
		}

		if (
			cobranca.situacao === 1 &&
			cobranca.retorno_codigo &&
			cobranca.retorno_codigo.length > 10
		) {
			return cobranca.retorno_codigo;
		}

		if (
			cobranca.situacao === 3 &&
			cobranca.retorno_codigo &&
			cobranca.retorno_codigo.length > 10
		) {
			return `Falha`;
		}

		return cobranca.retorno_codigo;
	}

	private obterTiposDeAutorizacaoPessoa() {
		const empresa =
			this.dadosPessoais && this.dadosPessoais.empresa
				? this.dadosPessoais.empresa.codigo
				: this.sessionService.empresaId;
		this.msPactopayApiConvenioCobranca
			.obterTiposDeAutorizacao(
				Number(empresa),
				Number(this.aluno.pessoa),
				Number(1)
			)
			.subscribe((res) => {
				this.tiposDeAutorizacaoPessoa = res.content;
				this.cd.detectChanges();
			});
	}

	public sincronizarTodosBoletos() {}

	acaoEvent(res) {
		if (res === "atualizarListaCartao") {
			this.tableCartao.reloadData();
			this.tableCartaoVerificacao.reloadData();
			this.cd.detectChanges();
		}
	}

	detalhesBoleto(row): void {
		this.admLegadoService
			.detalhesBoleto(
				this.sessionService.chave,
				this.sessionService.codUsuarioZW,
				row.row.codigo
			)
			.subscribe(
				(res) => {
					if (res.content) {
						window.open(res.content, "_blank");
					} else {
						this.snotifyService.error(res.meta.message);
					}
				},
				({ error }) => {
					this.snotifyService.error(error.meta.message);
				}
			);
	}

	imprimirBoleto(row): void {
		const convenioBBOnline = row.row.tipoConvenioCobranca === 47;
		const tipo_boleto_online = row.row.tipo_cobranca === 6;
		const boletoOnlineNaoRegistrado =
			row.row.tipoConvenioCobranca === 45 &&
			(row.row.linkPdf === null ||
				row.row.linkPdf === undefined ||
				row.row.linkPdf === "");
		if (tipo_boleto_online && !convenioBBOnline && !boletoOnlineNaoRegistrado) {
			window.open(row.row.linkPdf, "_blank");
			return;
		}
		if (convenioBBOnline) {
			this.admLegadoService
				.imprimirBoletoBBOnline(this.sessionService.chave, row.row.codigo)
				.subscribe(
					(res) => {
						if (res.content) {
							window.open(res.content, "_blank");
						} else {
							this.snotifyService.error(res.meta.message);
						}
					},
					({ error }) => {
						this.snotifyService.error(error.meta.message);
					}
				);
		} else if (boletoOnlineNaoRegistrado) {
			this.admLegadoService
				.imprimirBoletoOnlineNaoRegistrado(
					this.sessionService.chave,
					row.row.codigo,
					this.sessionService.codUsuarioZW
				)
				.subscribe(
					(res) => {
						if (res.content) {
							window.open(res.content, "_blank");
						} else {
							this.snotifyService.error(res.meta.message);
						}
					},
					({ error }) => {
						this.snotifyService.error(error.meta.message);
					}
				);
		} else {
			this.admLegadoService
				.imprimirBoleto(this.sessionService.chave, row.row.codigo, 0)
				.subscribe(
					(res) => {
						if (res.content) {
							window.open(res.content, "_blank");
						} else {
							this.snotifyService.error(res.meta.message);
						}
					},
					({ error }) => {
						this.snotifyService.error(error.meta.message);
					}
				);
		}
	}

	enviarEmailBoleto(row): void {
		const convenioBBOnline = row.row.tipoConvenioCobranca === 47;
		let boleto = 0;
		let remessa_item = 0;
		if (row.row.tipo_cobranca === 6 && !convenioBBOnline) {
			boleto = row.row.codigo;
		} else if (row.row.tipo_cobranca === 1) {
			remessa_item = row.row.codigo;
		}
		if (convenioBBOnline) {
			this.admLegadoService
				.enviarEmailBoletoBB(
					this.sessionService.chave,
					this.sessionService.empresaId,
					this.sessionService.codUsuarioZW,
					row.row.codigo
				)
				.subscribe(
					(res) => {
						if (res.content) {
							this.snotifyService.success("E-mail enviado com sucesso.");
						} else {
							this.snotifyService.error(res.meta.message);
						}
					},
					({ error }) => {
						this.snotifyService.error(error.meta.message);
					}
				);
		} else {
			this.admLegadoService
				.enviarEmailBoleto(
					this.sessionService.chave,
					this.sessionService.codUsuarioZW,
					remessa_item,
					boleto
				)
				.subscribe(
					(res) => {
						if (res.content) {
							this.snotifyService.success("E-mail enviado com sucesso.");
						} else {
							this.snotifyService.error(res.meta.message);
						}
					},
					({ error }) => {
						this.snotifyService.error(error.meta.message);
					}
				);
		}
	}

	removerParcelasBoleto(row): void {
		const modal = this.pactoModal.confirm(
			"Remover Parcela - Remessa Boleto",
			"Caso a remessa desta parcela já tenha sido fechada, ao remover essa parcela do registro de remessa, " +
				"ela poderá ser cobrada também automaticamente através de outro convênio (caso seja incluído " +
				"por exemplo uma autorização de cartão de crédito)" +
				" para este aluno, mesmo que ele ainda tenha o convênio de boleto. " +
				"Caso ocorra o pagamento do boleto e o processo de cobrança automático também cobre automaticamente no cartão de crédito, a " +
				"diferença será incluída como crédito na conta corrente desse aluno.",
			"Confirmar"
		);

		modal.result.then(
			() => {
				this.admLegadoService
					.removerParcelaRemessaItem(
						this.sessionService.chave,
						this.sessionService.codUsuarioZW,
						row.row.codigo
					)
					.subscribe((response) => {
						if (response.content) {
							this.tableBoleto.reloadData();
							this.snotifyService.success("Parcela(s) removidas!");
						} else {
							this.snotifyService.error(response.meta.message);
						}
					});
			},
			() => {}
		);
	}

	sincronizarBoleto(row): void {
		const modal = this.pactoModal.confirm(
			"Sincronizar Boleto",
			"Verificar se houveram alterações no boleto.",
			"Confirmar"
		);
		modal.result.then(
			() => {
				this.admLegadoService
					.sincronizarBoleto(
						this.sessionService.chave,
						this.sessionService.codUsuarioZW,
						row.row.codigo
					)
					.subscribe(
						(res) => {
							if (res.content) {
								this.tableBoleto.reloadData();
								this.snotifyService.success(res.content);
							} else {
								this.snotifyService.error(res.meta.message);
							}
						},
						({ error }) => {
							this.snotifyService.error(error.meta.message);
						}
					);
			},
			() => {}
		);
	}

	cancelarBoleto(row): void {
		const modal = this.pactoModal.confirm(
			"Cancelar Boleto",
			"Ao cancelar este boleto, mesmo que o aluno já o tenha impresso, " +
				"não será mais possível realizar o pagamento do mesmo. Esta operação não poderá ser desfeita!",
			"Confirmar"
		);
		modal.result.then(
			() => {
				this.admLegadoService
					.cancelarBoleto(
						this.sessionService.chave,
						this.sessionService.codUsuarioZW,
						row.row.codigo
					)
					.subscribe(
						(res) => {
							if (res.content) {
								this.tableBoleto.reloadData();
								this.snotifyService.success("Boleto cancelado!");
							} else {
								this.snotifyService.error(res.meta.message);
							}
						},
						({ error }) => {
							this.snotifyService.error(error.meta.message);
						}
					);
			},
			() => {}
		);
	}

	setDataCobranca(event: any) {
		this.tableCartaoVerificacao.reloadData();
		this.hasDataCobranca = event;
		this.cd.detectChanges();
	}

	get hasGeneralEmpty() {
		return (
			!this.hasDataCobranca &&
			!this.dataCobrancaCartao &&
			!this.dataCobrancaCartaoVerificacao &&
			!this.dataCobrancaDebitoConta &&
			!this.dataCancelamentoCobrancaGetcard &&
			!this.dataCobrancaPix &&
			!this.dataCobrancaBoleto &&
			!this.dataCobrancaParcelas
		);
	}

	rowCheckHandlerBoleto(itemCheck: {
		row?: any;
		checked?: boolean;
		selectedAll: boolean;
		clearAll: boolean;
	}) {
		// Força a detecção de mudanças para atualizar o estado dos botões
		this.cd.detectChanges();
	}

	imprimirBoletosSelecionados() {
		if (!this.hasSelectableItems) {
			this.snotifyService.error(
				"Não há boletos disponíveis para seleção (apenas boletos do tipo convênio 24 podem ser selecionados)"
			);
			return;
		}
		if (
			!this.tableBoleto ||
			!this.tableBoleto.selectedItems ||
			this.tableBoleto.selectedItems.length === 0
		) {
			this.snotifyService.error("Nenhum boleto selecionado");
			return;
		}
		this.admLegadoService
			.imprimirBoletos(
				this.sessionService.chave,
				this.tableBoleto.selectedItems
			)
			.subscribe(
				(res) => {
					if (res.content) {
						window.open(res.content, "_blank");
					} else {
						this.snotifyService.error(res.meta.message);
					}
				},
				({ error }) => {
					this.snotifyService.error(error.meta.message);
				}
			);
	}

	cancelarBoletosSelecionados(): void {
		if (!this.hasSelectableItems) {
			this.snotifyService.error(
				"Não há boletos disponíveis para seleção (apenas boletos do tipo convênio 24 podem ser selecionados)"
			);
			return;
		}
		if (
			!this.tableBoleto ||
			!this.tableBoleto.selectedItems ||
			this.tableBoleto.selectedItems.length === 0
		) {
			this.snotifyService.error("Nenhum boleto selecionado");
			return;
		}

		const modal = this.pactoModal.confirm(
			"Cancelar Boletos",
			"Ao cancelar os boletos, mesmo que o aluno já o tenha impresso, " +
				"não será mais possível realizar o pagamento do mesmo. Esta operação não poderá ser desfeita!",
			"Confirmar"
		);
		modal.result.then(
			() => {
				this.admLegadoService
					.cancelarBoletos(
						this.sessionService.chave,
						this.sessionService.codUsuarioZW,
						this.tableBoleto.selectedItems
					)
					.subscribe(
						(res) => {
							if (res.content) {
								this.tableBoleto.reloadData();
								this.snotifyService.success(res.content);
							} else {
								this.snotifyService.error(res.meta.message);
							}
						},
						({ error }) => {
							this.snotifyService.error(error.meta.message);
						}
					);
			},
			() => {}
		);
	}

	sincronizarBoletosSelecionados(): void {
		if (!this.hasSelectableItems) {
			this.snotifyService.error(
				"Não há boletos disponíveis para sincronização (apenas boletos do tipo convênio 24 podem ser sincronizados)"
			);
			return;
		}
		const modal = this.pactoModal.confirm(
			"Sincronizar Boletos",
			"Verificar se houve alterações nos boletos na página atual.",
			"Confirmar"
		);
		modal.result.then(
			() => {
				this.admLegadoService
					.sincronizarBoletos(
						this.sessionService.chave,
						this.sessionService.codUsuarioZW,
						this.tableBoleto.rawData
					)
					.subscribe(
						(res) => {
							if (res.content) {
								this.tableBoleto.reloadData();
								this.snotifyService.success(res.content);
							} else {
								this.snotifyService.error(res.meta.message);
							}
						},
						({ error }) => {
							this.snotifyService.error(error.meta.message);
						}
					);
			},
			() => {}
		);
	}

	enviarEmailBoletosSelecionados() {
		if (!this.hasSelectableItems) {
			this.snotifyService.error(
				"Não há boletos disponíveis para seleção (apenas boletos do tipo convênio 24 podem ser selecionados)"
			);
			return;
		}
		if (
			!this.tableBoleto ||
			!this.tableBoleto.selectedItems ||
			this.tableBoleto.selectedItems.length === 0
		) {
			this.snotifyService.error("Nenhum boleto selecionado");
			return;
		}
		this.admLegadoService
			.enviarEmailBoletos(
				this.sessionService.chave,
				this.sessionService.codUsuarioZW,
				this.tableBoleto.selectedItems
			)
			.subscribe(
				(res) => {
					if (res.content) {
						this.snotifyService.success(res.content);
					} else {
						this.snotifyService.error(res.meta.message);
					}
				},
				({ error }) => {
					this.snotifyService.error(error.meta.message);
				}
			);
	}

	abrirParametrosDeEnvioCancelamentoCobrancaGetcard(row): void {
		const str = row.row.paramsEnvio;
		const obj = JSON.parse(str);

		const modal = this.modalService.open(
			"Detalhamento - parâmetros de envio",
			ParametrosDaCobrancaComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.codigo = row.row.identificador;
		modal.componentInstance.tipoDeParametros = "envio";

		const paramsConvertido: Array<{ atribute: string; value: string }> = [];

		// Percorrendo o objeto "request" e adicionando ao array params
		for (const key in obj.request) {
			if (obj.request.hasOwnProperty(key)) {
				paramsConvertido.push({
					atribute: key,
					value: String(obj.request[key]), // Garantindo que o valor seja sempre uma string
				});
			}
		}

		modal.componentInstance.params = paramsConvertido;
	}

	abrirParametrosDeRespostaCancelamentoCobrancaGetcard(row): void {
		const str = row.row.paramsRespostaCancelamento;
		const obj = JSON.parse(str);

		const modal = this.modalService.open(
			"Detalhamento - parâmetros de resposta",
			ParametrosDaCobrancaComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.codigo = row.row.identificador;
		modal.componentInstance.tipoDeParametros = "resposta";

		const paramsConvertido: Array<{ atribute: string; value: string }> = [];

		// Percorrendo o objeto "request" e adicionando ao array params
		for (const key in obj.response) {
			if (obj.response.hasOwnProperty(key)) {
				paramsConvertido.push({
					atribute: key,
					value: String(obj.response[key]), // Garantindo que o valor seja sempre uma string
				});
			}
		}

		modal.componentInstance.params = paramsConvertido;
	}

	imprimirReciboCancelamentoCobrancaGetcard(row): void {
		const str = row.row.paramsRespostaCancelamento;
		const obj = JSON.parse(str);

		const modal = this.modalService.open(
			"Recibo cancelamento cobrança Getcard",
			ModalReciboCancelamentoCobrancaGetcardComponent,
			PactoModalSize.MEDIUM
		);

		modal.componentInstance.dadoApresentarModal = obj.response.CupomCliente;
	}

	enviarEmailReciboCancelamentoCobrancaGetcard(row): void {
		const str = row.row.paramsRespostaCancelamento;
		const obj = JSON.parse(str);

		const modal = this.modalService.open(
			"Enviar e-mail do recibo de cancelamento cobrança Getcard",
			ModalEmailReciboCancelamentoCobrancaGetcardComponent,
			PactoModalSize.MEDIUM
		);
		modal.componentInstance.msgRecibo = obj.response.CupomCliente;
		modal.componentInstance.codigoRecibo = obj.response.ID;
		modal.componentInstance.email = this.dadosPessoais.emails[0];
	}
}
