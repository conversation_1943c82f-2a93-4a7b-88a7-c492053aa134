<div *ngIf="showMensagemAviso" class="aviso-medico">
	<i class="pct pct-alert-triangle"></i>
	<span>{{ clienteMensagemAviso }}</span>
</div>

<ng-container *ngIf="ready">
	<pacto-cat-card-plain>
		<div class="div-professor">
			<div class="col-4" style="padding: 0">
				<div
					class="bit-title"
					i18n="@@perfil-aluno-dados-pessoal:info:professor">
					Professor (TreinoWeb) vinculado ao aluno
				</div>
				<pacto-cat-select-filter
					(click)="alterarProfessor()"
					[control]="professorFC"
					[endpointUrl]="getUrlProfessorAluno"
					[id]="'professor-select'"
					[labelKey]="'nome'"
					[paramBuilder]="professorSelectBuilder"
					[resposeParser]="responseParser"></pacto-cat-select-filter>
			</div>
		</div>
		<div *ngIf="!temProgramas" class="d-flex flex-column align-items-center">
			<img class="icon-empty" src="assets/images/empty-state-dumbell.svg" />
			<div class="text-empty mt-2 body-text-empty mt-3 mb-3">
				O aluno ainda não possui nenhum registro de treino!
			</div>
			<pacto-cat-button
				(click)="criarProgramaHandler('Criar programa', 'SEM_TREINO')"
				*ngIf="
					permissaoProgramaTreino?.incluir ||
					permissaoAtribuirProgramaTreinoPreDefinido
				"
				[id]="'renovar'"
				[v2]="true"
				i18n-label="@@perfil-aluno-treinamento:programas-aluno:renovar:label"
				label="Criar programa de treino"></pacto-cat-button>
		</div>
		<div *ngIf="!temProgramaAtual" class="card-grid row3">
			<div class="card-treino">
				<div class="d-flex justify-content-center">
					<div>
						<div class="margin-bottom-20">
							O aluno não possui um programa vigente
						</div>
						<div class="d-flex justify-content-center">
							<pacto-cat-button
								(click)="criarProgramaHandler('Criar programa')"
								*ngIf="permissaoProgramaTreino?.incluir"
								[v2]="true"
								i18n-label="
									@@perfil-aluno-treinamento:programas-aluno:renovar:label"
								id="criar-programa"
								label="CRIAR PROGRAMA"></pacto-cat-button>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div *ngIf="temProgramaAtual && temProgramas" class="card-grid row1">
			<div class="d-flex gap-30 alingn-items-baseline">
				<div class="d-flex flex-column">
					<h3 class="titulo">Programa atual</h3>
					<a
						(click)="abrirModalPrograma(atual)"
						[title]="atual?.nome | captalize : true"
						class="link-grande"
						id="tr-btn-modal-programa">
						{{ atual?.nome | captalize : true }}
					</a>
					<div class="card-info-text">
						{{ atual?.fichas }}
						<span
							*ngIf="atual?.fichas === 1"
							i18n="@@treino-container:programa-atual:ficha">
							ficha
						</span>
						<span
							*ngIf="atual?.fichas !== 1"
							i18n="@@treino-container:programa-atual:fichas">
							fichas
						</span>

						<span i18n="@@treino-container:programa-atual:no-programa">
							&nbsp;no programa
						</span>
					</div>
					<div class="card-action-button">
						<pacto-cat-button
							(click)="criarProgramaHandler('Renovar programa')"
							*ngIf="permissaoProgramaTreino?.incluir"
							i18n-label="
								@@perfil-aluno-treinamento:programas-aluno:renovar:label"
							id="renovar"
							label="RENOVAR PROGRAMA"
							size="MEDIUM"
							width="123.31px"></pacto-cat-button>
					</div>
				</div>
				<div class="gauge-wrapper">
					<div id="grafico-andamento"></div>
					<div style="margin-top: -35px">Realizado</div>
				</div>
			</div>
			<div class="vl"></div>
			<div>
				<div class="d-flex flex-column">
					<span class="titulo">Ficha do dia</span>
					<span>
						<a
							[title]="fichaDia?.nome | captalize : true"
							class="link-grande no-cursor">
							{{ fichaDia?.nome | captalize : true }}
						</a>
						<ng-container *ngIf="fichaDia?.vezes === 0">
							<span
								class="card-info-text"
								i18n="@@treino-container:ficha-do-dia:ainda-nao-executada">
								Ainda não executada
							</span>
						</ng-container>
						<div *ngIf="fichaDia?.vezes !== 0" class="card-info-text">
							<span i18n="@@treino-container:ficha-do-dia:executada">
								Executada
							</span>
							{{ fichaDia?.vezes }}
							<span
								*ngIf="fichaDia?.vezes === 1"
								i18n="@@treino-container:ficha-do-dia:executada:vez">
								vez
							</span>
							<span
								*ngIf="fichaDia?.vezes !== 1"
								i18n="@@treino-container:ficha-do-dia:executada:vezes">
								vezes
							</span>
						</div>
					</span>
					<div class="card-action-button">
						<pacto-cat-button
							(click)="imprimirFichaTreino(atual.id, fichaDia.id)"
							*ngIf="fichaDia?.id > 0"
							id="imprimir"
							label="Imprimir"
							size="MEDIUM"
							width="104px"></pacto-cat-button>
					</div>
				</div>
			</div>
			<div class="vl"></div>
			<div class="d-flex flex-column fichas-relacionadas">
				<span class="titulo">Fichas relacionadas</span>
				<div>
					<a
						[title]="ultimaFicha ? (ultimaFicha.nome | captalize : true) : '-'"
						class="link-grande no-cursor">
						{{ ultimaFicha ? (ultimaFicha.nome | captalize : true) : "-" }}
					</a>
					<span class="card-info-text">Última ficha executada</span>
				</div>

				<div>
					<a
						[title]="
							fichaProxima ? (fichaProxima.nome | captalize : true) : '-'
						"
						class="link-grande no-cursor">
						{{ fichaProxima ? (fichaProxima.nome | captalize : true) : "-" }}
					</a>
					<span class="card-info-text">Próxima ficha</span>
				</div>
			</div>
		</div>
	</pacto-cat-card-plain>

	<pacto-cat-card-plain *ngIf="temProgramas" class="card-grid row3">
		<span class="titulo">Lista de programas</span>

		<div *ngIf="ready" class="lista-programas">
			<pacto-relatorio
				#listaProgramaComponent
				#tableData
				(iconClick)="actionClickHandler($event)"
				(rowClick)="abrirModalEdicaoFicha($event)"
				[baseFilter]="baseFilter"
				[enableZebraStyle]="true"
				[showShare]="false"
				[table]="table"
				actionTitulo="Ações"
				idSuffix="tr-tbl-lista-programa"></pacto-relatorio>
		</div>
	</pacto-cat-card-plain>

	<pacto-cat-card-plain *ngIf="atual?.id !== null" class="card-grid row2">
		<div class="d-flex flex-column">
			<span class="titulo">Dias que treinou no programa atual</span>
			<span class="sub-titulo">
				<span i18n="@@treino-container:treinos-executados">
					Treinos executados nos últimos
				</span>
				{{ treinosExecutadosPeriodo }}
				<span *ngIf="treinosExecutadosPeriodo > 1; else singularDay">dias</span>
				<ng-template #singularDay>
					<span>dia</span>
				</ng-template>
			</span>
			<pacto-cat-column-chart
				*ngIf="dias"
				[animation]="false"
				[colors]="chartColors"
				[distributed]="true"
				[legend]="false"
				[series]="[dias]"
				[xAxisLabels]="diaSemanaCurto.getAllLabels()"
				height="193px"></pacto-cat-column-chart>
		</div>
		<div class="vl"></div>
		<div class="d-flex flex-column">
			<span class="titulo">Horários que treinou</span>
			<span class="sub-titulo">
				<span i18n="@@treino-container:treinos-executados">
					Treinos executados nos últimos
				</span>
				{{ treinosExecutadosPeriodo }}
				<ng-container *ngIf="treinosExecutadosPeriodo > 1; else singularDay">
					<span>dias</span>
				</ng-container>
				<ng-template #singularDay>
					<span>dia</span>
				</ng-template>
			</span>
			<div class="treino-executado-pie-chart">
				<pacto-cat-pie-chart
					#pieChartHorarioRef
					[colors]="chartColorsHorario"
					[series]="pieHorario"
					[showLegend]="false"
					height="210px"
					id="grafico-horario"
					labelCenter="treinos"></pacto-cat-pie-chart>
				<div class="legenda">
					<div>
						<i class="pct pct-sunrise"></i>
						<span>Manhã</span>
						<b>{{ porcentagemTreinoManha }}%</b>
					</div>
					<div>
						<i class="pct pct-sunset"></i>
						<span>Tarde</span>
						<b>{{ porcentagemTreinoTarde }}%</b>
					</div>
					<div>
						<i class="pct pct-moon"></i>
						<span>Noite</span>
						<b>{{ porcentagemTreinoNoite }}%</b>
					</div>
				</div>
			</div>
		</div>
	</pacto-cat-card-plain>
	<!--
	<pacto-cat-card-plain>
		<span class="titulo">Grupos musculares</span>
		<span class="sub-titulo" *ngIf="listaAtiva">Treinos executados no período: {{getSomaTreinos()}}</span>
		<div class="row">
			<div class="col-6 row">
				<div class="col-6 frontal">
					<svg viewBox="0 0 316 518" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g id="dataMuscleChart">
							<path id="Body-front"
								d="M155.657 0C157.312 0 158.968 0 160.623 0C160.941 0.324664 161.377 0.287771 161.776 0.361558C170.895 2.06604 177.191 7.2164 180.391 15.9159C182.556 21.7968 182.601 27.9359 182.024 34.0824C181.862 35.8164 182.054 37.2626 183.754 38.1407C184.722 38.6424 184.847 39.5205 184.67 40.4207C183.857 44.4642 183.029 48.5078 182.135 52.5366C181.884 53.6508 181.248 54.5657 180.096 55.0527C178.958 55.5323 178.204 56.4399 177.686 57.5467C177.31 58.351 176.977 59.17 176.548 59.9448C173.77 64.9255 173.208 70.3267 173.459 75.8755C173.57 78.2588 174.413 79.2254 176.726 79.8821C180.842 81.048 184.773 82.6934 188.35 85.0325C191.823 87.2978 195.541 88.7514 199.561 89.6737C204.401 90.7805 209.145 92.2046 213.639 94.3961C219.211 97.1189 222.64 101.458 224.169 107.427C225.337 111.972 225.485 116.628 225.884 121.262C226.246 125.468 226.941 129.556 229.409 133.157C233.473 139.089 235.971 145.656 237.286 152.703C237.915 156.068 238.114 159.484 238.639 162.856C239.4 167.733 241.44 171.806 245.63 174.669C247.736 176.108 249.487 177.968 251.113 179.923C254.66 184.195 257.173 189.05 259.124 194.216C260.543 197.971 262.035 201.698 263.868 205.269C268.908 215.075 275.611 223.627 283.562 231.249C284.737 232.371 285.971 232.895 287.553 232.578C290.272 232.039 292.962 232.437 295.652 232.865C299.739 233.515 303.685 234.806 307.735 235.632C309.59 236.009 311.474 236.134 313.344 236.4C314.223 236.525 314.925 236.887 315.169 237.824C315.398 238.717 315.095 239.432 314.474 240.074C313.514 241.078 312.309 241.521 310.957 241.462C308.599 241.351 306.242 241.166 303.892 241.004C303.33 240.967 302.658 240.554 302.259 241.181C301.852 241.816 302.325 242.413 302.658 242.967C302.717 243.07 302.784 243.166 302.843 243.269C305.215 247.453 308.407 251.024 311.393 254.758C313.189 257.009 315.08 259.207 315.997 262.011C316.012 263.472 315.989 266.048 315.613 268.468C315.524 269.036 315.369 269.877 314.704 270.394C314.268 270.733 313.654 270.844 312.716 270.829C311.474 270.807 310.233 270.77 308.999 270.903C307.772 271.036 306.649 270.792 305.673 269.995C304.468 269.014 303.094 268.637 301.542 268.778C299.968 268.925 298.534 268.505 297.152 267.7C290.442 263.782 284.634 258.846 279.912 252.663C279.232 251.77 278.53 250.855 278.197 249.785C277.414 247.224 275.648 245.468 273.697 243.793C269.396 240.104 265.139 236.363 260.853 232.651C255.857 228.32 250.862 223.989 245.837 219.687C240.811 215.385 235.757 211.128 231.404 206.11C227.088 201.129 223.356 195.787 220.666 189.744C219.418 186.933 218.597 183.974 217.555 181.089C214.902 173.703 210.72 167.114 206.559 160.532C206.441 160.34 206.271 160.148 205.938 160.229C205.436 160.532 205.207 161.085 204.896 161.579C203.455 163.882 202.066 166.213 200.581 168.486C197.625 173.009 196.228 177.982 196.065 183.347C195.977 186.291 195.844 189.235 195.666 192.179C195.548 194.179 195.26 196.164 195.164 198.163C195.001 201.698 194.565 205.232 194.772 208.759C194.927 211.349 194.927 213.946 195.075 216.536C195.23 219.296 195.334 222.085 196.243 224.741C197.987 229.84 199.095 235.086 199.982 240.377C201.623 250.139 203.034 259.938 204.312 269.752C205.391 278.053 206.145 286.383 206.618 294.736C206.943 300.499 207.209 306.284 207.098 312.061C207.054 314.423 207.091 316.784 206.951 319.138C206.662 324.081 206.034 328.988 205.273 333.88C204.261 340.41 203.049 346.896 200.787 353.139C199.339 357.138 198.186 361.226 197.647 365.454C197.174 369.143 196.974 372.833 197.373 376.559C197.765 380.27 198.615 383.878 199.398 387.509C200.233 391.39 200.684 395.316 200.566 399.271C200.366 405.771 199.354 412.176 197.905 418.507C195.112 430.726 193.819 443.144 192.925 455.615C192.548 460.905 192.259 466.203 191.513 471.457C191.048 474.74 191.232 477.906 192.378 481.02C192.644 481.735 192.828 482.488 193.013 483.226C193.361 484.598 193.656 485.985 193.553 487.417C193.375 489.999 194.114 492.294 195.659 494.368C197.107 496.323 198.201 498.463 199.029 500.765C200.152 503.886 202.022 506.506 204.741 508.468C205.074 508.712 205.391 508.978 205.68 509.273C207.771 511.398 207.224 514.94 204.615 516.364C203.5 516.976 202.288 517.19 201.046 517.33C199.391 517.308 197.706 517.308 195.984 517.33C193.205 517.367 190.515 517.47 187.922 517.625C187.109 517.618 182.017 517.559 179.061 517.05C178.662 516.983 177.982 516.843 177.347 516.341C176.563 515.714 176.157 514.755 175.965 513.648C175.713 512.202 175.529 510.704 175.617 509.251C175.787 506.314 176.031 503.377 176.401 500.448C176.925 496.345 177.25 492.243 176.305 488.14C175.824 486.059 175.839 483.971 176.201 481.831C176.999 477.183 177.31 472.504 177.029 467.76C176.637 461.126 175.41 454.663 173.578 448.302C172.092 443.144 170.511 438.016 169.447 432.748C168.279 426.97 167.466 421.134 166.705 415.29C165.929 409.357 165.996 403.425 166.919 397.492C167.392 394.467 168.383 391.545 168.604 388.468C168.789 385.885 169.269 383.325 169.417 380.743C169.646 376.817 168.811 373.002 167.961 369.202C166.882 364.369 165.914 359.521 165.5 354.57C165.19 350.814 164.872 347.051 164.407 343.318C163.527 336.345 163.18 329.35 163.113 322.332C163.054 316.614 163.202 310.895 162.811 305.177C162.522 301.001 162.108 296.839 161.451 292.707C160.763 288.405 159.988 284.111 159.241 279.816C158.805 277.307 158.82 274.769 158.776 272.231C158.761 271.331 158.753 270.401 158.103 269.589C157.371 270.29 157.453 271.08 157.46 271.832C157.468 274.43 157.408 277.034 156.958 279.588C156.211 283.882 155.413 288.169 154.741 292.478C153.994 297.319 153.433 302.174 153.226 307.059C152.952 313.36 153.196 319.676 152.93 325.985C152.753 330.11 152.768 334.234 152.361 338.344C151.947 342.447 151.452 346.542 151.09 350.645C150.573 356.526 149.804 362.355 148.444 368.103C147.136 373.637 146.168 379.193 147.159 384.904C147.55 387.169 147.499 389.494 148.068 391.729C149.56 397.596 150.233 403.55 149.982 409.601C149.812 413.615 149.146 417.562 148.6 421.532C147.602 428.785 146.301 435.972 144.151 442.982C141.779 450.715 139.754 458.514 139.207 466.638C138.919 470.837 139.037 474.998 139.554 479.16C139.902 481.957 140.626 484.724 139.976 487.572C138.948 492.073 139.237 496.603 139.857 501.112C140.345 504.69 140.559 508.262 140.434 511.87C140.412 512.586 140.279 513.272 140.116 513.966C139.813 515.316 139.126 516.356 137.803 516.895C137.456 517.035 137.271 517.256 137.256 517.264C134.736 518.99 113.778 517.138 113.778 517.138C113.327 517.102 112.514 516.843 111.62 516.364C108.716 514.814 108.31 510.785 110.97 508.83C113.667 506.852 115.766 504.425 116.926 501.304C117.924 498.625 119.21 496.139 120.865 493.807C122.018 492.184 122.75 490.361 122.661 488.354C122.55 485.712 123.053 483.196 123.91 480.724C124.774 478.238 125.07 475.677 124.797 473.05C124.508 470.239 124.235 467.421 123.991 464.602C123.548 459.437 123.09 454.272 122.713 449.099C122.41 444.923 121.981 440.761 121.493 436.607C120.695 429.87 119.254 423.244 117.88 416.603C116.801 411.401 116.01 406.155 115.663 400.857C115.3 395.271 116.165 389.804 117.458 384.388C118.493 380.071 119.195 375.695 119.047 371.261C118.899 366.657 118.234 362.096 116.875 357.677C116.128 355.256 115.293 352.858 114.51 350.445C112.899 345.494 111.96 340.388 111.147 335.267C110.179 329.15 109.307 323.026 109.182 316.828C109.026 308.984 109.13 301.133 109.64 293.305C109.965 288.25 110.298 283.196 110.911 278.156C111.465 273.655 111.879 269.132 112.485 264.638C113.342 258.329 114.273 252.035 115.271 245.749C116.424 238.532 117.643 231.323 120.015 224.358C120.621 222.572 120.909 220.698 120.924 218.787C120.932 217.901 120.998 217.016 121.05 216.138C121.397 210.714 121.456 205.284 121.139 199.868C120.821 194.459 120.281 189.058 120.134 183.634C119.986 178.241 118.737 173.142 115.677 168.575C114.495 166.811 113.446 164.959 112.322 163.159C111.642 162.066 110.933 160.997 110.216 159.875C109.677 160.2 109.477 160.635 109.233 161.019C108.354 162.421 107.497 163.83 106.625 165.232C102.745 171.467 99.3382 177.916 97.306 185.022C96.6926 187.176 95.7393 189.22 94.7712 191.242C90.2042 200.731 83.2946 208.346 75.4761 215.208C71.6185 218.595 67.6796 221.901 63.7925 225.258C58.7009 229.656 53.6166 234.068 48.5397 238.488C45.9162 240.775 43.3076 243.092 40.7063 245.402C39.6274 246.361 38.8293 247.497 38.3489 248.877C37.8981 250.161 37.233 251.349 36.4054 252.441C31.6684 258.691 25.823 263.679 19.0685 267.634C17.6423 268.475 16.1421 268.896 14.5016 268.719C13.0088 268.564 11.6934 268.947 10.5479 269.914C9.6907 270.637 8.69305 270.925 7.58456 270.844C6.10657 270.733 4.63597 270.748 3.15798 270.763C1.73172 270.777 1.14791 270.312 0.682344 268.947C0.394136 268.099 0.268507 267.42 0.246337 267.236C-0.0049213 265.081 -0.17489 263.59 0.305457 261.797C0.423696 261.362 0.564105 260.292 2.03471 258.329C3.30578 256.625 4.6138 254.95 5.94399 253.297C8.49352 250.124 11.1613 247.04 13.2009 243.491C13.4965 242.982 13.8143 242.472 14.0138 241.926C14.2946 241.152 14.0064 240.798 13.2083 240.82C12.9127 240.827 12.6245 240.93 12.3363 240.945C9.9789 241.107 7.6289 241.299 5.26411 241.395C3.86002 241.454 2.57417 241.034 1.5987 239.942C0.608445 238.835 0.660174 236.547 2.83282 236.348C4.00782 236.245 5.18283 236.112 6.35044 235.935C10.0971 235.359 13.7182 234.26 17.4058 233.434C21.1599 232.592 24.9288 231.847 28.8085 232.548C30.353 232.828 31.5576 232.282 32.6291 231.205C34.5062 229.331 36.3758 227.442 38.1716 225.479C45.9088 217.023 51.9538 207.519 56.0479 196.806C57.3781 193.33 58.7748 189.892 60.5557 186.615C63.2161 181.731 66.6229 177.466 71.1381 174.168C74.1902 171.939 76.0303 168.98 77.0058 165.372C77.7448 162.62 77.9591 159.794 78.2842 156.99C79.2819 148.482 81.7575 140.506 86.6349 133.371C88.0168 131.349 88.9923 129.157 89.517 126.759C89.9825 124.627 90.2412 122.457 90.4038 120.281C90.6698 116.754 90.8472 113.219 91.5122 109.736C92.0961 106.652 92.9828 103.656 94.7343 100.993C96.9512 97.628 100.107 95.407 103.72 93.7615C107.992 91.8209 112.492 90.6182 117.03 89.4745C120.119 88.6997 123.208 87.9471 125.935 86.1319C130.28 83.2321 134.973 81.0111 140.042 79.646C141.668 79.2107 142.429 78.1408 142.629 76.4658C142.88 74.2817 142.769 72.1197 142.606 69.943C142.407 67.3309 141.875 64.8074 140.848 62.4019C140.131 60.7196 139.318 59.0815 138.542 57.4287C138.054 56.3809 137.345 55.5028 136.266 55.0453C134.862 54.4477 134.189 53.3335 133.901 51.9463C133.11 48.1979 132.342 44.4421 131.61 40.6789C131.388 39.5279 131.507 38.5096 132.77 37.8308C133.894 37.2331 134.426 36.1115 134.248 34.835C133.849 31.9499 134.019 29.0648 134.012 26.1797C134.004 23.3906 134.381 20.6383 135.15 17.9672C137.869 8.52244 143.988 2.60469 153.647 0.413209C154.297 0.324664 155.066 0.509132 155.657 0Z"
								fill="#DCDDDF" />
							<g id="peitoral" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('peitoral')">
								<path id="Peitoral L"
									d="M155.613 118.79C155.11 122.516 156.071 127.689 154.527 132.994C151.933 141.93 146.693 142.786 139.518 142.55C132.933 142.336 126.504 140.897 120.629 137.444C115.249 134.286 110.978 130.08 107.423 125.07C106.662 124 106.448 122.959 107.926 122C114.628 117.632 118.789 111.205 122.07 104.114C125.617 96.4474 126.755 96.595 134.515 96.3663C138.742 96.2408 143.08 96.6909 147.358 97.0008C152.724 97.3845 155.406 100.373 155.583 105.9C155.716 109.707 155.613 113.514 155.613 118.79Z"
									[attr.fill]="paintDobras('peitoral')" />
								<path id="Peitoral R"
									d="M160.046 118.79C160.548 122.516 159.588 127.689 161.132 132.994C163.726 141.93 168.965 142.786 176.141 142.55C182.726 142.336 189.155 140.897 195.03 137.444C200.41 134.286 204.681 130.08 208.236 125.07C208.997 124 209.211 122.959 207.733 122C201.03 117.632 196.87 111.205 193.589 104.114C190.042 96.4474 188.904 96.595 181.144 96.3663C176.917 96.2408 172.579 96.6909 168.3 97.0008C162.935 97.3845 160.253 100.373 160.075 105.9C159.95 109.707 160.046 113.514 160.046 118.79Z"
									[attr.fill]="paintDobras('peitoral')" />
							</g>
							<g id="biceps" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('biceps')">
								<path id="Biceps L"
									d="M82.5333 180.69C80.2054 172.035 80.0724 163.823 81.1957 155.47C82.6293 144.837 86.679 135.82 95.7761 129.556C96.2712 129.216 96.7589 128.877 97.2614 128.552C102.457 125.232 103.159 125.483 104.319 131.725C105.583 138.55 104.348 145.236 102.021 151.574C97.9413 162.672 91.3199 172.22 82.5333 180.69Z"
									[attr.fill]="paintDobras('biceps')" />
								<path id="Biceps R"
									d="M233.126 180.69C235.454 172.035 235.587 163.823 234.464 155.47C233.03 144.837 228.98 135.82 219.883 129.556C219.388 129.216 218.9 128.877 218.398 128.552C213.203 125.232 212.501 125.483 211.341 131.725C210.077 138.55 211.311 145.236 213.639 151.574C217.725 162.672 224.339 172.22 233.126 180.69Z"
									[attr.fill]="paintDobras('biceps')" />
							</g>
							<g id="pelvis" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('pelvis')">
								<path id="Vector_8"
									d="M129.26 234.562C136.59 239.418 142.746 245.394 149.116 251.157C154.482 256.012 155.442 261.288 154.511 268.165C153.513 275.529 152.265 282.856 151.267 290.22C151.045 291.866 149.9 292.626 149.294 293.91C149.449 283.063 146.523 272.961 142.865 262.993C139.251 253.113 134.684 243.697 129.26 234.562Z"
									[attr.fill]="paintDobras('pelvis')" />
								<path id="Vector_9"
									d="M186.399 234.562C179.068 239.418 172.912 245.394 166.542 251.157C161.177 256.012 160.216 261.288 161.147 268.165C162.145 275.529 163.394 282.856 164.392 290.22C164.613 291.866 165.759 292.626 166.365 293.91C166.209 283.063 169.136 272.961 172.794 262.993C176.415 253.113 180.982 243.697 186.399 234.562Z"
									[attr.fill]="paintDobras('pelvis')" />
							</g>
							<g id="coxaMedial" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('coxaMedial')">
								<path id="Coxa-medial L"
									d="M132.674 340.743C125.491 306.011 119.572 271.235 124.693 235.278C126.836 239.536 129.186 243.712 131.07 248.08C134.788 256.669 138.564 265.251 141.431 274.172C142.835 278.562 144.653 282.849 144.638 287.623C144.624 292.242 145.001 296.824 144.742 301.495C143.959 315.559 139.399 328.361 132.674 340.743Z"
									[attr.fill]="paintDobras('coxaMedial')" />
								<path id="Coxa-medial R"
									d="M182.992 340.743C190.175 306.011 196.095 271.235 190.973 235.278C188.83 239.536 186.48 243.712 184.596 248.08C180.879 256.669 177.102 265.251 174.235 274.172C172.831 278.562 171.013 282.849 171.028 287.623C171.043 292.242 170.666 296.824 170.924 301.495C171.7 315.559 176.26 328.361 182.992 340.743Z"
									[attr.fill]="paintDobras('coxaMedial')" />
							</g>
							<g id="joelhoExt" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('joelhoExt')">
								<path id="Vector_10"
									d="M117.6 268.955C119.211 284.251 120.4 299.613 122.588 314.828C123.985 324.554 125.462 334.36 128.729 343.753C130.887 349.944 130.244 355.817 126.423 361.359C124.79 363.727 123.645 363.484 121.834 361.669C120.253 360.075 119.824 358.046 119.151 356.112C115.9 346.727 113.809 337.12 113.52 327.143C112.951 307.627 115.493 288.339 117.6 268.955Z"
									[attr.fill]="paintDobras('joelhoExt')" />
								<path id="Vector_11"
									d="M198.062 268.955C196.451 284.251 195.261 299.613 193.074 314.828C191.677 324.554 190.199 334.36 186.933 343.753C184.775 349.944 185.418 355.817 189.238 361.359C190.872 363.727 192.017 363.484 193.828 361.669C195.409 360.075 195.838 358.046 196.51 356.112C199.762 346.727 201.853 337.12 202.141 327.143C202.71 307.627 200.168 288.339 198.062 268.955Z"
									[attr.fill]="paintDobras('joelhoExt')" />
							</g>
							<g id="joelhoInt" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('joelhoInt')">
								<path id="Vector_12"
									d="M148.534 304.115C150.706 317.492 149.583 330.604 148.778 343.657C148.423 349.383 147.181 355.227 144.462 360.54C143.627 362.17 142.474 363.558 140.752 363.816C138.875 364.096 137.715 362.665 136.769 361.093C133.436 355.544 133.421 349.818 136.621 344.425C140.501 337.894 142.489 330.737 144.432 323.572C146.139 317.256 147.159 310.755 148.534 304.115Z"
									[attr.fill]="paintDobras('joelhoInt')" />
								<path id="Vector_13"
									d="M167.126 304.115C164.954 317.492 166.077 330.604 166.883 343.657C167.237 349.383 168.479 355.227 171.198 360.54C172.033 362.17 173.186 363.558 174.908 363.816C176.785 364.096 177.945 362.665 178.891 361.093C182.224 355.544 182.239 349.818 179.039 344.425C175.159 337.894 173.171 330.737 171.228 323.572C169.521 317.256 168.501 310.755 167.126 304.115Z"
									[attr.fill]="paintDobras('joelhoInt')" />
							</g>
							<g id="patela" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('patela')">
								<path id="Vector_30"
									d="M190.309 367.018C190.331 364.399 184.877 362.192 181.678 363.676C179.468 364.701 177.458 366.066 179.49 368.966C179.557 369.062 179.601 369.18 179.653 369.298C180.902 372.073 182.165 374.98 185.528 375.607C186.865 375.858 190.287 369.372 190.309 367.018Z"
									[attr.fill]="paintDobras('patela')" />
								<path id="Vector_4"
									d="M125.351 367.018C125.329 364.399 130.783 362.193 133.983 363.676C136.192 364.701 138.202 366.066 136.17 368.966C136.104 369.062 136.059 369.18 136.008 369.298C134.759 372.073 133.495 374.98 130.133 375.607C128.795 375.858 125.373 369.372 125.351 367.018Z"
									[attr.fill]="paintDobras('patela')" />
							</g>
							<g id="abdominal" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('abdominal')">
								<path id="abdominal4 L"
									d="M138.025 197.846C142.592 200.465 147.322 201.52 152.228 201.587C154.667 201.624 155.754 202.221 155.731 204.9C155.62 216.942 155.694 228.984 155.665 241.034C155.665 241.941 156.086 243.159 154.83 243.587C153.803 243.941 152.879 243.232 152.14 242.664C148.955 240.214 147.115 236.724 145.215 233.301C140.693 225.155 136.909 207.763 138.025 197.846Z"
									[attr.fill]="paintDobras('abdominal')" />
								<path id="abdominal3 L"
									d="M155.583 189.678C155.628 197.108 155.628 197.056 148.23 196.584C142.924 196.245 138.971 193.81 136.473 189.191C134.064 184.719 135.919 181.2 140.899 181.126C144.144 181.074 146.922 179.333 150.233 179.458C153.196 179.569 154.682 180.248 155.155 183.391C155.495 185.59 155.428 187.767 155.583 189.678Z"
									[attr.fill]="paintDobras('abdominal')" />
								<path id="abdominal2 L"
									d="M154.853 169.49C154.853 170.596 154.793 171.703 154.867 172.803C154.978 174.551 154.173 175.393 152.473 175.127C148.446 174.492 144.795 176.366 140.923 176.765C138.75 176.986 136.348 178.779 134.7 175.975C133.385 173.74 134.139 167.984 136.104 166.708C139.038 164.804 141.772 162.465 145.408 161.779C153.249 160.288 154.941 161.631 154.853 169.49Z"
									[attr.fill]="paintDobras('abdominal')" />
								<path id="abdominal1 L"
									d="M154.926 152.607C155.628 155.854 153.892 156.938 150.389 156.68C145.585 156.318 141.432 158.296 137.456 160.76C136.673 161.247 135.624 162.037 134.774 161.454C133.732 160.738 134.722 159.683 134.892 158.775C135.867 153.551 138.461 149.508 143.398 147.183C145.755 146.069 148.334 145.73 150.847 145.789C153.5 145.848 154.904 148.335 154.926 152.607Z"
									[attr.fill]="paintDobras('abdominal')" />
								<path id="abdominal4 R"
									d="M177.635 197.846C173.068 200.465 168.338 201.52 163.431 201.587C160.993 201.624 159.906 202.221 159.928 204.9C160.039 216.942 159.965 228.984 159.995 241.034C159.995 241.941 159.574 243.159 160.83 243.587C161.857 243.941 162.781 243.232 163.52 242.664C166.705 240.214 168.545 236.724 170.444 233.301C174.967 225.155 178.751 207.763 177.635 197.846Z"
									[attr.fill]="paintDobras('abdominal')" />
								<path id="abdominal3 R"
									d="M160.077 189.678C160.033 197.108 160.033 197.056 167.43 196.584C172.736 196.245 176.69 193.81 179.188 189.191C181.597 184.719 179.742 181.199 174.761 181.126C171.517 181.074 168.738 179.333 165.428 179.458C162.464 179.569 160.979 180.248 160.506 183.391C160.166 185.59 160.24 187.767 160.077 189.678Z"
									[attr.fill]="paintDobras('abdominal')" />
								<path id="abdominal2 R"
									d="M160.809 169.49C160.809 170.596 160.868 171.703 160.794 172.803C160.683 174.551 161.488 175.393 163.188 175.127C167.216 174.492 170.866 176.366 174.739 176.765C176.911 176.986 179.313 178.779 180.961 175.975C182.276 173.74 181.523 167.984 179.557 166.708C176.623 164.804 173.889 162.465 170.253 161.779C162.412 160.288 160.72 161.631 160.809 169.49Z"
									[attr.fill]="paintDobras('abdominal')" />
								<path id="abdominal1 R"
									d="M160.743 152.607C160.041 155.854 161.777 156.938 165.28 156.68C170.084 156.318 174.237 158.296 178.213 160.76C178.996 161.247 180.045 162.037 180.895 161.454C181.937 160.738 180.947 159.683 180.777 158.775C179.801 153.551 177.208 149.508 172.271 147.184C169.914 146.069 167.335 145.73 164.822 145.789C162.169 145.848 160.758 148.335 160.743 152.607Z"
									[attr.fill]="paintDobras('abdominal')" />
							</g>
							<g id="escapular" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('escapular')">
								<path id="Vector_14"
									d="M135.431 93.2377C131.751 93.2377 128.063 93.2525 124.383 93.223C123.814 93.223 123.075 93.4 122.802 92.6474C122.536 91.9317 123.097 91.5554 123.585 91.179C128.307 87.4749 133.621 84.7817 139.03 82.2729C140.944 81.3875 142.636 81.5129 143.811 83.852C144.957 86.132 146.494 88.2276 148.001 90.3084C149.538 92.4261 149.191 93.3632 146.494 93.3263C142.814 93.2746 139.126 93.3115 135.446 93.3115C135.431 93.282 135.431 93.2599 135.431 93.2377Z"
									[attr.fill]="paintDobras('escapular')" />
								<path id="Vector_15"
									d="M180.229 93.2377C183.909 93.2377 187.596 93.2525 191.277 93.223C191.846 93.223 192.585 93.4 192.858 92.6474C193.124 91.9317 192.563 91.5554 192.075 91.179C187.353 87.4749 182.039 84.7817 176.63 82.2729C174.716 81.3875 173.023 81.5129 171.848 83.852C170.703 86.132 169.166 88.2276 167.658 90.3084C166.121 92.4261 166.469 93.3632 169.166 93.3263C172.846 93.2746 176.534 93.3115 180.214 93.3115C180.229 93.282 180.229 93.2599 180.229 93.2377Z"
									[attr.fill]="paintDobras('escapular')" />
							</g>
							<g id="ombros" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('ombros')">
								<path id="Vector_16"
									d="M120.288 99.4505C120.192 105.531 115.965 109.736 111.849 113.861C106.949 118.768 101.614 123.21 95.8643 127.136C95.3248 127.504 94.8075 128.006 94.2828 127.667C93.3591 127.069 94.2016 126.412 94.4528 125.793C99.0863 114.577 106.188 105.051 114.679 96.4917C115.854 95.3037 117.354 93.2893 118.648 93.7837C120.658 94.5437 120.118 97.1189 120.288 99.4505Z"
									[attr.fill]="paintDobras('ombros')" />
								<path id="Vector_17"
									d="M114.229 93.2228C111.783 95.8497 109.27 98.4101 106.905 101.111C101.585 107.183 97.5497 114.09 94.2612 121.852C90.9505 106.571 99.6928 95.0601 114.281 93.2745L114.229 93.2228Z"
									[attr.fill]="paintDobras('ombros')" />
								<path id="Vector_18"
									d="M195.377 99.4505C195.473 105.531 199.7 109.736 203.817 113.861C208.716 118.768 214.052 123.21 219.801 127.136C220.341 127.504 220.858 128.006 221.383 127.667C222.306 127.069 221.464 126.412 221.213 125.793C216.579 114.577 209.477 105.051 200.986 96.4916C199.811 95.3037 198.311 93.2893 197.018 93.7836C195.008 94.5436 195.54 97.1188 195.377 99.4505Z"
									[attr.fill]="paintDobras('ombros')" />
								<path id="Vector_19"
									d="M201.429 93.2228C203.875 95.8497 206.388 98.4101 208.753 101.111C214.073 107.183 218.108 114.09 221.397 121.852C224.707 106.571 215.965 95.0601 201.377 93.2745L201.429 93.2228Z"
									[attr.fill]="paintDobras('ombros')" />
							</g>
							<g id="pe" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('pe')">
								<path id="Vector" opacity="0.1"
									d="M187.582 517.633C187.7 517.53 187.819 517.515 187.937 517.633C187.819 517.633 187.7 517.633 187.582 517.633Z"
									[attr.fill]="paintDobras('pe')" />
								<path id="Vector_2"
									d="M120.385 515.168C118.67 515.161 116.956 515.168 115.242 515.161C114.2 515.154 113.283 514.873 112.906 513.766C112.552 512.711 112.832 511.737 113.719 511.162C116.439 509.398 117.961 506.875 118.715 503.798C119.188 501.864 120.57 501.628 122.129 502.226C123.474 502.743 124.701 503.547 125.994 504.189C130.753 506.543 131.433 508.712 128.846 513.294C128.122 514.585 127.051 515.213 125.543 515.176C123.814 515.139 122.099 515.176 120.385 515.168Z"
									[attr.fill]="paintDobras('pe')" />
								<path id="Vector_3"
									d="M137.869 501.437C137.92 505.037 137.033 505.635 134.063 503.908C131.225 502.263 128.358 500.743 125.276 499.57C122.793 498.625 121.256 497.068 123.532 494.323C124.057 493.696 124.315 492.796 124.522 491.977C125.564 487.874 126.348 487.609 129.71 490.42C132.422 492.685 134.89 495.157 136.827 498.168C137.536 499.267 137.979 500.293 137.869 501.437Z"
									[attr.fill]="paintDobras('pe')" />
								<path id="Vector_5"
									d="M137.137 486.052C137.137 486.908 137.159 487.764 137.13 488.627C137.108 489.476 137.181 490.45 136.154 490.782C135.267 491.062 134.536 490.671 133.856 490.014C131.861 488.088 129.814 486.207 127.774 484.333C126.237 482.916 126.54 481.101 127.146 479.581C127.937 477.588 129.363 479.057 130.538 479.418C131.003 479.559 131.476 479.691 131.957 479.773C137.167 480.643 137.167 480.643 137.137 486.052Z"
									[attr.fill]="paintDobras('pe')" />
								<path id="Vector_6"
									d="M137.855 512.372C138.453 515.006 136.65 515.64 134.758 515.766C132.948 515.884 131.558 514.851 132.046 512.866C132.519 510.911 131.765 507.627 134.411 507.472C136.672 507.339 138.305 509.619 137.855 512.372Z"
									[attr.fill]="paintDobras('pe')" />
								<path id="Vector_31"
									d="M178.53 486.052C178.53 486.908 178.508 487.764 178.538 488.627C178.56 489.476 178.486 490.45 179.513 490.782C180.4 491.062 181.131 490.671 181.811 490.014C183.807 488.088 185.854 486.207 187.893 484.333C189.43 482.916 189.127 481.101 188.521 479.581C187.731 477.588 186.304 479.057 185.129 479.418C184.664 479.559 184.191 479.691 183.711 479.773C178.493 480.643 178.493 480.643 178.53 486.052Z"
									[attr.fill]="paintDobras('pe')" />
								<path id="Vector_38"
									d="M177.804 512.372C177.206 515.006 179.009 515.641 180.901 515.766C182.711 515.884 184.101 514.851 183.613 512.866C183.14 510.911 183.894 507.627 181.248 507.472C178.987 507.34 177.354 509.62 177.804 512.372Z"
									[attr.fill]="paintDobras('pe')" />
								<path id="Vector_20"
									d="M195.282 515.168C196.996 515.161 198.711 515.168 200.425 515.161C201.467 515.154 202.383 514.873 202.76 513.766C203.115 512.711 202.834 511.737 201.947 511.162C199.228 509.398 197.706 506.875 196.952 503.798C196.479 501.864 195.097 501.628 193.538 502.226C192.193 502.743 190.966 503.547 189.673 504.189C184.914 506.543 184.234 508.712 186.82 513.294C187.544 514.585 188.616 515.213 190.124 515.176C191.845 515.139 193.567 515.176 195.282 515.168Z"
									[attr.fill]="paintDobras('pe')" />
								<path id="Vector_21"
									d="M177.797 501.437C177.745 505.037 178.632 505.635 181.603 503.908C184.441 502.263 187.308 500.743 190.39 499.57C192.873 498.625 194.41 497.068 192.134 494.323C191.609 493.696 191.35 492.796 191.143 491.977C190.101 487.874 189.318 487.609 185.956 490.42C183.243 492.685 180.775 495.157 178.839 498.168C178.122 499.267 177.679 500.293 177.797 501.437Z"
									[attr.fill]="paintDobras('pe')" />
							</g>
							<g id="panturrilha" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true"
								position="top" (mouseover)="tooltipParte('panturrilha')">
								<path id="Vector_22"
									d="M128.278 468.129C128.67 461.363 127.369 454.744 126.911 448.059C126.409 440.828 124.79 433.803 123.46 426.734C121.857 418.219 119.736 409.8 118.162 401.285C117.445 397.404 118.797 393.523 119.418 389.663C120.061 385.686 120.571 381.68 122.278 377.953C122.603 377.238 122.219 376.013 123.445 375.969C124.687 375.924 124.413 377.105 124.702 377.85C127.443 384.904 128.36 392.445 128.766 399.772C129.609 414.928 131.715 430.04 131.168 445.262C130.909 452.471 130.222 459.665 129.705 466.867C129.675 467.295 129.365 467.701 129.18 468.121C128.877 468.121 128.581 468.121 128.278 468.129Z"
									[attr.fill]="paintDobras('panturrilha')" />
								<path id="Vector_23"
									d="M142.822 383.82C143.642 383.775 144.145 384.447 144.293 385.111C146.546 394.895 148.313 404.672 147.012 414.847C145.349 427.863 141.477 440.341 138.425 453.025C137.154 458.308 136.023 463.613 133.747 468.631C136.703 440.355 139.097 412.036 142.822 383.82Z"
									[attr.fill]="paintDobras('panturrilha')" />
								<path id="Vector_24"
									d="M143.442 368.073C138.794 380.337 139.237 393.626 135.15 405.941C134.168 400.916 135.793 395.928 135.675 390.896C135.594 387.59 135.904 384.262 135.594 380.979C134.995 374.662 139.067 371.372 143.442 368.073Z"
									[attr.fill]="paintDobras('panturrilha')" />
								<path id="Vector_25"
									d="M187.383 468.129C186.991 461.363 188.292 454.744 188.75 448.059C189.253 440.828 190.871 433.803 192.201 426.734C193.805 418.219 195.926 409.8 197.5 401.285C198.217 397.404 196.864 393.523 196.243 389.663C195.601 385.686 195.091 381.68 193.384 377.953C193.058 377.238 193.443 376.013 192.216 375.969C190.974 375.924 191.248 377.105 190.96 377.85C188.218 384.904 187.302 392.445 186.895 399.772C186.053 414.928 183.947 430.04 184.493 445.262C184.752 452.471 185.439 459.665 185.957 466.867C185.986 467.295 186.297 467.701 186.481 468.121C186.784 468.121 187.087 468.121 187.383 468.129Z"
									[attr.fill]="paintDobras('panturrilha')" />
								<path id="Vector_26"
									d="M172.84 383.82C172.019 383.775 171.517 384.447 171.369 385.111C169.115 394.895 167.349 404.672 168.65 414.847C170.312 427.863 174.185 440.341 177.237 453.025C178.508 458.308 179.638 463.613 181.915 468.631C178.966 440.355 176.564 412.036 172.84 383.82Z"
									[attr.fill]="paintDobras('panturrilha')" />
								<path id="Vector_27"
									d="M172.219 368.073C176.868 380.337 176.424 393.626 180.511 405.941C181.494 400.916 179.868 395.928 179.986 390.896C180.067 387.59 179.757 384.262 180.067 380.979C180.666 374.663 176.594 371.372 172.219 368.073Z"
									[attr.fill]="paintDobras('panturrilha')" />
							</g>
							<g id="triceps" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('triceps')">
								<path id="Vector_28"
									d="M93.2424 172.198C100.004 163.476 104.431 154.503 106.544 144.417C110.025 157.086 104.313 169.076 93.2424 172.198Z"
									[attr.fill]="paintDobras('triceps')" />
								<path id="Vector_29"
									d="M222.418 172.198C215.657 163.476 211.23 154.503 209.116 144.417C205.636 157.086 211.356 169.076 222.418 172.198Z"
									[attr.fill]="paintDobras('triceps')" />
							</g>
							<g id="antibraco" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('antibraco')">
								<path id="Vector_32"
									d="M39.577 228.977C42.4813 226.165 46.6049 221.694 50.3959 215.466C54.6747 208.427 56.1157 203.24 57.8154 198.872C62.0055 188.091 69.1812 180.041 78.6403 173.304C80.6725 180.823 79.5345 187.442 75.3296 193.736C67.2967 205.771 57.3203 215.599 52.0291 220.381C49.3909 222.764 45.6737 226.011 41.055 229.715C40.5599 229.471 40.0722 229.22 39.577 228.977Z"
									[attr.fill]="paintDobras('antibraco')" />
								<path id="Vector_33"
									d="M46.3605 233.389C48.6514 229.693 52.0507 226.889 54.9845 223.775C64.8797 213.267 73.7994 202.103 80.273 189.132C83.0664 183.539 86.8426 179.074 92.6142 176.145C95.9101 174.47 95.8879 175.872 95.7845 178.255C95.4519 185.605 92.289 191.84 88.025 197.617C80.5907 207.667 70.6882 215.142 61.1847 223.015C56.5956 226.815 51.7847 230.349 47.0773 234.009C46.8408 233.795 46.597 233.588 46.3605 233.389Z"
									[attr.fill]="paintDobras('antibraco')" />
								<path id="Vector_34"
									d="M45.9723 229.231C44.051 233.4 42.1517 236.27 40.7994 238.085C40.0234 239.126 38.9445 240.476 37.8434 240.299C37.533 240.247 37.3113 240.092 35.6264 238.085C34.3036 236.506 34.1336 236.248 34.1484 235.872C34.178 234.809 35.6116 234.049 36.3654 233.658C39.8682 231.85 43.5558 230.242 45.9723 229.231Z"
									[attr.fill]="paintDobras('antibraco')" />
								<path id="Vector_35"
									d="M276.083 228.977C273.179 226.165 269.055 221.694 265.264 215.466C260.985 208.427 259.544 203.24 257.844 198.872C253.654 188.091 246.479 180.041 237.02 173.304C234.987 180.823 236.125 187.442 240.33 193.736C248.363 205.771 258.34 215.599 263.631 220.381C266.269 222.764 269.986 226.011 274.605 229.715C275.1 229.471 275.595 229.22 276.083 228.977Z"
									[attr.fill]="paintDobras('antibraco')" />
								<path id="Vector_36"
									d="M269.3 233.389C267.009 229.693 263.609 226.889 260.676 223.775C250.78 213.267 241.861 202.103 235.387 189.132C232.594 183.539 228.818 179.074 223.046 176.145C219.75 174.47 219.772 175.872 219.876 178.255C220.208 185.605 223.371 191.84 227.635 197.617C235.069 207.667 244.972 215.142 254.475 223.015C259.065 226.815 263.875 230.349 268.583 234.009C268.827 233.795 269.063 233.588 269.3 233.389Z"
									[attr.fill]="paintDobras('antibraco')" />
								<path id="Vector_37"
									d="M269.688 229.231C271.609 233.4 273.509 236.27 274.861 238.085C275.637 239.126 276.716 240.476 277.817 240.299C278.127 240.247 278.349 240.092 280.034 238.085C281.357 236.506 281.527 236.248 281.512 235.872C281.482 234.809 280.049 234.049 279.295 233.658C275.792 231.85 272.105 230.242 269.688 229.231Z"
									[attr.fill]="paintDobras('antibraco')" />
							</g>

							<g id="costelas" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('costelas')">
								<path id="Vector_39"
									d="M120.022 153.212C122.963 154.297 125.054 155.293 127.449 155.322C128.956 155.337 129.828 156.481 130.36 157.721C130.796 158.731 130.235 159.609 129.444 160.288C128.254 161.299 127.101 161.115 125.756 160.414C122.97 158.945 120.938 156.983 120.022 153.212Z"
									[attr.fill]="paintDobras('costelas')" />
								<path id="Vector_40"
									d="M129.046 151.345C124.863 150.984 122.572 149.213 121.249 146.18C121.072 145.774 120.983 145.14 121.183 144.815C121.604 144.136 122.173 144.527 122.735 144.844C124.56 145.9 126.592 146.269 128.662 146.372C130.487 146.46 131.366 147.338 131.285 149.131C131.196 151.124 129.711 151.286 129.046 151.345Z"
									[attr.fill]="paintDobras('costelas')" />
								<path id="Vector_41"
									d="M120.193 163.395C123.673 163.609 125.898 165.579 128.654 165.948C129.844 166.103 129.925 167.586 129.718 168.722C129.482 169.999 128.44 170.013 127.45 169.999C123.429 169.917 122.21 166.745 120.193 163.395Z"
									[attr.fill]="paintDobras('costelas')" />
								<path id="Vector_42"
									d="M121.833 173.194C123.902 173.902 125.669 174.669 127.509 175.09C129.024 175.437 129.216 176.374 129.024 177.525C128.765 179.038 127.605 179.517 126.275 179.2C123.252 178.484 121.996 176.396 121.833 173.194Z"
									[attr.fill]="paintDobras('costelas')" />
								<path id="Vector_43"
									d="M195.636 153.212C192.695 154.297 190.603 155.293 188.209 155.322C186.701 155.337 185.829 156.481 185.297 157.721C184.861 158.731 185.423 159.609 186.214 160.288C187.403 161.299 188.556 161.115 189.901 160.414C192.695 158.945 194.719 156.983 195.636 153.212Z"
									[attr.fill]="paintDobras('costelas')" />
								<path id="Vector_44"
									d="M186.613 151.345C190.796 150.984 193.087 149.213 194.41 146.18C194.587 145.774 194.676 145.14 194.476 144.815C194.055 144.136 193.486 144.527 192.924 144.844C191.099 145.9 189.067 146.269 186.997 146.372C185.172 146.46 184.293 147.338 184.374 149.132C184.463 151.124 185.948 151.286 186.613 151.345Z"
									[attr.fill]="paintDobras('costelas')" />
								<path id="Vector_45"
									d="M195.466 163.395C191.985 163.609 189.761 165.579 187.004 165.948C185.815 166.103 185.733 167.586 185.94 168.722C186.177 169.999 187.219 170.013 188.209 169.999C192.229 169.917 193.456 166.745 195.466 163.395Z"
									[attr.fill]="paintDobras('costelas')" />
								<path id="Vector_46"
									d="M193.833 173.194C191.764 173.902 189.997 174.669 188.157 175.09C186.642 175.437 186.45 176.374 186.642 177.525C186.901 179.038 188.061 179.517 189.391 179.2C192.407 178.484 193.67 176.396 193.833 173.194Z"
									[attr.fill]="paintDobras('costelas')" />
							</g>
							<g id="supraIliaca" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true"
								position="top" (mouseover)="tooltipParte('supraIliaca')">
								<path id="supra-iliaca L"
									d="M124.915 183.775C126.785 184.018 127.871 185.56 128.551 186.726C129.985 189.176 131.16 191.707 131.418 194.85C132.372 206.509 133.621 218.16 136.067 229.634C136.274 230.593 136.88 231.685 135.978 232.356C135.032 233.057 134.212 232.113 133.406 231.545C127.45 227.383 124.457 221.79 124.597 214.433C124.738 207.077 124.657 199.713 124.553 192.356C124.516 190.054 124.051 187.752 123.844 185.45C123.777 184.675 123.578 183.635 124.915 183.775Z"
									[attr.fill]="paintDobras('supraIliaca')" />
								<path id="supra-iliaca R"
									d="M190.754 183.775C188.884 184.018 187.798 185.56 187.118 186.726C185.684 189.176 184.509 191.707 184.251 194.85C183.298 206.509 182.049 218.16 179.603 229.634C179.396 230.593 178.79 231.685 179.691 232.356C180.637 233.057 181.457 232.113 182.263 231.545C188.219 227.383 191.212 221.79 191.072 214.433C190.931 207.077 191.013 199.713 191.116 192.356C191.153 190.054 191.619 187.752 191.826 185.45C191.885 184.675 192.084 183.635 190.754 183.775Z"
									[attr.fill]="paintDobras('supraIliaca')" />
							</g>
							<g id="axiliarMedia" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true"
								position="top" (mouseover)="tooltipParte('axiliarMedia')">
								<path id="axiliar-media L"
									d="M117.481 163.483C111.517 153.551 109.921 142.417 107.512 131.511C107.785 131.371 108.059 131.238 108.339 131.098C110.734 133.762 113.113 136.44 115.537 139.074C117.932 141.686 118.515 144.535 117.133 147.877C115.626 151.515 117.03 155.145 117.569 158.731C117.806 160.318 117.932 161.845 117.481 163.483Z"
									[attr.fill]="paintDobras('axiliarMedia')" />
								<path id="axiliar-media R"
									d="M198.177 163.483C204.141 153.551 205.737 142.417 208.146 131.511C207.873 131.371 207.599 131.238 207.319 131.098C204.924 133.762 202.545 136.44 200.121 139.074C197.726 141.686 197.143 144.535 198.525 147.877C200.032 151.515 198.628 155.145 198.089 158.731C197.852 160.318 197.726 161.845 198.177 163.483Z"
									[attr.fill]="paintDobras('axiliarMedia')" />
							</g>
							<g id="mao" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('mao')">
								<path id="Vector_7" opacity="0.1"
									d="M40.3973 229.752C40.2788 229.855 40.1529 229.951 40.0344 230.055C40.1381 229.937 40.2418 229.826 40.3529 229.707L40.3973 229.752Z"
									[attr.fill]="paintDobras('mao')" />
								<path id="Vector_47"
									d="M24.0629 257.075C20.4566 257.119 17.4636 255.245 14.5963 253.511C12.7415 252.389 12.2537 249.954 13.5839 247.918C16.0522 244.147 18.5426 240.34 22.3632 237.735C25.5409 235.566 30.3221 236.555 33.5885 239.971C36.4484 242.959 36.7588 246.627 34.4457 249.984C30.9281 255.083 27.9869 257.09 24.0629 257.075Z"
									[attr.fill]="paintDobras('mao')" />
								<path id="Vector_48"
									d="M20.3608 234.142C19.8952 235.345 18.8458 237.455 16.6658 238.569C15.8677 238.975 15.3799 239.012 12.9708 239.307C6.3568 240.111 4.64972 240.48 3.36387 239.307C3.2678 239.218 2.41795 238.422 2.62487 237.831C2.86874 237.138 4.43541 237.145 5.58085 237.093C8.32992 236.975 10.9977 236.105 13.7098 235.618C15.2986 235.33 17.5895 234.872 20.3608 234.142Z"
									[attr.fill]="paintDobras('mao')" />
								<path id="Vector_49"
									d="M1.70871 265.015C0.637169 264.955 0.51154 264.395 1.02145 263.561C3.1128 260.108 5.30023 256.713 7.97539 253.673C8.61093 252.95 9.50511 252.08 10.0667 253.784C10.3919 254.802 2.86893 264.867 1.70871 265.015Z"
									[attr.fill]="paintDobras('mao')" />
								<path id="Vector_50"
									d="M7.83508 269.759C6.58618 269.567 6.50489 268.992 7.18476 268.217C9.99294 265 12.9046 261.856 16.3409 259.119C17.1538 258.47 18.2992 257.702 18.7648 259.48C19.0456 260.55 9.20961 269.759 7.83508 269.759Z"
									[attr.fill]="paintDobras('mao')" />
								<path id="Vector_51"
									d="M13.4286 256.684C12.963 256.153 12.187 256.175 11.788 256.603C10.3987 258.123 8.95025 259.598 7.57572 261.141C5.63955 263.148 3.85119 265.273 2.10716 267.427C1.43467 268.254 1.51596 268.866 2.75008 269.051C4.10244 269.036 13.8128 259.244 13.5911 258.071C13.8128 257.628 13.835 257.149 13.4286 256.684Z"
									[attr.fill]="paintDobras('mao')" />
								<path id="Vector_52"
									d="M17.8637 266.579C16.6961 267.45 16.1271 267.804 15.6837 267.568C15.3438 267.383 15.1812 266.911 15.159 266.542C15.1073 265.716 15.7576 265.081 16.5483 264.321C20.0659 260.949 20.4872 260.772 21.0488 260.742C21.507 260.713 22.3347 260.764 23.3619 261.436C22.3199 262.698 21.3592 263.672 20.6128 264.38C19.8738 265.066 19.2087 265.568 17.8637 266.579Z"
									[attr.fill]="paintDobras('mao')" />
								<path id="Vector_53"
									d="M291.594 257.075C295.201 257.119 298.193 255.245 301.061 253.511C302.916 252.389 303.403 249.954 302.073 247.918C299.605 244.147 297.115 240.34 293.294 237.735C290.116 235.566 285.335 236.555 282.069 239.971C279.209 242.959 278.898 246.627 281.211 249.984C284.729 255.083 287.67 257.09 291.594 257.075Z"
									[attr.fill]="paintDobras('mao')" />
								<path id="Vector_54"
									d="M295.298 234.142C295.764 235.345 296.813 237.455 298.993 238.569C299.791 238.975 300.279 239.012 302.688 239.307C309.302 240.111 311.009 240.48 312.295 239.307C312.391 239.218 313.241 238.422 313.034 237.831C312.79 237.138 311.223 237.145 310.078 237.093C307.329 236.975 304.661 236.105 301.949 235.618C300.36 235.33 298.077 234.872 295.298 234.142Z"
									[attr.fill]="paintDobras('mao')" />
								<path id="Vector_55"
									d="M313.956 265.015C315.028 264.955 315.154 264.395 314.644 263.561C312.552 260.108 310.365 256.713 307.69 253.673C307.054 252.95 306.16 252.08 305.598 253.784C305.266 254.802 312.789 264.867 313.956 265.015Z"
									[attr.fill]="paintDobras('mao')" />
								<path id="Vector_56"
									d="M307.823 269.759C309.072 269.567 309.153 268.992 308.473 268.217C305.665 265 302.753 261.856 299.317 259.119C298.504 258.47 297.359 257.702 296.893 259.48C296.612 260.55 306.456 269.759 307.823 269.759Z"
									[attr.fill]="paintDobras('mao')" />
								<path id="Vector_57"
									d="M302.236 256.684C302.702 256.153 303.478 256.175 303.877 256.603C305.266 258.123 306.715 259.598 308.089 261.141C310.025 263.148 311.814 265.273 313.558 267.427C314.23 268.254 314.149 268.866 312.915 269.051C311.562 269.036 301.852 259.244 302.074 258.071C301.845 257.628 301.822 257.149 302.236 256.684Z"
									[attr.fill]="paintDobras('mao')" />
								<path id="Vector_58"
									d="M297.795 266.579C298.962 267.45 299.531 267.804 299.975 267.568C300.315 267.383 300.477 266.911 300.499 266.542C300.551 265.716 299.901 265.081 299.11 264.321C295.592 260.949 295.171 260.772 294.609 260.742C294.151 260.713 293.324 260.764 292.296 261.436C293.338 262.698 294.299 263.672 295.045 264.38C295.784 265.066 296.457 265.568 297.795 266.579Z"
									[attr.fill]="paintDobras('mao')" />
							</g>
							<g id="pescoco" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('pescoco')">
								<path id="Vector_59"
									d="M155.583 94.6322C151.119 91.5331 148.392 87.128 146.907 82.1105C146.161 79.5944 145.089 77.0708 145.237 74.2595C145.363 71.9057 145.991 71.7508 147.786 73.0199C149.301 74.0972 151.082 74.99 151.119 77.0856C151.215 82.8189 153.299 88.0209 155.265 93.2598C155.428 93.6951 155.479 94.1747 155.583 94.6322Z"
									[attr.fill]="paintDobras('pescoco')" />
								<path id="Vector_60"
									d="M157.815 74.7613C157.135 74.7687 156.448 74.776 155.768 74.8129C154.261 74.9015 153.861 75.5287 153.647 75.8829C153.388 76.3034 153.381 76.7462 153.381 76.9675C153.418 82.0958 154.859 85.822 154.859 85.822C155.45 87.342 156.374 89.4081 157.815 91.725V74.7613Z"
									[attr.fill]="paintDobras('pescoco')" />
								<path id="Vector_61"
									d="M160.076 94.6322C164.54 91.5331 167.266 87.128 168.752 82.1105C169.498 79.5943 170.57 77.0708 170.422 74.2595C170.296 71.9057 169.668 71.7507 167.872 73.0199C166.357 74.0972 164.577 74.99 164.54 77.0856C164.444 82.8188 162.36 88.0208 160.394 93.2597C160.231 93.6951 160.18 94.1747 160.076 94.6322Z"
									[attr.fill]="paintDobras('pescoco')" />
								<path id="Vector_62"
									d="M157.845 74.7613C158.525 74.7687 159.212 74.776 159.892 74.8129C161.4 74.9015 161.799 75.5287 162.013 75.8829C162.272 76.3034 162.279 76.7462 162.279 76.9675C162.242 82.0958 160.801 85.822 160.801 85.822C160.21 87.342 159.286 89.4081 157.845 91.725V74.7613Z"
									[attr.fill]="paintDobras('pescoco')" />
							</g>
						</g>
					</svg>
				</div>
				<div class="col-6 traseiro">
					<svg viewBox="0 0 315 516" fill="none" xmlns="http://www.w3.org/2000/svg">
						<g id="Data Muscle Chart">
							<path id="Body-back"
								d="M314.699 261.221C314.581 260.787 314.442 259.72 312.979 257.763C311.715 256.063 310.414 254.392 309.091 252.744C306.556 249.579 303.903 246.503 301.874 242.963C301.58 242.455 301.264 241.948 301.066 241.403C300.786 240.63 301.073 240.277 301.867 240.299C302.161 240.307 302.447 240.41 302.734 240.424C305.079 240.586 307.416 240.778 309.768 240.873C311.164 240.932 312.443 240.513 313.413 239.423C314.398 238.32 314.346 236.038 312.186 235.84C311.017 235.737 309.848 235.604 308.687 235.427C304.961 234.853 301.36 233.757 297.692 232.933C293.959 232.094 290.21 231.35 286.352 232.05C284.816 232.329 283.618 231.785 282.545 230.71C282.199 230.364 281.869 230.018 281.523 229.68H281.494C281.391 229.584 281.288 229.496 281.178 229.393C271.16 219.311 258.718 194.93 258.718 194.93C257.755 193.031 256.314 190.286 254.44 187.041C251.346 181.676 249.795 178.99 247.693 176.694C243.945 172.595 242.35 173.022 240.204 169.651C238.786 167.429 237.955 165.994 237.573 160.003C236.926 149.774 233.854 140.428 228.203 131.987C224.396 126.298 224.778 119.829 224.329 113.604C223.492 101.969 219.773 96.7143 209.057 92.446C204.493 90.6283 199.775 89.6569 195.115 88.3249C193.381 87.8318 191.653 87.0296 190.147 86.0435C185.641 83.0925 180.82 80.87 175.683 79.3246C172.405 78.3384 173.089 75.5567 172.986 73.3121C172.736 67.9988 174.301 63.0314 176.477 58.2479C176.778 57.5782 176.925 56.5258 177.425 56.3124C183.084 53.8692 182.062 48.2173 183.341 43.7871C184.039 41.3807 184.679 38.4959 181.864 36.4279C180.975 35.773 181.504 34.3379 181.474 33.2488C181.32 27.906 181.732 22.6148 180.269 17.2499C177.983 8.83837 170.391 1.14067 161.77 0.287008C160.411 0.154543 159.051 0.0662326 157.699 0.0294367V0C157.632 0 157.566 0.00735918 157.5 0.00735918C157.434 0.00735918 157.368 0 157.302 0V0.0147184C155.949 0.0515142 154.59 0.139824 153.23 0.27229C144.602 1.12595 137.017 8.8163 134.731 17.2352C133.269 22.6074 133.688 27.8913 133.526 33.234C133.497 34.3158 134.018 35.7582 133.136 36.4132C130.322 38.4811 130.961 41.3659 131.659 43.7724C132.938 48.2026 131.917 53.8545 137.576 56.2977C138.075 56.5111 138.222 57.5708 138.524 58.2332C140.699 63.024 142.265 67.9841 142.015 73.2974C141.904 75.5346 142.595 78.3237 139.317 79.3099C134.18 80.8479 129.359 83.0704 124.854 86.0288C123.34 87.0223 121.62 87.8171 119.885 88.3101C115.226 89.6422 110.507 90.6136 105.943 92.4313C95.2277 96.707 91.5088 101.954 90.671 113.589C90.2227 119.807 90.6048 126.284 86.7978 131.972C81.146 140.413 78.0739 149.759 77.4271 159.989C77.0449 165.979 76.2144 167.414 74.796 169.636C72.6499 173.007 71.0551 172.58 67.3068 176.679C65.2122 178.975 63.6615 181.661 60.56 187.026C58.6858 190.272 57.2453 193.017 56.2825 194.915C56.2825 194.915 43.8398 219.296 33.8224 229.378C33.7195 229.481 33.6166 229.57 33.5064 229.665H33.477C33.1389 230.011 32.8008 230.357 32.4554 230.696C31.3824 231.77 30.1918 232.315 28.6484 232.035C24.7972 231.336 21.0416 232.086 17.3081 232.918C13.6406 233.742 10.0394 234.839 6.31315 235.413C5.15193 235.589 3.98337 235.722 2.8148 235.825C0.654037 236.024 0.602581 238.305 1.58742 239.409C2.55755 240.498 3.83636 240.917 5.23277 240.859C7.57727 240.763 9.92177 240.572 12.2663 240.41C12.5603 240.388 12.8469 240.292 13.1335 240.285C13.9273 240.262 14.2066 240.616 13.9346 241.388C13.7362 241.933 13.4202 242.441 13.1262 242.949C11.0977 246.488 8.44452 249.564 5.90894 252.729C4.58602 254.377 3.28515 256.055 2.02104 257.748C0.56583 259.713 0.418843 260.772 0.30125 261.207C-0.176468 262.995 -7.00905e-05 264.482 0.242464 266.63C0.264513 266.814 0.389452 267.491 0.676083 268.338C1.1391 269.699 1.71972 270.163 3.13818 270.148C4.60808 270.133 6.07798 270.119 7.54053 270.229C8.64296 270.31 9.63515 270.03 10.4877 269.302C11.6269 268.338 12.9351 267.955 14.4197 268.11C16.0586 268.279 17.5432 267.859 18.9617 267.028C25.6792 263.076 31.4926 258.108 36.2036 251.875C37.0268 250.786 37.6883 249.601 38.1366 248.321C38.6216 246.945 39.4154 245.811 40.4811 244.855C43.0755 242.551 45.6625 240.24 48.2716 237.959C51.4466 235.185 54.6289 232.425 57.8112 229.658H57.3629C62.7354 225.007 68.1374 220.385 73.5907 215.83C83.1304 207.853 91.3839 198.727 95.7789 187.092C98.9613 178.659 101.879 170.291 107.082 162.829C109.831 158.877 109.603 158.701 112.073 162.984C114.895 167.878 118.93 172.477 119.209 178.225C119.606 186.533 120.407 194.79 120.958 203.077C121.583 212.437 119.525 221.445 117.379 230.482C115.042 240.329 113.859 250.381 112.499 260.412C110.691 273.717 109.361 287.067 108.611 300.453C108.185 308.011 108.31 315.606 109.206 323.178C110.522 334.313 111.94 345.432 115.975 355.971C118.974 363.801 119.092 371.948 117.46 379.748C113.947 396.483 114.3 397.609 115.953 408.39C116.806 413.954 118.092 417.081 120.201 429.828C120.878 433.905 121.333 437.12 121.546 438.673C122.303 444.185 123.575 453.406 123.876 462.208C124.111 469.037 124.685 485.323 119.15 496.87C117.585 500.137 113.058 508.1 109.824 509.233C107.773 509.947 106.664 511.139 107.075 513.303C107.516 515.599 109.317 515.908 111.294 515.9C119.003 515.886 126.728 515.658 134.423 515.989C139.288 516.202 140.339 513.347 140.809 509.461C141.258 505.789 140.001 502.418 139.192 499.041C137.127 490.43 139.281 481.783 138.524 473.188C137.458 461.097 141.103 449.771 144.293 438.408C147.306 427.679 149.621 416.816 149.408 405.667C149.269 398.499 147.894 391.368 146.336 384.333C145.587 380.955 145.344 377.401 146.292 374.141C149.452 363.278 148.923 357.575 150.452 347.493C151.657 339.567 151.547 334.879 151.804 327.542C152.201 316.202 153.495 298.047 157.485 268.507C161.476 298.047 162.77 316.202 163.167 327.542C163.424 334.879 163.321 339.567 164.519 347.493C166.048 357.568 165.511 363.278 168.679 374.141C169.627 377.401 169.384 380.955 168.635 384.333C167.077 391.376 165.702 398.499 165.563 405.667C165.349 416.816 167.672 427.679 170.678 438.408C173.868 449.771 177.513 461.104 176.447 473.188C175.69 481.783 177.844 490.43 175.778 499.041C174.97 502.418 173.713 505.789 174.162 509.461C174.639 513.347 175.683 516.202 180.548 515.989C188.243 515.658 195.968 515.886 203.677 515.9C205.654 515.908 207.455 515.599 207.896 513.303C208.307 511.139 207.198 509.947 205.147 509.233C201.913 508.1 197.386 500.137 195.821 496.87C190.286 485.33 190.852 469.045 191.095 462.208C191.403 453.406 192.668 444.185 193.425 438.673C193.638 437.12 194.093 433.905 194.77 429.828C196.879 417.081 198.165 413.954 199.018 408.39C200.671 397.609 201.017 396.476 197.511 379.748C195.872 371.948 195.997 363.801 198.996 355.971C203.03 345.432 204.449 334.313 205.764 323.178C206.654 315.613 206.779 308.011 206.36 300.453C205.61 287.059 204.28 273.717 202.472 260.412C201.112 250.381 199.929 240.329 197.592 230.482C195.446 221.445 193.388 212.437 194.013 203.077C194.564 194.79 195.365 186.533 195.762 178.225C196.041 172.477 200.083 167.87 202.898 162.984C205.368 158.701 205.132 158.885 207.888 162.829C213.092 170.291 216.01 178.659 219.192 187.092C223.58 198.727 231.841 207.853 241.38 215.83C246.834 220.393 252.228 225.007 257.608 229.658H257.16C260.342 232.425 263.524 235.185 266.699 237.959C269.308 240.24 271.903 242.551 274.49 244.855C275.563 245.811 276.357 246.945 276.834 248.321C277.283 249.601 277.944 250.786 278.767 251.875C283.478 258.108 289.292 263.083 296.009 267.028C297.428 267.867 298.92 268.286 300.551 268.11C302.036 267.955 303.344 268.338 304.483 269.302C305.336 270.023 306.328 270.31 307.43 270.229C308.9 270.119 310.363 270.133 311.833 270.148C313.251 270.163 313.832 269.699 314.295 268.338C314.581 267.491 314.706 266.814 314.728 266.63C315.008 264.496 315.177 263.01 314.699 261.221Z"
								fill="#DCDDDF" />
							<g id="antebraco" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('antebraco')">
								<path id="Vector_47"
									d="M251.369 206.896C250.229 203.827 247.378 202.127 245.518 199.618C241.513 194.209 238.544 188.262 235.707 182.199C233.105 176.642 227.343 175.686 222.279 173.89C221.169 173.5 220.405 174.258 220.824 175.597C225.006 188.991 232.752 199.309 246.114 204.659C247.892 205.373 249.23 207.146 251.405 206.866L251.369 206.896Z"
									[attr.fill]="paintDobras('antebraco')" />
								<path id="Vector_48"
									d="M237.374 172.337C238.05 180.896 241.203 188.527 246.215 195.394C252.72 204.306 259.959 212.57 268.543 219.672C267.448 216.272 264.597 213.983 262.958 210.907C259.32 204.063 255.976 197.101 252.632 190.11C249.199 182.942 244.804 176.245 237.462 172.242L237.374 172.337Z"
									[attr.fill]="paintDobras('antebraco')" />
								<path id="Vector_49"
									d="M256.013 223.255C256.189 223.226 256.417 223.204 256.733 223.358C257.277 223.631 258.923 224.794 261.136 226.471C265.296 229.445 269.823 231.888 273.696 235.258C274.6 236.046 276.202 236.612 275.298 237.996C274.571 239.107 272.902 238.776 271.976 238.025C267.853 234.67 263.576 231.476 259.96 227.553C257.748 225.794 256.197 224.58 255.785 224.3C255.719 224.256 255.565 224.102 255.477 223.896C255.425 223.778 255.793 223.292 256.013 223.255Z"
									[attr.fill]="paintDobras('antebraco')" />
								<path id="Vector_50"
									d="M224.211 189.197C224.836 189.764 225.629 190.22 226.056 190.912C230.024 197.219 235.206 202.237 241.762 205.77C250.647 210.561 258.989 216.154 266.39 223.012C267.83 224.344 269.756 225.264 271.152 226.692C271.182 226.707 271.211 226.714 271.24 226.729C274.136 229.687 276.245 231.866 276.554 232.211C276.606 232.27 276.694 232.417 276.716 232.594C276.731 232.712 276.429 233.014 276.304 232.992C276.113 232.962 275.952 232.852 275.915 232.815C275.386 232.322 270.836 229.525 268.11 227.443C262.215 223.528 256.387 219.48 250.221 216.043C243.672 212.393 237.014 209.001 232.126 203.128C228.679 198.985 224.718 195.07 224.167 189.234L224.211 189.197Z"
									[attr.fill]="paintDobras('antebraco')" />
								<path id="Vector"
									d="M63.6827 206.896C64.8219 203.827 67.6735 202.127 69.533 199.618C73.5385 194.209 76.5076 188.262 79.3446 182.198C81.9463 176.642 87.7083 175.686 92.7721 173.89C93.8819 173.5 94.6463 174.258 94.2273 175.597C90.0455 188.991 82.2991 199.309 68.9377 204.659C67.1591 205.373 65.8215 207.146 63.646 206.866L63.6827 206.896Z"
									[attr.fill]="paintDobras('antebraco')" />
								<path id="Vector_2"
									d="M77.6681 172.337C76.992 180.896 73.839 188.527 68.8266 195.393C62.3223 204.305 55.083 212.57 46.4988 219.671C47.5939 216.271 50.4455 213.983 52.0844 210.907C55.7225 204.063 59.0665 197.101 62.4105 190.11C65.8427 182.942 70.2378 176.245 77.5799 172.242L77.6681 172.337Z"
									[attr.fill]="paintDobras('antebraco')" />
								<path id="Vector_3"
									d="M59.0381 223.255C58.8617 223.226 58.6339 223.204 58.3179 223.358C57.774 223.631 56.1277 224.793 53.9155 226.471C49.7557 229.444 45.2284 231.888 41.3552 235.258C40.4512 236.046 38.849 236.612 39.753 237.996C40.4806 239.107 42.149 238.776 43.075 238.025C47.1981 234.669 51.4755 231.476 55.0914 227.553C57.3036 225.794 58.8544 224.58 59.266 224.3C59.3321 224.256 59.4865 224.102 59.5747 223.896C59.6261 223.778 59.2513 223.292 59.0381 223.255Z"
									[attr.fill]="paintDobras('antebraco')" />
								<path id="Vector_4"
									d="M90.8326 189.197C90.2079 189.764 89.4142 190.22 88.9879 190.912C85.0192 197.219 79.8378 202.237 73.282 205.77C64.3964 210.561 56.0547 216.154 48.6537 223.012C47.2132 224.344 45.2877 225.264 43.8913 226.692C43.8619 226.707 43.8325 226.714 43.8031 226.729C40.9074 229.687 38.7981 231.866 38.4894 232.211C38.4379 232.27 38.3497 232.417 38.3277 232.594C38.313 232.712 38.6143 233.014 38.7392 232.992C38.9303 232.962 39.092 232.852 39.1288 232.815C39.658 232.322 44.2073 229.525 46.934 227.443C52.8283 223.528 58.6564 219.48 64.8227 216.043C71.3711 212.393 78.0298 209.001 82.9172 203.128C86.3642 198.985 90.3255 195.07 90.8767 189.234L90.8326 189.197Z"
									[attr.fill]="paintDobras('antebraco')" />
							</g>
							<g id="maos" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('maos')">
								<path id="Vector_5"
									d="M23.9723 256.46C20.3857 256.504 17.4091 254.635 14.5575 252.905C12.7128 251.787 12.2277 249.358 13.5506 247.327C16.0054 243.567 18.4822 239.769 22.2819 237.172C25.4422 235.008 30.1973 235.994 33.4457 239.401C36.29 242.382 36.5987 246.039 34.2983 249.388C30.7926 254.473 27.8675 256.475 23.9723 256.46Z"
									[attr.fill]="paintDobras('maos')" />
								<path id="Vector_6"
									d="M20.2828 233.58C19.8198 234.78 18.7762 236.885 16.6081 237.996C15.8143 238.401 15.3293 238.437 12.9333 238.732C6.35549 239.534 4.65775 239.902 3.37894 238.732C3.28339 238.643 2.4382 237.849 2.64398 237.26C2.88652 236.568 4.44462 236.576 5.58379 236.524C8.31781 236.406 10.971 235.538 13.6683 235.052C15.2484 234.773 17.5268 234.316 20.2828 233.58Z"
									[attr.fill]="paintDobras('maos')" />
								<path id="Vector_7"
									d="M1.73315 264.371C0.66747 264.312 0.542534 263.753 1.04965 262.921C3.12956 259.477 5.305 256.092 7.96553 253.06C8.59759 252.339 9.48689 251.47 10.0455 253.17C10.3688 254.186 2.88703 264.231 1.73315 264.371Z"
									[attr.fill]="paintDobras('maos')" />
								<path id="Vector_8"
									d="M7.82601 269.11C6.58394 268.919 6.50312 268.345 7.17927 267.572C9.97209 264.364 12.8678 261.229 16.2853 258.499C17.0938 257.851 18.2329 257.086 18.696 258.859C18.9826 259.926 9.19302 269.103 7.82601 269.11Z"
									[attr.fill]="paintDobras('maos')" />
								<path id="Vector_9"
									d="M13.3895 256.062C12.9265 255.533 12.1548 255.555 11.7579 255.982C10.3762 257.498 8.93568 258.969 7.56866 260.507C5.64309 262.509 3.86449 264.629 2.13 266.777C1.46119 267.602 1.54206 268.212 2.76943 268.396C4.11439 268.382 13.7716 258.616 13.5512 257.446C13.7716 257.004 13.7937 256.526 13.3895 256.062Z"
									[attr.fill]="paintDobras('maos')" />
								<path id="Vector_10"
									d="M17.8061 265.931C16.6449 266.8 16.0789 267.153 15.638 266.917C15.2999 266.733 15.1382 266.262 15.1162 265.894C15.0647 265.07 15.7115 264.437 16.4979 263.679C19.9962 260.316 20.4152 260.14 20.9737 260.11C21.4294 260.081 22.2525 260.132 23.2741 260.802C22.2378 262.06 21.2824 263.032 20.5401 263.738C19.8052 264.43 19.1363 264.93 17.8061 265.931Z"
									[attr.fill]="paintDobras('maos')" />
								<path id="Vector_11"
									d="M291.077 256.46C294.664 256.504 297.64 254.635 300.492 252.906C302.337 251.787 302.822 249.359 301.499 247.327C299.044 243.567 296.567 239.77 292.768 237.172C289.607 235.008 284.852 235.994 281.604 239.402C278.759 242.382 278.451 246.04 280.751 249.388C284.25 254.473 287.175 256.475 291.077 256.46Z"
									[attr.fill]="paintDobras('maos')" />
								<path id="Vector_12"
									d="M294.76 233.58C295.223 234.78 296.266 236.885 298.434 237.996C299.228 238.401 299.713 238.437 302.109 238.732C308.687 239.534 310.385 239.902 311.663 238.732C311.759 238.643 312.604 237.849 312.398 237.26C312.156 236.568 310.598 236.576 309.459 236.524C306.725 236.406 304.071 235.538 301.374 235.052C299.794 234.773 297.523 234.316 294.76 233.58Z"
									[attr.fill]="paintDobras('maos')" />
								<path id="Vector_13"
									d="M313.317 264.371C314.382 264.312 314.507 263.753 314 262.921C311.92 259.477 309.745 256.092 307.084 253.06C306.452 252.339 305.563 251.471 305.004 253.171C304.674 254.186 312.155 264.231 313.317 264.371Z"
									[attr.fill]="paintDobras('maos')" />
								<path id="Vector_14"
									d="M307.217 269.111C308.459 268.919 308.539 268.345 307.863 267.572C305.071 264.364 302.175 261.229 298.757 258.499C297.949 257.851 296.81 257.086 296.347 258.859C296.067 259.926 305.857 269.103 307.217 269.111Z"
									[attr.fill]="paintDobras('maos')" />
								<path id="Vector_15"
									d="M301.66 256.063C302.123 255.533 302.895 255.555 303.292 255.982C304.674 257.498 306.114 258.969 307.481 260.508C309.407 262.509 311.185 264.629 312.92 266.778C313.588 267.602 313.508 268.213 312.28 268.397C310.935 268.382 301.278 258.616 301.499 257.446C301.271 257.005 301.249 256.526 301.66 256.063Z"
									[attr.fill]="paintDobras('maos')" />
								<path id="Vector_16"
									d="M297.243 265.931C298.404 266.8 298.97 267.153 299.411 266.918C299.749 266.734 299.911 266.263 299.933 265.895C299.985 265.07 299.338 264.438 298.551 263.68C295.053 260.316 294.634 260.14 294.076 260.11C293.62 260.081 292.797 260.132 291.775 260.802C292.811 262.061 293.767 263.032 294.509 263.738C295.244 264.43 295.913 264.931 297.243 265.931Z"
									[attr.fill]="paintDobras('maos')" />
							</g>
							<g id="dorsal" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('dorsal')">
								<path id="Vector_17"
									d="M132.902 204.696C132.438 200.273 132.872 196.402 132.034 192.582C130.322 184.73 127.397 177.577 121.671 171.763C115.579 165.574 112.977 157.751 110.405 149.642C108.391 143.276 108.347 136.704 106.649 130.383C106.583 130.147 106.451 129.912 106.458 129.676C106.524 127.527 105.253 124.613 107.002 123.472C108.464 122.516 111.11 123.774 113.212 124.385C119.239 126.151 125.317 128.035 131.623 128.072C137.679 128.101 141.567 131.096 143.963 136.115C146.645 141.73 149.364 147.184 153.679 151.827C156.008 154.337 155.046 157.972 154.428 160.975C151.951 173.022 146.748 183.913 140.133 194.246C137.951 197.66 136.15 201.288 132.902 204.696Z"
									[attr.fill]="paintDobras('dorsal')" />
								<path id="Vector_18"
									d="M182.143 204.696C182.606 200.273 182.172 196.402 183.01 192.582C184.723 184.73 187.648 177.577 193.373 171.763C199.466 165.574 202.068 157.751 204.64 149.642C206.654 143.276 206.698 136.704 208.396 130.383C208.462 130.147 208.594 129.912 208.587 129.676C208.52 127.527 209.792 124.613 208.043 123.472C206.58 122.516 203.934 123.774 201.832 124.385C195.806 126.151 189.728 128.035 183.422 128.072C177.366 128.101 173.478 131.097 171.082 136.115C168.399 141.731 165.68 147.184 161.366 151.827C159.036 154.337 159.999 157.972 160.616 160.975C163.093 173.022 168.297 183.913 174.911 194.246C177.094 197.66 178.902 201.288 182.143 204.696Z"
									[attr.fill]="paintDobras('dorsal')" />
							</g>
							<g id="nadegas" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('nadegas')">
								<path id="Vector_19"
									d="M117.967 262.958C117.967 250.072 119.65 238.761 124.03 228.017C125.603 224.153 127.572 220.4 130.314 217.081C131.696 215.411 131.923 215.344 132.908 217.228C135.716 222.615 138.509 228.024 142.374 232.764C144.293 235.118 146.306 237.392 148.254 239.725C154.685 247.423 157.044 256.011 153.23 265.578C150.841 271.568 146.174 274.865 139.604 275.373C133.254 275.866 127.065 277.058 121.847 281.054C120.377 282.18 119.635 282.107 119.525 280.238C119.15 274.034 117.349 267.94 117.967 262.958Z"
									[attr.fill]="paintDobras('nadegas')" />
								<path id="Vector_20"
									d="M197.077 262.958C197.077 250.072 195.394 238.761 191.014 228.017C189.441 224.153 187.472 220.4 184.73 217.081C183.349 215.411 183.121 215.344 182.136 217.228C179.328 222.615 176.536 228.024 172.67 232.764C170.752 235.118 168.738 237.392 166.79 239.725C160.359 247.423 158 256.011 161.815 265.578C164.203 271.568 168.87 274.865 175.44 275.373C181.79 275.866 187.979 277.058 193.197 281.054C194.667 282.18 195.409 282.107 195.519 280.238C195.902 274.034 197.702 267.94 197.077 262.958Z"
									[attr.fill]="paintDobras('nadegas')" />
							</g>
							<g id="lombar" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('lombar')">
								<path id="Vector_21"
									d="M154.694 176.613C154.694 197.388 154.694 218.17 154.694 239.541C151.35 237.407 149.549 234.346 147.403 231.608C142.427 225.257 138.892 218.067 135.254 210.936C134.688 209.832 134.615 208.522 135.519 207.47C143.456 198.212 149.953 188.078 154.694 176.613Z"
									[attr.fill]="paintDobras('lombar')" />
								<path id="Vector_22"
									d="M160.359 176.613C160.359 197.388 160.359 218.17 160.359 239.541C163.703 237.407 165.503 234.346 167.649 231.608C172.625 225.257 176.16 218.067 179.798 210.936C180.364 209.832 180.438 208.522 179.534 207.47C171.589 198.212 165.092 188.078 160.359 176.613Z"
									[attr.fill]="paintDobras('lombar')" />
							</g>
							<g id="ombro" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('ombro')">
								<path id="Vector_23"
									d="M93.2354 117.305C93.8968 111.028 94.3893 103.382 101.401 98.0832C105.825 94.7421 111.08 94.3447 116.063 92.9832C118.518 92.3136 118.51 94.4845 118.481 96.0079C118.312 104.397 115.041 111.33 108.522 116.658C105.597 119.049 102.261 120.772 98.8798 122.413C93.9556 124.797 93.1325 124.26 93.2354 117.305Z"
									[attr.fill]="paintDobras('ombro')" />
								<path id="Vector_24"
									d="M221.817 117.305C221.155 111.028 220.663 103.382 213.651 98.0832C209.227 94.7421 203.972 94.3447 198.989 92.9832C196.534 92.3136 196.542 94.4845 196.571 96.0079C196.74 104.397 200.01 111.33 206.529 116.658C209.455 119.049 212.791 120.772 216.172 122.413C221.089 124.797 221.912 124.26 221.817 117.305Z"
									[attr.fill]="paintDobras('ombro')" />
							</g>
							<g id="quadril" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('quadril')">
								<path id="Vector_25"
									d="M121.539 176.863C130.285 187.88 133.431 211.488 123.009 218.729C125.229 204.534 122.406 191.155 121.539 176.863Z"
									[attr.fill]="paintDobras('quadril')" />
								<path id="Vector_26"
									d="M193.505 176.863C184.759 187.88 181.614 211.488 192.035 218.729C189.823 204.534 192.645 191.155 193.505 176.863Z"
									[attr.fill]="paintDobras('quadril')" />
							</g>
							<g id="coxas" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('coxas')">
								<path id="Vector_27"
									d="M132.241 281.702C134.931 286.044 135.96 290.224 136.959 294.212C139.061 302.58 141.406 310.91 142.677 319.477C144.661 332.885 146.381 346.293 143.985 359.864C143.478 362.734 143.809 365.736 142.501 368.481C142.192 369.122 142.229 370.034 141.303 370.13C140.399 370.225 140.09 369.431 139.759 368.798C135.489 360.636 132.527 352.328 132.542 342.739C132.564 326.122 130.94 309.52 131.3 292.873C131.381 289.414 131.873 285.963 132.241 281.702Z"
									[attr.fill]="paintDobras('coxas')" />
								<path id="Vector_28"
									d="M182.805 281.702C180.116 286.044 179.087 290.224 178.087 294.212C175.985 302.58 173.641 310.91 172.369 319.477C170.385 332.885 168.665 346.293 171.061 359.864C171.568 362.734 171.237 365.736 172.546 368.481C172.854 369.122 172.817 370.034 173.744 370.13C174.648 370.225 174.956 369.431 175.287 368.798C179.557 360.636 182.519 352.328 182.504 342.739C182.482 326.122 184.106 309.52 183.746 292.873C183.673 289.414 183.18 285.963 182.805 281.702Z"
									[attr.fill]="paintDobras('coxas')" />
								<path id="Vector_29"
									d="M128.594 280.767C129.909 283.858 128.329 286.669 128.344 289.525C128.395 297.311 128.175 305.104 128.293 312.89C128.484 325.114 127.3 337.249 126.022 349.37C125.691 352.483 124.015 355.36 123.699 358.547C123.64 359.15 123.133 359.658 122.391 359.518C121.781 359.4 121.832 358.826 121.737 358.37C119.436 347.294 117.452 336.197 116.357 324.9C115.475 315.797 115.644 306.804 117.467 297.981C118.885 291.122 121.729 284.594 128.594 280.767Z"
									[attr.fill]="paintDobras('coxas')" />
								<path id="Vector_30"
									d="M186.45 280.767C185.135 283.858 186.715 286.669 186.7 289.525C186.648 297.311 186.869 305.104 186.751 312.89C186.56 325.114 187.744 337.249 189.022 349.37C189.353 352.483 191.029 355.36 191.345 358.547C191.404 359.15 191.911 359.658 192.653 359.518C193.263 359.4 193.212 358.826 193.307 358.37C195.608 347.294 197.592 336.197 198.687 324.9C199.569 315.797 199.4 306.804 197.577 297.981C196.159 291.122 193.315 284.594 186.45 280.767Z"
									[attr.fill]="paintDobras('coxas')" />
								<path id="Vector_31"
									d="M148.321 338.551C149.1 330.044 146.924 321.824 145.815 313.523C144.903 306.672 142.853 299.975 140.663 293.373C139.362 289.458 138.347 285.448 136.172 281.878C135.672 281.062 135.547 279.899 136.723 279.891C142.551 279.855 147.167 276.749 152.326 273.533C150.636 284.888 149.497 295.633 148.843 306.429C148.196 317.129 151.084 327.859 148.321 338.551Z"
									[attr.fill]="paintDobras('coxas')" />
								<path id="Vector_32"
									d="M166.725 338.551C165.946 330.044 168.121 321.824 169.231 313.523C170.142 306.672 172.193 299.975 174.383 293.373C175.684 289.458 176.698 285.448 178.874 281.878C179.374 281.062 179.498 279.899 178.323 279.891C172.494 279.855 167.879 276.749 162.719 273.533C164.41 284.888 165.549 295.633 166.203 306.429C166.85 317.129 163.969 327.859 166.725 338.551Z"
									[attr.fill]="paintDobras('coxas')" />
								<path id="Vector_33"
									d="M114.822 269.427C116.644 276.109 116.857 282.07 115.667 288.377C113.94 297.546 112.337 306.812 112.97 316.975C110.184 308.57 111.367 278.148 114.822 269.427Z"
									[attr.fill]="paintDobras('coxas')" />
								<path id="Vector_34"
									d="M200.23 269.427C198.408 276.109 198.195 282.07 199.385 288.377C201.112 297.546 202.715 306.812 202.082 316.975C204.868 308.57 203.677 278.148 200.23 269.427Z"
									[attr.fill]="paintDobras('coxas')" />
							</g>
							<g id="triceps" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('triceps')">
								<path id="triceps3 L"
									d="M90.5602 148.251C91.376 153.939 91.8904 159.128 91.6111 164.367C91.4201 168.098 90.1119 171.116 86.5694 172.963C83.534 174.545 82.1523 173.78 82.02 170.291C81.873 166.604 83.4973 163.337 84.9819 160.268C86.8193 156.486 88.5685 152.666 90.5602 148.251Z"
									[attr.fill]="paintDobras('triceps')" />
								<path id="triceps2 L"
									d="M91.3099 131.538C89.5093 143.18 85.2098 152.865 80.6457 162.91C78.3453 156.272 84.6218 137.337 91.3099 131.538Z"
									[attr.fill]="paintDobras('triceps')" />
								<path id="triceps1 L"
									d="M94.398 169.651C96.3529 162.726 94.4788 156.486 93.4425 150.105C91.8697 140.494 92.4577 131.288 100.99 124.532C101.725 123.951 102.137 122.78 103.335 123.281C104.357 123.708 104.055 124.767 104.011 125.562C103.548 134.195 105.973 142.496 106.966 150.959C107.987 159.665 102.96 167.833 94.398 169.651Z"
									[attr.fill]="paintDobras('triceps')" />
								<path id="triceps3 R"
									d="M224.492 148.251C223.676 153.939 223.161 159.127 223.441 164.367C223.632 168.098 224.94 171.116 228.482 172.963C231.518 174.545 232.9 173.78 233.032 170.291C233.179 166.604 231.555 163.337 230.07 160.268C228.233 156.485 226.476 152.666 224.492 148.251Z"
									[attr.fill]="paintDobras('triceps')" />
								<path id="triceps2 R"
									d="M223.734 131.538C225.534 143.18 229.834 152.865 234.398 162.91C236.698 156.272 230.429 137.337 223.734 131.538Z"
									[attr.fill]="paintDobras('triceps')" />
								<path id="triceps1 R"
									d="M220.647 169.651C218.692 162.726 220.566 156.486 221.603 150.105C223.175 140.494 222.588 131.288 214.055 124.532C213.32 123.951 212.908 122.78 211.71 123.281C210.689 123.708 210.99 124.767 211.034 125.562C211.497 134.195 209.072 142.496 208.08 150.959C207.065 159.665 212.085 167.833 220.647 169.651Z"
									[attr.fill]="paintDobras('triceps')" />
							</g>
							<g id="panturrilha" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true"
								position="top" (mouseover)="tooltipParte('panturrilha')">
								<path id="Vector_35"
									d="M133.739 364.051C140.383 372.249 141.669 381.264 143.97 390.073C146.351 399.176 145.859 408.221 145.947 417.339C145.984 421.475 144.256 425.184 142.97 428.966C142.397 430.652 141.03 432.168 138.825 431.984C136.561 431.8 135.319 430.343 134.871 428.289C134.298 425.677 133.504 423.013 133.563 420.393C133.872 406.565 132.659 392.774 132.821 378.961C132.887 374.266 133.386 369.57 133.739 364.051Z"
									[attr.fill]="paintDobras('panturrilha')" />
								<path id="Vector_36"
									d="M127.786 355.353C130.138 376.694 130.969 396.969 130.491 417.287C130.432 419.767 131.233 423.02 127.978 423.815C124.406 424.683 122.892 421.431 121.525 418.936C119.018 414.344 118.717 409.111 118.129 404.056C117.071 394.989 119.599 386.349 122.171 377.82C123.171 374.508 124.023 371.263 123.854 367.848C123.627 363.367 126.287 360.018 127.786 355.353Z"
									[attr.fill]="paintDobras('panturrilha')" />
								<path id="Vector_37"
									d="M128.927 486.375C127.618 465.586 125.23 444.95 121.459 423.601C123.973 425.301 124.862 426.891 125.318 428.62C126.744 434.037 128.162 439.424 128.39 445.12C128.772 454.753 129.911 464.357 129.706 474.019C129.617 478.14 129.199 482.254 128.927 486.375Z"
									[attr.fill]="paintDobras('panturrilha')" />
								<path id="Vector_38"
									d="M134.49 469.501C133.953 459.853 134.416 450.234 135.872 440.704C136.202 438.555 137.055 436.193 138.892 434.611C139.48 434.103 140.333 433.382 141.163 433.978C141.817 434.449 141.56 435.362 141.288 436.031C137.812 444.583 137.04 453.73 135.313 462.671C134.872 464.945 135.254 467.278 134.49 469.501Z"
									[attr.fill]="paintDobras('panturrilha')" />
								<path id="Vector_39"
									d="M181.306 364.051C174.662 372.249 173.376 381.264 171.076 390.073C168.694 399.177 169.187 408.221 169.098 417.339C169.062 421.475 170.789 425.184 172.075 428.966C172.648 430.652 174.015 432.168 176.22 431.984C178.484 431.8 179.726 430.343 180.174 428.289C180.747 425.677 181.541 423.013 181.482 420.393C181.174 406.565 182.386 392.774 182.225 378.961C182.166 374.266 181.659 369.571 181.306 364.051Z"
									[attr.fill]="paintDobras('panturrilha')" />
								<path id="Vector_40"
									d="M187.266 355.353C184.914 376.694 184.083 396.969 184.561 417.287C184.62 419.767 183.819 423.02 187.074 423.815C190.646 424.683 192.16 421.431 193.527 418.936C196.034 414.344 196.335 409.111 196.923 404.056C197.981 394.989 195.453 386.349 192.881 377.82C191.881 374.508 191.029 371.263 191.198 367.848C191.418 363.367 188.765 360.018 187.266 355.353Z"
									[attr.fill]="paintDobras('panturrilha')" />
								<path id="Vector_41"
									d="M186.127 486.375C187.436 465.586 189.824 444.95 193.595 423.601C191.081 425.301 190.192 426.891 189.736 428.62C188.31 434.037 186.892 439.424 186.664 445.12C186.282 454.753 185.143 464.357 185.348 474.019C185.429 478.14 185.855 482.254 186.127 486.375Z"
									[attr.fill]="paintDobras('panturrilha')" />
								<path id="Vector_42"
									d="M180.565 469.501C181.101 459.853 180.638 450.234 179.183 440.704C178.852 438.555 178 436.193 176.162 434.611C175.574 434.103 174.722 433.382 173.891 433.978C173.237 434.449 173.494 435.362 173.766 436.031C177.243 444.583 178.014 453.73 179.741 462.671C180.182 464.945 179.8 467.278 180.565 469.501Z"
									[attr.fill]="paintDobras('panturrilha')" />
							</g>
							<g id="costas" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true" position="top"
								(mouseover)="tooltipParte('costas')">
								<path id="Vector_43" opacity="0.8"
									d="M154.531 59.5653C155.956 61.2211 155.537 62.4942 155.545 63.6496C155.552 79.9355 155.934 96.2361 155.354 112.5C154.986 122.766 155.045 133.032 154.81 143.291C154.795 144.071 155.207 145.233 154.075 145.469C153.061 145.682 152.789 144.586 152.377 143.857C149.415 138.676 147.446 133.106 145.902 127.358C142.713 115.524 138.67 104.147 128.418 96.317C126.889 95.1469 125.235 94.0798 123.508 93.2556C121.399 92.2474 121.142 91.4967 123.222 90.0911C127.69 87.0739 132.57 84.9029 137.656 83.3207C144.351 81.2454 150.128 78.2208 152.304 71.0382C153.362 67.5058 153.737 63.7526 154.531 59.5653Z"
									[attr.fill]="paintDobras('costas')" />
								<path id="Vector_44"
									d="M151.084 60.4851C151.459 65.563 150.445 70.1845 147.262 74.1879C146.748 74.8282 146.131 75.7333 145.241 75.5273C144.154 75.2771 144.55 74.1438 144.484 73.3416C144.051 67.8517 144.462 62.7518 151.084 60.4851Z"
									[attr.fill]="paintDobras('costas')" />
								<path id="Vector_45" opacity="0.8"
									d="M160.521 59.5653C159.095 61.2211 159.514 62.4942 159.507 63.6496C159.499 79.9355 159.117 96.2361 159.698 112.5C160.065 122.766 160.006 133.032 160.242 143.291C160.256 144.071 159.845 145.233 160.977 145.469C161.991 145.682 162.263 144.586 162.674 143.857C165.636 138.676 167.606 133.106 169.149 127.358C172.339 115.524 176.381 104.147 186.634 96.317C188.162 95.1469 189.816 94.0798 191.543 93.2556C193.653 92.2474 193.91 91.4967 191.83 90.0911C187.361 87.0739 182.481 84.9029 177.395 83.3207C170.7 81.2454 164.923 78.2208 162.748 71.0382C161.682 67.5058 161.315 63.7526 160.521 59.5653Z"
									[attr.fill]="paintDobras('costas')" />
								<path id="Vector_46"
									d="M163.968 60.4851C163.594 65.5629 164.608 70.1845 167.79 74.1879C168.305 74.8281 168.922 75.7333 169.811 75.5273C170.899 75.2771 170.502 74.1437 170.568 73.3416C170.994 67.8516 170.583 62.7517 163.968 60.4851Z"
									[attr.fill]="paintDobras('costas')" />
							</g>
							<g id="subEscapular" [pactoCatTolltip]="tooltipParteRef" [darkTheme]="true"
								position="top" (mouseover)="tooltipParte('subEscapular')">
								<path id="sub-escapular L"
									d="M133.836 125.187C127.471 123.98 119.563 123.553 112.096 120.764C110.56 120.19 110.295 119.719 111.699 118.586C117.851 113.611 121.709 107.422 121.783 99.2458C121.805 96.8394 122.87 96.9204 124.539 98.1125C130.242 102.175 134.137 107.687 137.114 113.876C138.099 115.922 139.179 117.946 139.517 120.359C140.201 125.261 140.384 125.238 133.836 125.187Z"
									[attr.fill]="paintDobras('subEscapular')" />
								<path id="sub-escapular R"
									d="M181.218 125.187C187.583 123.98 195.491 123.553 202.958 120.764C204.494 120.19 204.759 119.719 203.355 118.586C197.203 113.611 193.345 107.422 193.271 99.2458C193.249 96.8394 192.184 96.9204 190.515 98.1125C184.812 102.175 180.917 107.687 177.94 113.876C176.956 115.922 175.875 117.946 175.537 120.359C174.854 125.261 174.67 125.238 181.218 125.187Z"
									[attr.fill]="paintDobras('subEscapular')" />
							</g>
						</g>
					</svg>
				</div>
				<div class="col-12 referencia">
					<span class="valor-referencia">
						<svg width="18" height="18" viewBox="0 0 18 18" fill="none"
							xmlns="http://www.w3.org/2000/svg">
							<rect id="Dote Circule" x="1" y="1" width="16" height="16" rx="8" fill="#8FACEF"
								stroke="white" />
						</svg>
						0 - 20%</span>
					<span class="valor-referencia">
						<svg width="18" height="18" viewBox="0 0 18 18" fill="none"
							xmlns="http://www.w3.org/2000/svg">
							<rect id="Dote Circule" x="1" y="1" width="16" height="16" rx="8" fill="#638BE9"
								stroke="white" />
						</svg>
						20 - 40%</span>
					<span class="valor-referencia">
						<svg width="18" height="18" viewBox="0 0 18 18" fill="none"
							xmlns="http://www.w3.org/2000/svg">
							<rect id="Dote Circule" x="1" y="1" width="16" height="16" rx="8" fill="#366AE2"
								stroke="white" />
						</svg>
						40 - 60%</span>
					<span class="valor-referencia">
						<svg width="18" height="18" viewBox="0 0 18 18" fill="none"
							xmlns="http://www.w3.org/2000/svg">
							<rect id="Dote Circule" x="1" y="1" width="16" height="16" rx="8" fill="#1D50C9"
								stroke="white" />
						</svg>
						60 - 80%</span>
					<span class="valor-referencia">
						<svg width="18" height="18" viewBox="0 0 18 18" fill="none"
							xmlns="http://www.w3.org/2000/svg">
							<rect id="Dote Circule" x="1" y="1.00049" width="16" height="16" rx="8" fill="#163E9C"
								stroke="white" />
						</svg>
						80 - 100%</span>
				</div>
			</div>
			<div class="col-6">
				<div class="lista">
					<div class="upper">
						<pacto-cat-form-select id="tr-select-ficha-treino" label="Ficha Treino"
							[control]="formGroup.get('fichaTreino')" [items]="listaFichas" idKey="id"
							labelKey="nome"></pacto-cat-form-select>
					</div>
					<div class="lower" *ngIf="listaAtiva">
						<div class="parte-treino row"
							*ngFor="let parte of getPartes(formGroup.get('fichaTreino').value)">
							<span class="nome-parte col-4">{{parte.nome | captalize}} <small>({{parte.lado |
									captalize}})</small></span>
							<span class="qtd-treino col-4">{{parte.qtdExercicios}} Exercicios</span>
							<span class="porcentagem col-4">{{parte.porcentagemTreino}} %</span>
						</div>
					</div>
				</div>
			</div>

		</div>
	</pacto-cat-card-plain> -->
</ng-container>

<pacto-traducoes-xingling #diaSemanaCurto>
	<span i18n="@treino-bi:dias-curto:domingo" xingling="domingo">Domingo</span>
	<span i18n="@treino-bi:dias-curto:segunda" xingling="segunda">Segunda</span>
	<span i18n="@treino-bi:dias-curto:terca" xingling="terca">Terça</span>
	<span i18n="@treino-bi:dias-curto:quarta" xingling="quarta">Quarta</span>
	<span i18n="@treino-bi:dias-curto:quinta" xingling="quinta">Quinta</span>
	<span i18n="@treino-bi:dias-curto:sexta" xingling="sexta">Sexta</span>
	<span i18n="@treino-bi:dias-curto:sabado" xingling="sabado">Sábado</span>
</pacto-traducoes-xingling>

<span
	#tooltipRemover
	[hidden]="true"
	i18n="@@crud-ambientes:remover:tooltip-icon">
	Remover
</span>

<ng-template #nomeColumnName>
	<span i18n="@@crud-alunos:nome:title:table">Nome</span>
</ng-template>

<ng-template #nomeColumnInicio>
	<span i18n="@@crud-alunos:inio:title:table">Início</span>
</ng-template>

<ng-template #nomeColumnTermino>
	<span i18n="@@crud-alunos:termino:title:table">Término</span>
</ng-template>

<ng-template #nomeColumnFichas>
	<span i18n="@@crud-alunos:fichas:title:table">Fichas</span>
</ng-template>

<ng-template #nomeColumnCompleto>
	<span i18n="@@crud-alunos:completo:title:table">Completo</span>
</ng-template>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="semPermissao">
		Seu usuário não possui permissão, procure seu administrador.
	</span>
	<span
		i18n="@@perfil-aluno-dados-pessoal:confitm-user:mensagem-success"
		xingling="confimUserSuccess">
		Email enviado com sucesso.
	</span>
</pacto-traducoes-xingling>

<span
	#realizado
	[hidden]="true"
	i18n="@@perfil-aluno-treinamento:realizado:grafico">
	Realizado
</span>
<span
	#removerTitulo
	[hidden]="true"
	i18n="@@perfil-aluno-treinamento:remover-programa:title">
	Remover programa ?
</span>
<span
	#sucessoRemover
	[hidden]="true"
	i18n="@@perfil-aluno-treinamento:remover-programa:mensagem-success">
	Programa removido com sucesso.
</span>
<span
	#erroRemover
	[hidden]="true"
	i18n="@@perfil-aluno-treinamento:remover-programa:mensagem-error">
	Não é possível remover esse programa pois já está vinculado.
</span>
<span
	#removerBody
	[hidden]="true"
	i18n="@@perfil-aluno-treinamento:remover-programa:body">
	Tem certeza que deseja remover o programa: {{ programaNome }} ?
</span>
<span
	#sucessoCreate
	[hidden]="true"
	i18n="@@perfil-aluno-treinamento:create-programa:mensagem-success">
	Programa criado com sucesso.
</span>
<span #messageErrorOlympia [hidden]="true">
	Usuário com pendências no sistema Olympia
</span>
<span #messageErrorConfigPrescricao [hidden]="true">
	Não é permitido lançar programa de treino para alunos inativos ou visitantes
</span>
<!-- 
<ng-template #tooltipParteRef>
	<span class="title-tooltip">{{tooltipAtivo?.nome | captalize}}</span>
	<hr class="separator-tooltip">
	<span class="text-tooltip">{{tooltipAtivo?.qtdExercicios}} Exercicios / {{tooltipAtivo?.porcentagemTreino}}%</span>
</ng-template> -->
