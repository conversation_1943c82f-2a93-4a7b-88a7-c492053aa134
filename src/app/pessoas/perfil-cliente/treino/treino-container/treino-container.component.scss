@import "src/assets/scss/pacto/plataforma-import.scss";

.card-grid {
	display: grid;
	grid-template-rows: 1fr;
	gap: 24px;

	&.row1 {
		grid-template-columns: minmax(411px, 560px) 1px minmax(179px, 250px) 1px minmax(
				179px,
				250px
			);
		gap: 64.5px;
		margin-bottom: 0.875rem;
		margin-top: 0.875rem;
	}

	&.row2 {
		grid-auto-flow: column dense;
		margin-top: 24px;
		grid-template-columns: 1.2fr 1px 1fr;
		gap: 52px;
		height: 302px;
	}

	&.row3 {
		margin-top: 24px;
		grid-template-columns: 1fr;
	}
}

.treino-executado-pie-chart {
	gap: 1.5rem;
	margin-top: auto;
	height: 175px;

	display: grid;
	grid-template-columns: 175px 1fr;
	grid-template-rows: 147px;
	align-items: center;
	padding-right: 1rem;
}

.gap-30 {
	gap: 30px;
	justify-content: space-between;
}

.vl {
	width: 1px;
	height: 69%;
	align-self: center;
	background-color: #c9cbcf;
	margin-top: 0.5rem;
}

.card-treino {
	padding: 16px;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
	display: block;
	width: 100%;
	font-size: 18px;
	font-weight: 400;
	line-height: 2rem;
	color: $preto07;
}

.card-info-text {
	font-size: 12px;
	font-weight: 400;
	line-height: 17.5px;
	margin-top: 8px;
	display: block;
}

.titulo {
	display: block;
	font-size: 14px;
	font-weight: 700;
	line-height: 16px;
	color: $pretoPri;
}

.sub-titulo {
	margin-top: 8px;
	display: block;
	font-size: 0.75rem;
	font-weight: 400;
	line-height: 14px;
	color: $cinzaPri;
}

.link-grande {
	font-size: 12px;
	font-weight: 700;
	line-height: 17.5px;
	color: $pretoPri;
	cursor: pointer;
	margin-top: 24px;
	max-height: 3rem;
	max-width: 237px;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.no-cursor {
	cursor: default !important;
}

.card-action-button {
	margin-top: auto;
}

.flex-column {
	height: 100%;
}

.legenda {
	font-size: 18px;
	font-weight: 400;
	line-height: 25px;

	display: flex;
	flex-direction: column;
	gap: 1.5rem;

	div {
		display: flex;
		gap: 2.5rem;

		span {
			padding-top: 8px;
		}

		> *:nth-child(2) {
			width: 3.44rem;
		}
	}

	b {
		margin-left: 30px;
		padding-top: 8px;
	}

	.pct {
		font-size: 32px;
		line-height: 32px;
		margin-right: 30px;
		font-weight: 700;

		&.pct-sunrise {
			color: $pequizaoPri;
		}

		&.pct-sunset {
			color: $laranjinhaPri;
		}

		&.pct-moon {
			color: $acaiPri;
		}
	}
}

.gauge-wrapper {
	font-family: Nunito Sans;
	font-size: 17px;
	font-weight: 400;
	line-height: 22px;
	text-align: center;
	color: $cinzaPri;
	height: 187px;
	// width: 147px;
}

.box {
	height: 442px;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
}

.card-sem-treino {
	padding: 25px 35px;
	display: flex;
	flex-wrap: wrap;

	.text-align-center {
		text-align: center;
	}

	.text {
		font-family: "Nunito Sans";
		font-weight: 700;
		font-size: 24px;
		line-height: 24px;
		color: $pretoPri;
		margin-bottom: 32px;
	}
}

.margin-bottom-20 {
	margin-bottom: 20px;
}

.div-professor {
	padding-bottom: 20px;
	padding-left: 0;
	border-bottom: 1px solid #d7d8db;
	width: 100%;

	.bit-title {
		@extend .type-h6;
		font-weight: 600;
		color: $pretoPri;
	}
}

.aviso-medico {
	margin-bottom: 24px;
	padding: 25px 15px;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 #e5e9f2;
	background: #fff;
	display: flex;
	height: 60px;
	align-items: center;
	justify-content: initial;
	width: 100%;
	font-size: 16px;
	font-family: "Nunito Sans";
	color: #e10505;

	i {
		margin-right: 10px;
	}

	span::first-letter {
		text-transform: uppercase;
	}
}

.custom-buttom {
	margin-right: 15px;
	margin-bottom: 1.25rem;
}

.fichas-btns {
	margin-bottom: 1.25rem;
	display: flex;
	gap: 0.87rem;
}

pacto-cat-card-plain {
	padding: 16px;
	overflow-x: auto;

	&.row3,
	&.row2 {
		padding-bottom: 5px;
	}
}

.row2 {
	pacto-cat-column-chart {
		margin-top: auto;
	}
}

.fichas-relacionadas > *:last-child {
	margin-top: auto;

	a {
		margin-top: 0;
	}
}

:host {
	.lista-programas {
		::ng-deep {
			pacto-relatorio {
				.table-content {
					padding: 0 16px 16px;
				}

				.pacto-table-title-block {
					padding: 16px 0 15px;
				}

				.table-content {
					padding: 0;

					th {
						font-size: 14px;
						font-weight: 700;
					}

					td {
						font-size: 12px;

						i {
							font-size: 16px;
						}
					}
				}
			}
		}
	}
}

.lower {
	border: 1px solid #d7d8db;
	border-radius: 8px;
	max-height: 360px;
	overflow: auto;

	.parte-treino {
		width: 100%;
		border-bottom: 1px solid #d7d8db;
		padding: 16px;
		margin: 0px;

		&:last-child {
			border: none;
		}

		.nome-parte {
			font-family: Poppins;
			font-size: 14px;
			font-weight: 600;
			color: #80858c;
			padding: 0px;

			small {
				font-size: 12px;
			}
		}

		.qtd-treino {
			//styleName: Display/6;
			font-family: Poppins;
			font-size: 14px;
			font-weight: 500;
			color: #797d86;
			text-align: end;
			padding: 0px;
		}

		.porcentagem {
			//styleName: Display/6;
			font-family: Poppins;
			font-size: 14px;
			font-weight: 500;
			color: #04af04;
			text-align: end;
			padding: 0px;
		}
	}
}

::ng-deep {
	.title-tooltip {
		font-family: Nunito Sans;
		font-size: 14px;
		font-weight: 400;
		color: #fff;
	}

	.separator-tooltip {
		border-bottom: 1px solid #c9cbcf;
		width: 90%;
	}

	.text-tooltip {
		font-family: Nunito Sans;
		font-size: 12px;
		font-weight: 400;
		color: #fff;
	}
}

.referencia {
	display: flex;
	justify-content: space-evenly;
	flex-direction: row;

	.valor-referencia {
		font-family: Nunito Sans;
		font-size: 12px;
		font-weight: 400;
		color: #797d86;

		i.porcento0ate20 {
			color: #8facef;
		}

		i.porcento20ate40 {
			color: #638be9;
		}

		i.porcento40ate60 {
			color: #366ae2;
		}

		i.porcento60ate80 {
			color: #1d50c9;
		}

		i.porcento80ate100 {
			color: #163e9c;
		}
	}
}

::ng-deep ngb-modal-window:not(:only-of-type) {
	background-color: #000000a0;
}
