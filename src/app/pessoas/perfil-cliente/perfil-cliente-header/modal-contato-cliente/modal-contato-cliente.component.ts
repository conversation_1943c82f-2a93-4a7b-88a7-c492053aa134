import { Component, EventEmitter, OnInit, Output } from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { FormControl, FormGroup } from "@angular/forms";

@Component({
	selector: "pacto-modal-contato-cliente",
	templateUrl: "./modal-contato-cliente.component.html",
	styleUrls: ["./modal-contato-cliente.component.scss"],
})
export class ModalContatoClienteComponent implements OnInit {
	dadosPessoais;
	email = false;
	telefone = false;
	@Output() update = new EventEmitter<string>();
	formGroup = new FormGroup({
		selecionado: new FormControl(),
	});
	itens: any[];

	constructor(
		private modal: NgbActiveModal,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {
		this.itens = [];
		if (this.telefone) {
			for (let i = 0; this.dadosPessoais.telefones.length > i; i++) {
				if (this.dadosPessoais.telefones[i].whatsapp) {
					const tel = this.dadosPessoais.telefones[i];
					const numero = (tel.ddi ? "+" + tel.ddi : "+55") + " " + tel.numero;
					this.itens.push({ id: numero, nome: numero });
				}
			}
		} else if (this.email) {
			for (let i = 0; this.dadosPessoais.emails.length > i; i++) {
				const numero = this.dadosPessoais.emails[i];
				this.itens.push({ id: numero, nome: numero });
			}
		}
	}

	fecharHandler() {
		this.modal.close();
	}

	confirmar() {
		const valorInserido = this.formGroup.get("selecionado").value;
		if (!valorInserido || valorInserido.length === 0) {
			this.snotifyService.info(
				"Selecione um " + (this.email ? "e-mail" : "número")
			);
			return;
		}
		if (this.telefone) {
			const valorInput = Number(
				valorInserido
					.replace("(", "")
					.replace(")", "")
					.replace(" ", "")
					.replace("+", "")
					.replace("-", "")
			);
			const target = "https://api.whatsapp.com/send?phone=" + valorInput;
			window.open(target, "_blank");
		} else if (this.email) {
			const target = "mailto:" + valorInserido;
			window.open(target, "_blank");
		}
	}
}
