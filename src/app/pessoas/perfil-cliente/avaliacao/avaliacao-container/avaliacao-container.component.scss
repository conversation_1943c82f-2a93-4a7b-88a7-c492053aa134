// @import "~src/assets/scss/pacto/plataforma-import.scss";

// pacto-cat-card-plain {
//   margin-top: 20px;
// }

// .col-header {
//   font-family: Poppins;
//   font-size: 14px;
//   font-weight: 600;
//   line-height: 18px;
//   letter-spacing: 0.25px;
//   color: #51555A;
// }

// .items {
//   border-right: 1px solid #C9CBCF;
//   gap: 35px;
//   justify-content: space-evenly;
//   height: 100%;
//   display: flex;
//   flex-direction: column;
//   flex-wrap: nowrap;
// }

// .top {
//   display: flex;
//   flex-direction: row;
//   justify-content: space-between;

//   .buttons {
//     pacto-cat-button {
//       padding-right: 8px;
//     }
//   }
// }

// .info {
//   margin: 20px;
//   font-family: Poppins;
//   font-size: 14px;
//   font-weight: 600;
//   line-height: 18px;
//   letter-spacing: 0.25px;

// }

// .bordered {
//   border: 1px solid #D7D8DB;
//   border-radius: 8px;
//   margin: 16px 0;
//   display: flex;
//   justify-content: space-around;
//   align-items: center;

//   .area {
//     display: flex;
//     flex-direction: column;
//     flex-wrap: nowrap;
//     align-items: center;
//     padding: 29px;

//     .area-value {
//       font-family: Poppins;
//       font-size: 14px;
//       font-weight: 500;
//       line-height: 14px;
//       letter-spacing: 0.25px;
//       color: #55585E;
//     }

//     .area-label {
//       font-family: Nunito Sans;
//       font-size: 12px;
//       font-weight: 400;
//       line-height: 16px;
//       letter-spacing: 0px;
//       padding-top: 2px;
//       color: #80858C;
//     }

//     .area-info {
//       font-family: Poppins;
//       font-size: 14px;
//       font-weight: 600;
//       line-height: 18px;
//       letter-spacing: 0.25px;
//       padding-top: 16px;
//       color: #55585E;
//       display: flex;
//       align-content: center;

//       i {
//         padding-left: 6px;
//         font-size: 16px;
//         font-weight: 400;
//       }
//     }

//     &.sectioned-area {
//       border: 1px solid #D7D8DB;
//       border-radius: 0px;
//       border-bottom: 0px;
//       border-top: 0px;
//       width: 33%;

//       &:first-child {
//         border-left: 0px;
//         border-right: 0px;
//       }

//       &:last-child {
//         border-right: 0px;
//         border-left: 0px;
//       }
//     }

//   }
// }

// .separator {
//   border-right: 1px solid #D7D8DB;

// }

// .coluna {
//   display: flex;
//   gap: 8px;

//   .card-parte-corpo {
//     border: 1px solid #D7D8DB;
//     display: flex;
//     border-radius: 8px;
//     width: 177px;
//     height: 78px;
//     flex-direction: column;
//     align-items: center;
//     justify-content: space-evenly;

//     .upper {
//       width: 100%;
//       display: flex;
//       flex-direction: row;
//       justify-content: space-evenly;
//       flex-wrap: nowrap;
//       align-items: center;

//       .tamanho {
//         //styleName: Display/5;
//         font-family: Poppins;
//         font-size: 18px;
//         font-weight: 500;
//         line-height: 18px;
//         letter-spacing: 0.25px;
//       }

//       .tipoMedida {
//         //styleName: Display/7;
//         font-family: Poppins;
//         font-size: 12px;
//         font-weight: 500;
//         line-height: 12px;
//         letter-spacing: 0.25px;

//       }

//       .diferencial {
//         //styleName: Display/7;
//         font-family: Poppins;
//         font-size: 12px;
//         font-weight: 500;
//         line-height: 12px;
//         letter-spacing: 0.25px;

//       }
//     }

//     .lower {
//       width: 100%;
//       display: flex;
//       flex-direction: row;
//       justify-content: space-evenly;
//       flex-wrap: nowrap;

//       .cor {
//         border: 8px solid;
//         border-radius: 50%;
//         height: 16px;
//         width: 16px;
//       }

//       .parteNome {
//         //styleName: Overline/2;
//         font-family: Nunito Sans;
//         font-size: 12px;
//         font-weight: 400;
//         line-height: 16px;
//         letter-spacing: 0px;
//         text-align: center;
//       }
//     }

//   }
// }

// .gapped {
//   gap: 8px 0;
// }

// .graph {
//   min-height: 231px;
//   flex-direction: column;
// }

// .conteudo {
//   display: flex;
//   flex-direction: column;
//   justify-content: space-between;
//   color: #51555A;

//   .head {
//     padding-bottom: 8px;
//     font-family: Nunito Sans;
//     font-size: 14px;
//     font-weight: 600;

//   }

//   .text {
//     font-family: Nunito Sans;
//     font-size: 12px;
//     font-weight: 400;
//     text-transform: lowercase;

//     &::first-letter {
//       text-transform: capitalize;
//     }
//   }

//   table {
//     margin-top: 32px;
//     width: 100%;

//     .linha {
//       width: 100%;
//       justify-content: space-between;

//       &.isEven {
//         background-color: #FAFAFA;
//       }

//       td {
//         padding: 16px 14px;
//       }

//       .icon-calendar {
//         width: 16px;
//         padding: 16px 12px 16px 14px;
//         color: #51555A;
//         font-size: 16px;
//         font-weight: 400;
//       }

//       .date {
//         padding: 16px 16px 14px 0px;
//         font-family: Nunito Sans;
//         font-size: 12px;
//         font-weight: 400;
//         color: #51555A;

//       }

//       .name {
//         font-family: Nunito Sans;
//         font-size: 12px;
//         font-weight: 400;
//         color: #B4B7BB;
//         text-transform: lowercase;

//         &::first-letter {
//           text-transform: capitalize;
//         }
//       }

//       .icon-trash {
//         color: #51555A;
//         font-size: 13px;
//       }
//     }
//   }
// }

// .row-header {
//   font-family: Nunito Sans;
//   font-size: 14px;
//   font-weight: 700;
//   color: #51555A;
// }

// .actionable:hover {
//   cursor: pointer
// }

// ::ng-deep {
//   .tablePerimetria {
//     .table-content {
//       padding: 0px;
//     }
//   }
// }

// .positivo {
//   color: #04AF04;
// }

// .negativo {
//   color: #DB2C3D;
// }

// .empty-state {
//   display: flex;
//   align-items: center;
//   flex-direction: column;
//   justify-content: center;
// }

@import "~src/assets/scss/pacto/plataforma-import.scss";

.top-aux {
	width: 100%;
	background-color: $branco;
	justify-content: center;
	display: flex;
}

pacto-avaliacoes-aluno-top {
	@include plataformaV2LarguraConteudo();
}

.content-aux {
	background-color: $cinzaClaroPri;
	color: $pretoPri;
	display: flex;
	align-items: center;
	flex-direction: column;

	> * {
		@include plataformaV2LarguraConteudo();
	}
}

.avaliacao-nome {
	margin: 30px 0px;
	text-align: left;
}

.tabela-scroll {
	height: calc(100vh - 315px);
	position: relative;
}

.empty-state {
	color: #666;
	border: 1px solid #f1f1f1;
	font-size: 12px;
	background-color: #f9f9f9;
	line-height: 100px;
	text-align: center;
}

/** Configuracoes das divs(conteudo) **/

.titulo-box {
	height: 20px;
	@extend .type-h5;
	line-height: 1;
	color: $pretoPri;
	text-align: center;
	padding-top: 71px;
}

.pct-value {
	flex-grow: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	text-align: center;
	line-height: 1em;
	@extend .type-hero-bold;
	height: 56px;
	padding-top: 69px;
}

.descricao-label {
	text-align: center;
	margin-top: 44px;
	padding: 0 20%;
	font-family: "Nunito Sans", sans-serif;
	font-size: 14px;
	line-height: 1;
	color: $gelo04;
}

.resumo-grupos {
	display: flex;
	justify-content: space-between;
}

.title {
	@extend .type-h5;
	color: $pretoPri;
}

.desc-label {
	@extend .type-p-small-rounded;
	color: $cinza05;
	text-align: left;
	margin-top: 3px;
}

.select-grupos {
	width: 168px;
	height: 32px;
	margin-top: 40px;
	margin-right: 40px;
}

.select-dobras {
	width: 168px;
	height: 32px;
	margin-right: 50px;
}

.select-perimetria {
	margin-top: 35px;
	//width: 150px;
	height: 32px;
	margin-right: 30px;
}

.top-row {
	margin-left: 30px;
	margin-top: 27px;
}

.top-row-dobras {
	display: flex;
	@extend .top-row;
	justify-content: space-between;
}

.center-row-perimetria {
	margin-top: 20px;
	margin-right: 30px;
}

.tabela-grupos {
	display: flex;
	justify-content: space-between;
	height: 56px;

	.colum {
		margin: 12px 24px 0px 24px;
		@extend .type-h6;
	}

	.colum-2 {
		color: $gelo04;
		width: 200px;
	}
}

.colum-1 {
	width: 200px;
}

.tabela-grupos:nth-child(odd) {
	background-color: #fafafa;
}

.center-row-dobras {
	margin-top: 20px;
	margin-right: 30px;
}

.resumo-perimetria {
	display: flex;
	justify-content: space-between;
}

//Css Bootstrap

.bb {
	display: grid;
	grid-template-columns: 1fr 1fr;
	grid-template-rows: auto;
	grid-gap: 30px;
	@media (max-width: 1200px) {
		//grid-template-columns: 0.5fr 0.5fr 0.5fr 0.5fr 0.5fr 0.5fr;
		//grid-template-areas: "avaliacoes avaliacoes gordura gordura  massa-magra  massa-magra"
		//"exercicios exercicios exercicios exercicios exercicios exercicios"
		//"perimetria perimetria perimetria peso-x-massa peso-x-massa peso-x-massa"
		//"dobras dobras dobras dobras dobras dobras";
	}
	@media (max-width: 970px) {
		grid-template-columns: 2fr;
		grid-template-areas:
			"avaliacoes" "gordura" "massa-magra"
			"exercicios" "perimetria" "peso-x-massa" "dobras";
	}
	@media (max-width: 750px) {
		grid-template-columns: 0.5fr;
		grid-template-areas:
			"avaliacoes" "gordura" "massa-magra"
			"exercicios" "perimetria" "peso-x-massa" "dobras";
	}
	grid-template-areas:
		"ultima ultima"
		"ultima ultima"
		"exercicios exercicios"
		"exercicios exercicios"
		"perimetria peso-x-massa"
		"perimetria peso-x-massa"
		"dobras dobras"
		"dobras dobras"
		"dobras dobras";
}

.dobras {
	grid-area: dobras;
}

.avaliacoes {
	grid-area: avaliacoes;
}

.gordura {
	grid-area: gordura;
}

.massa-magra {
	grid-area: massa-magra;
}

.exercicios {
	grid-area: exercicios;
}

.ultima {
	grid-area: ultima;
}

.perimetria {
	grid-area: perimetria;
}

.peso-x-massa {
	grid-area: peso-x-massa;
}

.box-left {
	height: 283px;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
}

.box-right-1 {
	height: 442px;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
}

.box-right-2 {
	height: 437px;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
}

.box-right-3 {
	height: 437px;
	border-radius: 10px;
	margin-bottom: 50px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
}

.box-right-4 {
	height: 520px;
	border-radius: 10px;
	box-shadow: 0 1px 4px 0 $geloPri;
	background-color: $branco;
}

.caixa {
	border: 1px dashed red;
	min-height: 60px;
}

.chart-peso {
	margin-top: 50px;
}

.table-grupos {
	position: center;
	margin: 23px 35px 25px 30px;
	height: 390px;
	overflow: auto;
}

.sem-avaliacao {
	padding: 25px 35px;
	display: flex;
	flex-wrap: wrap;

	.text-align-center {
		text-align: center;
	}

	.text {
		font-family: "Nunito Sans";
		font-weight: 700;
		font-size: 24px;
		line-height: 24px;
		color: $pretoPri;
		margin-bottom: 32px;
	}
}

.center-row-empty {
	margin-top: 20px;
	margin-right: 30px;
	margin-left: 30px;
}

.content-aviso-medico {
	padding: 0px 16px;

	.aviso-medico {
		padding: 25px 15px;
		border-radius: 10px;
		box-shadow: 0 1px 4px 0 #e5e9f2;
		background: #fff;
		display: flex;
		height: 60px;
		align-items: center;
		justify-content: initial;
		width: 100%;
		font-size: 16px;
		font-family: "Nunito Sans";
		color: #e10505;

		i {
			margin-right: 10px;
		}

		span::first-letter {
			text-transform: uppercase;
		}
	}
}

.div-empty {
	margin: 5px 0;
	border-radius: 10px;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	width: 100%;
	margin-top: 45px;
	padding: 24px 16px 25px 22px;
	align-items: center;

	.div-interna-empty {
		width: 450px;
		text-align: center;
		justify-content: center;

		.icon-empty {
			width: 112px;
			height: 112px;
		}

		.titulo-empty {
			padding-top: 20px;
			font-size: 14px;
			color: #55585e;
			font-weight: 600;
		}

		.text-empty {
			font-size: 14px;
			color: #797d86;
			font-weight: 400;
			padding-top: 15px;
		}
	}
}

.display-flex {
	display: flex;
}

.componente-corpo-grupo-muscular {
	display: flex;
	justify-content: space-around;

	::ng-deep .grupo-muscular-frontal {
		max-height: 380px;
		max-width: 232px;
	}

	::ng-deep .grupo-muscular-posterior {
		max-height: 380px;
		max-width: 232px;
	}
}
