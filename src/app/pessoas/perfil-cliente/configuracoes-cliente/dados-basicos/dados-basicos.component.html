<pacto-cat-card-plain id="idFormulario">
	<span class="sesi-title">Dados básicos</span>
	<div class="row">
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-multi-select-filter
				[control]="form.get('tipoDePessoa')"
				[options]="listaPessoa"
				[paramBuilder]="selectBuilder"
				errorMsg="Selecione o tipo de pessoa"
				id="dados-basicos-tipo-de-pessoa"
				i18n-label="@@dados-basicos:tipo-de-pessoa"
				i18n-placeholder="@@dados-basicos:tipo"
				idKey="codigo"
				label="Tipo de pessoa"
				labelKey="descricao"></pacto-cat-form-multi-select-filter>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-select
				[control]="form.get('tipoPessoa')"
				[items]="tipoPessoa$ | async"
				id="dados-basicos-tipo-pessoa"
				i18n-label="@@dados-basicos:tipo-pessoa"
				idKey="codigo"
				label="Pessoa"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-input
				[control]="form.get('nome')"
				id="dados-basicos-nome"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@dados-basicos:nome-required"
				i18n-label="@@dados-basicos:nome"
				i18n-placeholder="@@dados-basicos:nome-completo"
				label="*Nome"
				placeholder="Nome completo"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-4" style="position: relative">
			<pacto-cat-form-input
				id="dados-basicos-nome-registro"
				[control]="form.get('nomeRegistro')"
				[label]="
					(configCliFormValidation.configForms['nomeRegistro']?.indicator ||
						'') + 'Nome de registro'
				"
				i18n-label="@@dados-basicos:nome"
				i18n-placeholder="@@dados-basicos:nome-completo"
				placeholder="Nome de registro completo"></pacto-cat-form-input>
			<div
				(click)="infoNomeRegistro()"
				[ngStyle]="{
					position: 'absolute',
					'top.px': 20,
					color: '#1E60FA',
					cursor: 'pointer',
					left:
						'calc(150px + ' +
						(configCliFormValidation.configForms['nomeRegistro']?.indicator
							? '15px'
							: '0px') +
						')'
				}">
				<i class="pct pct-info"></i>
			</div>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-datepicker
				[control]="form.get('dataNasc')"
				[label]="
					(configCliFormValidation.configForms['dataNasc']?.indicator || '') +
					'Data de nascimento'
				"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@dados-basicos:data-nasc-required"
				i18n-label="@@dados-basicos:data-nascimento"
				id="data-nascimento"></pacto-cat-form-datepicker>
		</div>
		<ng-container
			*ngIf="
				form.get('tipoPessoa').value == 0 || form.get('tipoPessoa').value == 2
			">
			<ng-container *ngIf="form.get('tipoPessoa').value == 0">
				<div class="col-sm-12 col-lg-4 col-md-4">
					<pacto-cat-form-input
						id="dados-basicos-cpf"
						[control]="form.get('cpf')"
						[label]="
							(configCliFormValidation.configForms['cpf']?.indicator || '') +
							'CPF'
						"
						[textMask]="{ mask: cpfMask }"
						errorMsg="Campo obrigatório"
						i18n-errorMsg="@@dados-basicos:cpf-required"></pacto-cat-form-input>
				</div>

				<ng-container *ngTemplateOutlet="campoCnpjClienteSesi"></ng-container>

				<div class="col-sm-12 col-lg-4 col-md-4">
					<pacto-cat-form-input
						id="dados-basicos-rg"
						[control]="form.get('rg')"
						[label]="
							(configCliFormValidation.configForms['rg']?.indicator || '') +
							'RG'
						"
						errorMsg="Campo obrigatório"
						i18n-errorMsg="@@dados-basicos:rg-required"
						maxlength="20"
						placeholder="000000000"></pacto-cat-form-input>
				</div>
			</ng-container>
			<ng-container *ngIf="form.get('tipoPessoa').value == 2">
				<div class="col-sm-12 col-lg-4 col-md-4">
					<pacto-cat-form-input
						id="dados-basicos-passaporte"
						[control]="form.get('passaporte')"
						i18n-label="@@dados-basicos:placeholder"
						label="Passaporte"
						placeholder="0000000"></pacto-cat-form-input>
				</div>
			</ng-container>
			<div
				class="col-sm-12 col-lg-4 col-md-4"
				*ngIf="form.get('tipoPessoa').value == 0">
				<pacto-cat-form-input
					id="dados-basicos-rg-orgao"
					[control]="form.get('rgOrgao')"
					i18n-label="@@dados-basicos:rg-orgao"
					label="Orgão Emissor do RG"
					maxlength="10"></pacto-cat-form-input>
			</div>
			<div
				class="col-sm-12 col-lg-4 col-md-4"
				*ngIf="form.get('tipoPessoa').value == 0">
				<pacto-cat-form-select-filter
					id="dados-basicos-rg-uf"
					[control]="form.get('rgUf')"
					[options]="estadosUf"
					i18n-label="@@dados-basicos:rg-uf"
					idKey="id"
					label="Estado de emissão do RG"
					labelKey="label"></pacto-cat-form-select-filter>
			</div>
			<div
				class="col-sm-12 col-lg-4 col-md-4"
				*ngIf="form.get('tipoPessoa').value == 2">
				<pacto-cat-form-input
					id="dados-basicos-rne"
					[control]="form.get('rne')"
					label="RNE"
					maxlength="32"></pacto-cat-form-input>
			</div>
			<div class="col-sm-12 col-lg-4 col-md-4">
				<pacto-cat-form-input
					id="dados-basicos-nacionalidade"
					[control]="form.get('nacionalidade')"
					[label]="
						(configCliFormValidation.configForms['nacionalidade']?.indicator ||
							'') + 'Nacionalidade'
					"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@dados-basicos:nacionalidade-required"
					i18n-label="@@dados-basicos:nacionalidade"></pacto-cat-form-input>
			</div>
			<div class="col-sm-12 col-lg-4 col-md-4">
				<pacto-cat-form-input
					id="dados-basicos-naturalidade"
					[control]="form.get('naturalidade')"
					[label]="
						(configCliFormValidation.configForms['naturalidade']?.indicator ||
							'') + 'Naturalidade'
					"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@dados-basicos:naturalidade-required"
					i18n-label="@@dados-basicos:naturalidade"></pacto-cat-form-input>
			</div>
			<div class="col-sm-12 col-lg-4 col-md-4" style="position: relative">
				<pacto-cat-form-select-filter
					id="dados-basicos-sexo"
					[addEmptyOption]="
						!configCliFormValidation.configForms['sexo']?.indicator
					"
					[control]="form.get('sexo')"
					[label]="
						(configCliFormValidation.configForms['sexo']?.indicator || '') +
						'Sexo'
					"
					[options]="listaSexo$ | async"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@dados-basicos:sexo-required"
					i18n-label="@@dados-basicos:sexo"
					idKey="codigo"
					labelKey="descricao"></pacto-cat-form-select-filter>
				<div
					(click)="infoSexo()"
					[ngStyle]="{
						position: 'absolute',
						'top.px': 20,
						color: '#1E60FA',
						cursor: 'pointer',
						left:
							'calc(59px + ' +
							(configCliFormValidation.configForms['sexo']?.indicator
								? '13px'
								: '0px') +
							')'
					}">
					<i class="pct pct-info"></i>
				</div>
			</div>
			<div class="col-sm-12 col-lg-4 col-md-4" style="position: relative">
				<pacto-cat-form-select-filter
					id="dados-basicos-genero"
					[addEmptyOption]="
						!configCliFormValidation.configForms['genero']?.indicator
					"
					[control]="form.get('genero')"
					[label]="
						(configCliFormValidation.configForms['genero']?.indicator || '') +
						'Gênero'
					"
					[options]="gender$ | async"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@dados-basicos:genero-required"
					i18n-label="@@dados-basicos:genero"
					idKey="codigo"
					labelKey="descricao"></pacto-cat-form-select-filter>
				<div
					(click)="infoGenero()"
					[ngStyle]="{
						position: 'absolute',
						'top.px': 20,
						color: '#1E60FA',
						cursor: 'pointer',
						left:
							'calc(78px + ' +
							(configCliFormValidation.configForms['genero']?.indicator
								? '17px'
								: '0px') +
							')'
					}">
					<i class="pct pct-info"></i>
				</div>
			</div>

			<div class="col-sm-12 col-lg-4 col-md-4">
				<pacto-cat-form-select-filter
					id="dados-basicos-estado-civil"
					[addEmptyOption]="
						!configCliFormValidation.configForms['estadoCivil']?.indicator
					"
					[control]="form.get('estadoCivil')"
					[label]="
						(configCliFormValidation.configForms['estadoCivil']?.indicator ||
							'') + 'Estado civil'
					"
					[options]="estadoCivil$ | async"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@dados-basicos:estado-civil-required"
					i18n-label="@@dados-basicos:estado-civil"
					idKey="codigo"
					label="Estado civil"
					labelKey="descricao"></pacto-cat-form-select-filter>
			</div>
			<div class="col-sm-12 col-lg-4 col-md-4">
				<pacto-cat-form-select-filter
					id="dados-basicos-profissao"
					[addEmptyOption]="
						!configCliFormValidation.configForms['profissao']?.indicator
					"
					[control]="form.get('profissao')"
					[endpointUrl]="profissoesUrl"
					[label]="
						(configCliFormValidation.configForms['profissao']?.indicator ||
							'') + 'Profissão'
					"
					[paramBuilder]="selectBuilder"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@dados-basicos:profissao-required"
					i18n-label="@@dados-basicos:profissao"
					idKey="codigo"
					labelKey="descricao"></pacto-cat-form-select-filter>
			</div>
			<div class="col-sm-12 col-lg-4 col-md-4">
				<pacto-cat-form-select-filter
					id="dados-basicos-grau-instrucao"
					[addEmptyOption]="
						!configCliFormValidation.configForms['grauInstrucao']?.indicator
					"
					[control]="form.get('grauInstrucao')"
					[endpointUrl]="grauInstrucaoUrl"
					[label]="
						(configCliFormValidation.configForms['grauInstrucao']?.indicator ||
							'') + 'Grau de Instrução'
					"
					[paramBuilder]="selectBuilder"
					errorMsg="Campo obrigatório"
					i18n-errorMsg="@@dados-basicos:grauInstrucao-required"
					i18n-label="@@dados-basicos:grau-instrucao"
					idKey="codigo"
					labelKey="descricao"></pacto-cat-form-select-filter>
			</div>
			<ng-container [ngTemplateOutlet]="estadoCidadePaisTempl"></ng-container>
		</ng-container>
		<ng-container *ngIf="form.get('tipoPessoa').value == 1">
			<div class="col-4">
				<pacto-cat-form-input
					id="dados-basicos-cnpj"
					[control]="form.get('cnpj')"
					[label]="
						(configCliFormValidation.configForms['cnpj']?.indicator || '') +
						'CNPJ'
					"
					[textMask]="{ mask: cnpjMask }"
					i18n-label="@@dados-basicos:cnpj"></pacto-cat-form-input>
			</div>

			<ng-container *ngTemplateOutlet="campoCnpjClienteSesi"></ng-container>

			<div class="col-4">
				<pacto-cat-form-input
					id="dados-basicos-inscricao-estadual"
					[control]="form.get('inscricaoEstadual')"
					i18n-label="@@dados-basicos:inscricao-estadual"
					label="Inscrição Estadual"></pacto-cat-form-input>
			</div>

			<div class="col-4">
				<pacto-cat-form-input
					id="dados-basicos-inscricao-municipal"
					[control]="form.get('inscricaoMunicipal')"
					i18n-label="@@dados-basicos:inscricao-municipal"
					label="Inscrição Municipal"></pacto-cat-form-input>
			</div>
			<div class="col-4">
				<pacto-cat-form-input
					id="dados-basicos-cfdf"
					[control]="form.get('cfdf')"
					i18n-label="@@dados-basicos:cfdf"
					label="CFDF"></pacto-cat-form-input>
			</div>
			<div class="col-4">
				<pacto-cat-form-input
					id="dados-basicos-nome-responsavel-empresa"
					[control]="form.get('nomeResponsavelEmpresa')"
					[label]="'Nome do responsável da empresa'"
					i18n-label="@@dados-basicos:nome-responsavel-empresa"
					label="Nome do responsável da empresa"></pacto-cat-form-input>
			</div>
			<div class="col-4">
				<pacto-cat-form-input
					id="dados-basicos-cpf-responsavel-empresa"
					[control]="form.get('cpfResponsavelEmpresa')"
					[label]="'CPF do responsável da empresa'"
					i18n-label="@@dados-basicos:cpf-responsavel-empresa"
					label="CPF do responsável da empresa"></pacto-cat-form-input>
			</div>
			<ng-container [ngTemplateOutlet]="estadoCidadePaisTempl"></ng-container>
		</ng-container>
	</div>

	<div>
		<hr class="separator" />

		<ng-container *ngIf="recursoEmail">
			<span class="sesi-title">Contatos</span>
			<div
				*ngFor="let email of formDosEmails.controls; let i = index"
				class="row">
				<div class="col-sm-12 col-lg-8 col-md-8">
					<pacto-cat-form-input
						[id]="'dados-basicos-email-' + i"
						[control]="email.get('email')"
						[enableClearInput]="false"
						[errorMsg]="
							email.get('email').hasError('required')
								? 'Campo obrigatório'
								: 'E-mail inválido'
						"
						[label]="
							(configCliFormValidation.configForms['email']?.indicator || '') +
							'E-mail' +
							(i + 1)
						"
						i18n-errorMsg="@@dados-basicos:email-required"
						i18n-label="@@dados-basicos:email"></pacto-cat-form-input>
				</div>
				<div
					class="col-sm-12 col-lg-2 col-md-2 align-self-center"
					style="margin-top: 27px">
					<ds3-checkbox
						[id]="'dados-basicos-email-correspondencia-' + i"
						i18n-label="@@dados-basicos:email-correspondencia"
						[formControl]="
							formDosEmails.controls[i].get('emailCorrespondencia')
						">
						Email de correspondência
					</ds3-checkbox>
				</div>
				<div
					class="col-sm-12 col-lg-1 col-md-1 d-flex align-items-center"
					style="margin-top: 27px">
					<span
						[id]="'dados-basicos-email-btn-remover-' + i"
						(click)="removeEmail(i)"
						class="actionable">
						<i class="pct pct-trash-2 cor-hellboy05"></i>
					</span>
				</div>
			</div>

			<div *ngIf="permiteEditar">
				<div>
					<button
						id="dados-basicos-btn-add-email"
						(click)="addEmail()"
						ds3-text-button
						i18n="@@dados-basicos:adicionar-email">
						<i class="pct pct-plus"></i>
						Adicionar outro e-mail
					</button>
				</div>
			</div>

			<hr class="separator" />
		</ng-container>

		<ng-container *ngIf="recursoTelefone">
			<div
				*ngFor="let telefone of formDosTelefones.controls; let i = index"
				class="col-12 row">
				<div class="col-sm-12 col-lg-4 col-md-4">
					<pacto-cat-form-select-filter
						[id]="'dados-basicos-tipo-telefone-' + i"
						[control]="formDosTelefones.controls[i].get('tipoTelefone')"
						[options]="phoneTypes$ | async"
						errorMsg="Campo obrigatório"
						i18n-label="@@dados-basicos:tipoTelefone"
						idKey="codigo"
						label="*Tipo de telefone"
						labelKey="descricao"></pacto-cat-form-select-filter>
				</div>
				<div class="col-sm-12 col-lg-4 col-md-4">
					<pacto-cat-form-input
						[id]="'dados-basicos-tipo-numero-' + i"
						[control]="telefone.get('telefone')"
						[label]="
							(configCliFormValidation.configForms['telefone']?.indicator ||
								'') +
							'Telefone' +
							(i + 1)
						"
						[label]="'Telefone ' + (i + 1)"
						[textMask]="{ mask: foneMask }"
						errorMsg="Campo obrigatório"
						i18n-errorMsg="@@dados-basicos:telefone-required"
						i18n-label="@@dados-basicos:telefone"
						max="11"></pacto-cat-form-input>
				</div>
				<div class="col-sm-12 col-lg-3 col-md-3">
					<pacto-cat-form-input
						[id]="'dados-basicos-tipo-descricao-' + i"
						[control]="telefone.get('descricaoTelefone')"
						[label]="
							(configCliFormValidation.configForms['descricaoTelefone']
								?.indicator || '') +
							'Descrição do telefone ' +
							(i + 1)
						"
						errorMsg="Campo obrigatório"
						i18n-errorMsg="@@dados-basicos:descricao-telefone-required"
						i18n-label="@@dados-basicos:descricao-telefone"
						maxlength="20"></pacto-cat-form-input>
				</div>
				<div class="col-sm-12 col-lg-1 col-md-1 d-flex align-items-center">
					<span
						[id]="'dados-basicos-telefone-btn-remover-' + i"
						(click)="removeTelefone(i)"
						class="actionable">
						<i class="pct pct-trash-2 cor-hellboy05"></i>
					</span>
				</div>
			</div>

			<div *ngIf="permiteEditar">
				<div>
					<button
						id="dados-basicos-btn-add-telefone"
						(click)="addTelefone()"
						ds3-text-button
						i18n="@@dados-basicos:adicionar-telefone">
						<i class="pct pct-plus"></i>
						Adicionar outro telefone
					</button>
				</div>
			</div>

			<hr class="separator" />
		</ng-container>

		<ng-container *ngIf="recursoEndereco">
			<div
				*ngFor="let endereco of formDosEnderecos.controls; let i = index"
				class="col-12 row">
				<div class="col-10">
					<span class="sesi-title">Endereço</span>
				</div>
				<div class="col-sm-12 col-lg-4 col-md-4">
					<pacto-cat-form-input
						[id]="'dados-basicos-endereco-cep-' + i"
						[control]="endereco.get('cep')"
						[label]="
							(configCliFormValidation.configForms['cep']?.indicator || '') +
							'CEP'
						"
						errorMsg="Por favor, digite um CEP válido."
						errorMsg="Campo obrigatório"
						i18n-errorMsg="@@dados-basicos:cep-required"
						placeholder="CEP"
						(input)="applyCepMask($event)"></pacto-cat-form-input>
				</div>

				<div class="col-sm-12 col-lg-3 col-md-3 align-self-center">
					<ds3-checkbox
						[id]="'dados-basicos-endereco-consulta-cep-' + i"
						(click)="consultaCep(i)"
						style="margin-top: 27px">
						Utilizar CEP para preencher endereço
					</ds3-checkbox>
				</div>
				<div
					class="col-sm-2 col-lg-2 col-md-2 d-flex align-items-center"
					style="margin-top: 27px">
					<span
						[id]="'dados-basicos-endereco-btn-remover-' + i"
						(click)="removeEndereco(i)"
						class="actionable">
						<i class="pct pct-trash-2 cor-hellboy05"></i>
					</span>
				</div>

				<div class="col-sm-12 col-lg-4 col-md-4">
					<pacto-cat-form-input
						[id]="'dados-basicos-endereco-endereco-' + i"
						[control]="endereco.get('endereco')"
						[label]="
							(configCliFormValidation.configForms['endereco']?.indicator ||
								'') + 'Endereço'
						"
						errorMsg="Campo obrigatório"
						i18n-errorMsg="@@dados-basicos:endereco-required"
						label="Endereço"
						placeholder="Endereço"></pacto-cat-form-input>
				</div>
				<div class="col-sm-12 col-lg-4 col-md-4">
					<pacto-cat-form-input
						[id]="'dados-basicos-endereco-bairro-' + i"
						[control]="endereco.get('bairro')"
						[label]="
							(configCliFormValidation.configForms['bairro']?.indicator || '') +
							'Bairro'
						"
						errorMsg="Campo obrigatório"
						i18n-errorMsg="@@dados-basicos:bairro-required"
						i18n-label="@@dados-basicos:bairro"
						placeholder="Bairro"></pacto-cat-form-input>
				</div>
				<div class="col-sm-12 col-lg-4 col-md-4">
					<pacto-cat-form-input
						[id]="'dados-basicos-endereco-numero-' + i"
						[control]="endereco.get('numero')"
						[label]="
							(configCliFormValidation.configForms['numero']?.indicator || '') +
							'Número'
						"
						errorMsg="Campo obrigatório"
						i18n-errorMsg="@@dados-basicos:numero-required"
						i18n-label="@@dados-basicos:numero"
						label="Número"
						placeholder="Número"></pacto-cat-form-input>
				</div>
				<div class="col-sm-12 col-lg-8 col-md-8">
					<pacto-cat-form-input
						[id]="'dados-basicos-endereco-complemento-' + i"
						[control]="endereco.get('complemento')"
						[label]="
							(configCliFormValidation.configForms['complemento']?.indicator ||
								'') + 'Complemento'
						"
						errorMsg="Campo obrigatório"
						i18n-errorMsg="@@dados-basicos:complemento-required"
						i18n-label="@@dados-basicos:complemento"
						label="Complemento"
						placeholder="Complemento"></pacto-cat-form-input>
				</div>
				<div class="col-sm-12 col-lg-4 col-md-4">
					<pacto-cat-form-select-filter
						[id]="'dados-basicos-endereco-tipo-' + i"
						[addEmptyOption]="
							!configCliFormValidation.configForms['tipoEndereco']?.indicator
						"
						[control]="endereco.get('tipoEndereco')"
						[label]="
							(configCliFormValidation.configForms['tipoEndereco']?.indicator ||
								'') + 'Tipo de endereço'
						"
						[options]="listaTipoEndereco"
						errorMsg="Campo obrigatório"
						i18n-errorMsg="@@dados-basicos:tipo-endereco-required"
						i18n-label="@@dados-basicos:tipo-endereco"
						idKey="codigo"
						labelKey="descricao"></pacto-cat-form-select-filter>
				</div>
			</div>

			<div *ngIf="permiteEditar">
				<div>
					<button
						id="dados-basicos-btn-add-endereco"
						(click)="addEndereco()"
						ds3-text-button
						i18n="@@dados-basicos:adicionar-endereco">
						<i class="pct pct-plus"></i>
						Adicionar endereço alternativo
					</button>
				</div>
			</div>

			<hr class="separator" />
		</ng-container>
	</div>

	<div *ngIf="form.get('tipoPessoa').value == 0" class="row">
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-input
				id="dados-basicos-nome-mae"
				[control]="form.get('nomeMae')"
				[label]="
					(configCliFormValidation.configForms['nomeMae']?.indicator || '') +
					'Nome da mãe'
				"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@dados-basicos:nome-mae-required"
				i18n-label="@@dados-basicos:nome-mae"
				i18n-placeholder="@@dados-basicos:nome-mae-responsavel"
				placeholder="Nome completo do mãe ou responsável"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-2 col-md-2">
			<pacto-cat-form-input
				id="dados-basicos-cpf-mae"
				[control]="form.get('cpfMae')"
				[label]="
					(configCliFormValidation.configForms['cpfMae']?.indicator || '') +
					'CPF da mãe'
				"
				[textMask]="{ mask: cpfMask }"
				errorMsg="Campo obrigatório"
				errorMsg="Inválido!"
				i18n-errorMsg="@@dados-basicos:cpf-mae-required"
				placeholder="000.000.000-00"
				required></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-2 col-md-2">
			<pacto-cat-form-input
				id="dados-basicos-rg-mae"
				[control]="form.get('rgMae')"
				[label]="
					(configCliFormValidation.configForms['rgMae']?.indicator || '') +
					'RG da mãe'
				"
				errorMsg="Campo obrigatório"
				errorMsg="Forneça um RG no formato (13.131.312-3)."
				i18n-errorMsg="@@dados-basicos:rg-mae-required"
				maxlength="20"
				placeholder="000000000"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-input
				id="dados-basicos-email-mae"
				[control]="form.get('emailMae')"
				i18n-label="@@dados-basicos:email-mae"
				label="E-mail da mãe"
				placeholder="E-mail da mãe"></pacto-cat-form-input>
		</div>

		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-input
				id="dados-basicos-nome-pai"
				[control]="form.get('nomePai')"
				[label]="
					(configCliFormValidation.configForms['nomePai']?.indicator || '') +
					'Nome do pai'
				"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@dados-basicos:nome-pai-required"
				i18n-label="@@dados-basicos:nome-pai"
				i18n-placeholder="@@dados-basicos:nome-pai-responsavel"
				label="Nome do pai"
				placeholder="Nome completo do pai ou responsável"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-2 col-md-2">
			<pacto-cat-form-input
				id="dados-basicos-cpf-pai"
				[control]="form.get('cpfPai')"
				[label]="
					(configCliFormValidation.configForms['cpfPai']?.indicator || '') +
					'CPF do pai'
				"
				[textMask]="{ mask: cpfMask }"
				errorMsg="Campo obrigatório"
				errorMsg="Inválido!"
				i18n-errorMsg="@@dados-basicos:cpf-pai-required"
				placeholder="000.000.000-00"
				required></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-2 col-md-2">
			<pacto-cat-form-input
				id="dados-basicos-rg-pai"
				[control]="form.get('rgPai')"
				[label]="
					(configCliFormValidation.configForms['rgPai']?.indicator || '') +
					'RG do pai'
				"
				errorMsg="Campo obrigatório"
				errorMsg="Forneça um RG no formato (13.131.312-3)."
				i18n-errorMsg="@@dados-basicos:rg-pai-required"
				maxlength="20"
				placeholder="000000000"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-3">
			<pacto-cat-form-input
				id="dados-basicos-email-pai"
				[control]="form.get('emailPai')"
				i18n-label="@@dados-basicos:email-pai"
				label="E-mail do pai"
				placeholder="E-mail do pai"></pacto-cat-form-input>
		</div>

		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-input
				id="dados-basicos-nome-responsavel-financeiro"
				[control]="form.get('nomeRespFinanceiro')"
				[label]="
					(configCliFormValidation.configForms['nomeRespFinanceiro']
						?.indicator || '') + 'Nome do resp. financeiro'
				"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@dados-basicos:nome-pai-required"
				placeholder="Nome completo do responsável financeiro"
				label="Responsável Financeiro"
				i18n-label="@@dados-basicos:nome-responsavel-financeiro"
				i18n-placeholder="
					@@dados-basicos:nome-responsavel-financeiro"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-2 col-md-2">
			<pacto-cat-form-input
				id="dados-basicos-cpf-responsavel-financeiro"
				[control]="form.get('cpfRespFinanceiro')"
				errorMsg="Inválido!"
				[label]="
					(configCliFormValidation.configForms['cpfRespFinanceiro']
						?.indicator || '') + 'CPF do resp. financeiro'
				"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@dados-basicos:cpf-responsavel-financeiro-required"
				placeholder="000.000.000-00"
				[textMask]="{ mask: cpfMask }"
				required></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-2 col-md-2">
			<pacto-cat-form-input
				id="dados-basicos-rg-responsavel-financeiro"
				[control]="form.get('rgRespFinanceiro')"
				[label]="
					(configCliFormValidation.configForms['rgRespFinanceiro']?.indicator ||
						'') + 'RG do resp. financeiro'
				"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@dados-basicos:rg-responsavel-financeiro-required"
				errorMsg="Forneça um RG no formato (13.131.312-3)."
				placeholder="000000000"
				maxlength="20"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-3">
			<pacto-cat-form-input
				id="dados-basicos-email-responsavel-financeiro"
				[control]="form.get('emailRespFinanceiro')"
				placeholder="E-mail do Responsável Financeiro"
				i18n-label="@@dados-basicos:email-responsavel-financeiro"
				label="E-mail do resp. financeiro"></pacto-cat-form-input>
		</div>

		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-datepicker
				[control]="form.get('dataNascimentoResponsavel')"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@dados-basicos:data-nasc-responsavel-required"
				i18n-label="@@dados-basicos:data-nascimento-responsavel"
				id="data-nascimento-responsavel"
				label="Data de nascimento do responsável"></pacto-cat-form-datepicker>
		</div>

		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-input
				id="dados-basicos-contato-emergencia"
				[control]="form.get('contatoEmergencia')"
				[label]="
					(configCliFormValidation.configForms['contatoEmergencia']
						?.indicator || '') + 'Nome do contato de emergência'
				"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@dados-basicos:contato-emergencia-required"
				i18n-label="@@dados-basicos:contato-emergencia"
				i18n-placeholder="@@dados-basicos:nome-contato"
				placeholder="Nome do contato"></pacto-cat-form-input>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-input
				id="dados-basicos-telefone-emergencia"
				[control]="form.get('telefoneEmergencia')"
				[label]="
					(configCliFormValidation.configForms['telefoneEmergencia']
						?.indicator || '') + 'Telefone/celular de emergência'
				"
				[textMask]="{ mask: foneMask }"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@dados-basicos:telefone-celular-required"
				i18n-label="@@dados-basicos:telefone"
				placeholder="+55 (00) 00000-0000"></pacto-cat-form-input>
		</div>

		<div class="col-sm-12 col-lg-6 col-md-6">
			<pacto-cat-form-select-filter
				id="dados-basicos-pessoa-responsavel"
				[label]="'Pessoa responsável'"
				[control]="form.get('pessoaResponsavel')"
				[endpointUrl]="responsavelGroupsUrl"
				[paramBuilder]="selectBuilderResp"
				idKey="codigo"
				labelKey="nome"
				[labelFn]="formatResponsavelLabel"></pacto-cat-form-select-filter>
		</div>
		<div
			class="col-sm-12 col-lg-2 col-md-2"
			style="align-items: center; display: grid"
			*ngIf="form.get('pessoaResponsavel') != null">
			<pacto-cat-button
				id="dados-basicos-btn-remover-responsavel"
				(click)="removerResponsavelCPF()"
				label="Remover Responsável"
				size="LARGE"
				style="margin-right: 10px"
				type="PRIMARY"
				width="100%"></pacto-cat-button>
		</div>
		<div
			class="col-sm-12 col-lg-4 col-md-4"
			style="align-items: center; display: grid;}">
			<pacto-cat-checkbox
				id="check-utilizar-responsavel-pagamento"
				label="Utilizar responsável do cliente para pagamentos"
				[control]="
					form.get('utilizarResponsavelPagamento')
				"></pacto-cat-checkbox>
		</div>
	</div>

	<div *ngIf="isConfigSesiCe">
		<hr class="separator" />
		<span class="sesi-title">Integração SESI</span>
		<div class="row">
			<div class="col-sm-12 col-lg-3 col-md-3">
				<pacto-cat-form-select-filter
					id="dados-basicos-necessidades-especiais-sesi"
					[control]="form.get('necessidadesEspeciaisSesiCe')"
					[options]="opcoesNecessidadesEspeciais"
					[paramBuilder]="selectBuilder"
					idKey="id"
					label="Necessidades Especiais"
					labelKey="label"></pacto-cat-form-select-filter>
			</div>
			<div class="col-sm-12 col-lg-3 col-md-3">
				<pacto-cat-form-datepicker
					id="dados-basicos-data-validade-cadastro-sesi"
					[control]="form.get('dataValidadeCadastroSesiCe')"
					id="data-vidade-cadastro-sesi"
					label="Data de validade do cadastro"></pacto-cat-form-datepicker>
			</div>
			<div
				*ngIf="!isCadastroEmpresaHabilitado"
				class="col-sm-12 col-lg-3 col-md-3">
				<pacto-cat-form-input
					id="dados-basicos-razao-social-empresa-sesi"
					[control]="form.get('razaoSocialEmpresaSesiCe')"
					label="Empresa"
					placeholder="Insira nome fantasia da empresa"></pacto-cat-form-input>
			</div>
			<div
				class="d-flex align-items-center"
				*ngIf="isCadastroEmpresaHabilitado">
				<div class="col" style="width: 280px">
					<pacto-cat-form-select-filter
						[control]="form.get('empresaFornecedor')"
						[endpointUrl]="listaEmpresaSesiUrl"
						[paramBuilder]="empresaSesiFornecedorParamBuilder"
						idKey="codigo"
						label="Empresa"
						labelKey="nomeFantasia"></pacto-cat-form-select-filter>
				</div>
				<div class="ml-1">
					<span (click)="removerEmpresaFornecedor()" class="actionable">
						<i class="pct pct-trash-2 cor-hellboy05"></i>
					</span>
				</div>
			</div>

			<div class="col-sm-12 col-lg-3 col-md-2">
				<pacto-cat-form-select-filter
					id="dados-basicos-status-matricula-sesi"
					[control]="form.get('statusMatriculaSesiCe')"
					[options]="opcoesStatusMatricula"
					[paramBuilder]="selectBuilder"
					idKey="id"
					label="Status da Matrícula"
					labelKey="label"></pacto-cat-form-select-filter>
			</div>
		</div>
	</div>

	<hr class="separator" />

	<span class="sesi-title">Adicionais</span>
	<div class="row">
		<div *ngIf="permiteEditar" class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-multi-select-filter
				id="dados-basicos-grupo"
				[control]="form.get('grupoClientes')"
				[endpointUrl]="clientGroupsUrl"
				[paramBuilder]="selectBuilder"
				[resposeParser]="responseParser"
				errorMsg="Selecione grupo de clientes"
				i18n-label="@@dados-basicos:grupo-clientes"
				idKey="codigo"
				label="Grupo de clientes"
				labelKey="descricao"></pacto-cat-form-multi-select-filter>
		</div>

		<div *ngIf="permiteEditar" class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-multi-select-filter
				id="dados-basicos-classificacao"
				[control]="form.get('classificacaoCliente')"
				[endpointUrl]="clientClassificacaoUrl"
				[paramBuilder]="selectBuilder"
				[resposeParser]="responseParser"
				errorMsg="Selecione classificação de cliente"
				i18n-label="@@dados-basicos:classificacao-cliente"
				i18n-placeholder="@@dados-basicos:classificacao-cliente"
				idKey="codigo"
				label="Classificação de cliente"
				labelKey="nome"></pacto-cat-form-multi-select-filter>
		</div>
		<div class="col-sm-12 col-lg-4 col-md-4">
			<pacto-cat-form-select-filter
				id="dados-basicos-categoria"
				[addEmptyOption]="
					!configCliFormValidation.configForms['categoria']?.indicator
				"
				[control]="form.get('categoria')"
				[label]="
					(configCliFormValidation.configForms['categoria']?.indicator || '') +
					'Categoria'
				"
				[options]="categories$ | async"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@acesso-catraca:categoria-required"
				i18n-label="@@acesso-catraca:categoria"
				idKey="codigo"
				labelKey="nome"></pacto-cat-form-select-filter>
		</div>

		<div class="col-sm-12 col-lg-12 col-md-12">
			<pacto-cat-form-input
				id="dados-basicos-webpage"
				[control]="form.get('webPage')"
				[label]="
					(configCliFormValidation.configForms['webPage']?.indicator || '') +
					'WebPage'
				"
				errorMsg="Campo obrigatório"
				i18n-errorMsg="@@dados-basicos:web-page-required"
				i18n-label="@@dados-basicos:web-page"
				i18n-placeholder="@@dados-basicos:web-page"
				maxlength="50"></pacto-cat-form-input>
		</div>
	</div>

	<div class="d-flex justify-content-end">
		<pacto-cat-button
			id="dados-basicos-btn-salvar"
			(click)="saveConfigFn()"
			[hidden]="!configCliFormValidation.isClientOnly"
			i18n-label="@@config-pessoa:btn-salvar"
			label="Salvar alterações"
			size="LARGE"
			type="PRIMARY"></pacto-cat-button>
	</div>
</pacto-cat-card-plain>

<pacto-traducoes-xingling #traducoes>
	<span i18n-label="@@config-dados-basicos:amasiado" xingling="amasiado">
		Amasiado(a)
	</span>
	<span i18n-label="@@config-dados-basicos:casado" xingling="casado">
		Casado(a)
	</span>
	<span i18n-label="@@config-dados-basicos:divorciado" xingling="divorciado">
		Divorciado(a)
	</span>
	<span i18n-label="@@config-dados-basicos:separado" xingling="separado">
		Separado(a)
	</span>
	<span i18n-label="@@config-dados-basicos:solteiro" xingling="solteiro">
		Solteiro(a)
	</span>
	<span
		i18n-label="@@config-dados-basicos:uniao-estavel"
		xingling="uniao-estavel">
		União estável
	</span>
	<span i18n-label="@@config-dados-basicos:viuvo" xingling="viuvo">
		Viúvo(a)
	</span>
	<span
		i18n-label="@@config-dados-basicos:pessoa-fisica"
		xingling="pessoa-fisica">
		Física
	</span>
	<span
		i18n-label="@@config-dados-basicos:pessoa-juridica"
		xingling="pessoa-juridica">
		Jurídica
	</span>
	<span i18n-label="@@config-dados-basicos:estrangeira" xingling="estrangeira">
		Estrangeira
	</span>
	<span
		i18n-label="@@config-dados-basicos:sexo-nao-declarar"
		xingling="sexo-nao-declarar">
		Não declarar
	</span>
	<span
		i18n-label="@@config-dados-basicos:sexo-feminino"
		xingling="sexo-feminino">
		Feminino
	</span>
	<span
		i18n-label="@@config-dados-basicos:sexo-masculino"
		xingling="sexo-masculino">
		Masculino
	</span>
	<span i18n-label="@@config-dados-basicos:agenero" xingling="agenero">
		Agênero
	</span>
	<span
		i18n-label="@@config-dados-basicos:genero-feminino"
		xingling="genero-feminino">
		Feminino
	</span>
	<span
		i18n-label="@@config-dados-basicos:genero-masculino"
		xingling="genero-masculino">
		Masculino
	</span>
	<span i18n-label="@@config-dados-basicos:nao-binario" xingling="nao-binario">
		Não-binário
	</span>
	<span
		i18n-label="@@config-dados-basicos:telefone-celular"
		xingling="telefone-celular">
		Celular
	</span>
	<span
		i18n-label="@@config-dados-basicos:telefone-comercial"
		xingling="telefone-comercial">
		Comercial
	</span>
	<span
		i18n-label="@@config-dados-basicos:telefone-emergencia"
		xingling="telefone-emergencia">
		Emergência
	</span>
	<span i18n-label="@@config-dados-basicos:fax" xingling="fax">Fax</span>
	<span i18n-label="@@config-dados-basicos:recado" xingling="recado">
		Recado
	</span>
	<span i18n-label="@@config-dados-basicos:residencial" xingling="residencial">
		Residencial
	</span>
	<span xingling="cep-nao-informado">Por favor informe o CEP</span>

	<span
		i18n="@@config-dados-basicos:cli-emails-obrigatorio"
		xingling="cli-emails-obrigatorio">
		Campos obrigatórios dos e-mails adicionados não foram informados
	</span>
	<span
		i18n="@@config-dados-basicos:cli-emails-invalidos"
		xingling="cli-emails-invalidos">
		Há e-mails inválidos
	</span>
	<span
		i18n="@@config-dados-basicos:cli-telefones-obrigatorio"
		xingling="cli-telefones-obrigatorio">
		Campos obrigatórios dos telefones adicionados não foram informados
	</span>
</pacto-traducoes-xingling>

<ng-template #estadoCidadePaisTempl>
	<div class="col-4">
		<pacto-cat-form-select-filter
			id="dados-basicos-pais"
			[addEmptyOption]="!configCliFormValidation.configForms['pais']?.indicator"
			[control]="form.get('pais')"
			[endpointUrl]="paisUrl"
			[label]="
				(configCliFormValidation.configForms['pais']?.indicator || '') + 'País'
			"
			[paramBuilder]="paisParamBuilder"
			errorMsg="Campo obrigatório"
			i18n-errorMsg="@@dados-basicos:pais-required"
			i18n-label="@@dados-basicos:pais"
			idKey="codigo"
			labelKey="nome"></pacto-cat-form-select-filter>
	</div>
	<div class="col-4">
		<pacto-cat-form-select-filter
			id="dados-basicos-estado"
			[addEmptyOption]="
				!configCliFormValidation.configForms['estado']?.indicator
			"
			[control]="form.get('estado')"
			[endpointUrl]="estadoUrl"
			[label]="
				(configCliFormValidation.configForms['estado']?.indicator || '') +
				'Estado'
			"
			[paramBuilder]="estadoParamBuilder"
			errorMsg="Campo obrigatório"
			i18n-errorMsg="@@dados-basicos:estado-required"
			i18n-label="@@dados-basicos:estado"
			idKey="codigo"
			labelKey="descricao"></pacto-cat-form-select-filter>
	</div>
	<div class="col-4">
		<pacto-cat-form-select-filter
			id="dados-basicos-cidade"
			[addEmptyOption]="
				!configCliFormValidation.configForms['cidade']?.indicator
			"
			[control]="form.get('cidade')"
			[endpointUrl]="cidadeUrl"
			[label]="
				(configCliFormValidation.configForms['cidade']?.indicator || '') +
				'Cidade'
			"
			[paramBuilder]="cidadeParamBuilder"
			errorMsg="Campo obrigatório"
			i18n-errorMsg="@@dados-basicos:cidade-required"
			i18n-label="@@dados-basicos:cidade"
			idKey="codigo"
			labelKey="nome"></pacto-cat-form-select-filter>
	</div>
</ng-template>

<ng-template #campoCnpjClienteSesi>
	<div *ngIf="isConfigSesiSc && isApresentarCnpjClienteSesi" class="col-4">
		<pacto-cat-form-input
			id="dados-basicos-cnpj-sesi-2"
			[control]="form.get('cnpjClienteSesi')"
			[label]="
				(configCliFormValidation.configForms['cnpjClienteSesi']?.indicator ||
					'') + 'CNPJ Sesi Indústria'
			"
			[textMask]="{ mask: cnpjMask }"
			placeholder="CNPJ Sesi Indústria"></pacto-cat-form-input>
	</div>
</ng-template>
