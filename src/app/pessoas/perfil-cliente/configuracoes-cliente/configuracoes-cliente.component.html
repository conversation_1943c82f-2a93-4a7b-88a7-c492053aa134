<pacto-cat-layout-v2>
	<div class="nav-aux">
		<a
			class="top-navigation"
			id="voltar-alunos"
			[routerLink]="['/pessoas', 'perfil-v2', matricula]">
			<i class="pct pct-arrow-left"></i>
			<span i18n="@@configuracoes-cliente:title-config">Edição do Cliente</span>
		</a>
	</div>

	<ng-container *ngIf="form.invalid && showErrorMessages">
		<div class="cli-notification cli-not-error">
			<span class="cli-not-icon"><i class="pct pct-alert-triangle"></i></span>
			<span i18n="@@configuracoes-cliente:required-fields-not-filled">
				Existem campos que são obrigatórios e não foram preenchidos.
			</span>
		</div>
	</ng-container>

	<pacto-perfil-cliente-header-v2
		[isMiniPerfil]="true"></pacto-perfil-cliente-header-v2>

	<pacto-cat-tabs-transparent #tabs [ngClass]="{ 'loading-blur': loading }">
		<ng-template
			pactoTabTransparent
			i18n-label="@@configuracoes-cliente:dados-basicos"
			label="Dados Básicos"
			tabIcone="pct pct-alert-triangle cor-hellboy-pri"
			[showTabIcon]="form.get('abaDadosBasicos').invalid && showErrorMessages"
			id="cli-tab-dados-basicos">
			<pacto-dados-basicos
				[codigoPessoa]="codigo"
				[permiteEditar]="permiteEditarCliente()"
				[form]="form.get('abaDadosBasicos')"
				(saveConfig)="saveConfig()"></pacto-dados-basicos>
		</ng-template>

		<ng-template
			pactoTabTransparent
			i18n-label="@@configuracoes-cliente:acesso-catraca"
			label="Acesso presencial	"
			tabIcone="pct pct-alert-triangle cor-hellboy-pri"
			[showTabIcon]="form.get('abaAcessoCatraca').invalid && showErrorMessages"
			id="cli-tab-acesso-catraca">
			<pacto-acesso-catraca
				[form]="form.get('abaAcessoCatraca')"
				[permiteEditar]="permiteEditarCliente()"
				(saveConfig)="saveConfig()"></pacto-acesso-catraca>
		</ng-template>

		<ng-template
			pactoTabTransparent
			i18n-label="@@configuracoes-cliente:dados-financeiros"
			label="Dados financeiros"
			tabIcone="pct pct-alert-triangle cor-hellboy-pri"
			[showTabIcon]="
				form.get('abaDadosFinanceiros').invalid && showErrorMessages
			"
			id="cli-tab-dados-financeiro">
			<pacto-dados-financeiros
				[form]="form.get('abaDadosFinanceiros')"
				[permiteEditar]="permiteEditarCliente()"
				(saveConfig)="saveConfig()"></pacto-dados-financeiros>
		</ng-template>

		<!-- TODO Para pessoa jurídica deve apresentar outra aba de dependenters -->
		<ng-template
			pactoTabTransparent
			i18n-label="@@configuracoes-cliente:familiares"
			label="Familiares"
			*ngIf="
				form.get('abaDadosBasicos').get('tipoPessoa').value == 0 &&
				recursoFamiliar
			"
			tabIcone="pct pct-alert-triangle cor-hellboy-pri"
			[showTabIcon]="form.get('abaFamiliares').invalid && showErrorMessages"
			id="cli-tab-familiares">
			<pacto-familiares
				(saveConfig)="saveConfig()"
				[permiteEditar]="permiteEditarCliente()"
				[recursoFamiliar]="recursoFamiliar"
				[form]="form.get('abaFamiliares')"></pacto-familiares>
		</ng-template>

		<ng-template
			pactoTabTransparent
			i18n-label="@@configuracoes-cliente:dependente"
			label="Dependentes"
			*ngIf="form.get('abaDadosBasicos').get('tipoPessoa').value == 1"
			tabIcone="pct pct-alert-triangle cor-hellboy-pri"
			[showTabIcon]="form.get('abaDependentes').invalid && showErrorMessages"
			id="cli-tab-dependentes">
			<pacto-dependentes
				(saveConfig)="saveConfig()"
				[permiteEditar]="permiteEditarCliente()"
				[form]="form.get('abaDependentes')"></pacto-dependentes>
		</ng-template>

		<ng-template
			pactoTabTransparent
			i18n-label="@@configuracoes-cliente:vinculos"
			label="Vinculos"
			*ngIf="recursoVinculo"
			tabIcone="pct pct-alert-triangle cor-hellboy-pri"
			[showTabIcon]="form.get('abaVinculos').invalid && showErrorMessages"
			id="cli-tab-vinculos">
			<pacto-configuracoes-vinculos
				[recursoVinculo]="recursoVinculo"
				[codigoCliente]="dadosPessoais?.codigoCliente"
				(saveConfig)="saveConfig()"
				[form]="form.get('abaVinculos')"></pacto-configuracoes-vinculos>
		</ng-template>

		<ng-template
			pactoTabTransparent
			i18n-label="@@configuracoes-cliente:acesso-catraca"
			label="Pacto"
			tabIcone="pct pct-alert-triangle cor-hellboy-pri"
			[showTabIcon]="form.get('abaAcessoCatraca').invalid && showErrorMessages"
			id="cli-tab-pacto">
			<pacto-pacto [form]="form.get('abaAcessoCatraca')"></pacto-pacto>
		</ng-template>

		<!-- as 3 ultimas abas apenas vão aparecer caso o usuário seja algo além de cliente -->

		<ng-template
			pactoTabTransparent
			i18n-label="@@configuracoes-cliente:rh"
			label="RH"
			tabIcone="pct pct-alert-triangle cor-hellboy-pri"
			[showTabIcon]="form.get('abaRh').invalid && showErrorMessages"
			*ngIf="!userIsOnlyClient"
			id="cli-tab-rh">
			<pacto-configuracoes-rh
				(saveConfig)="saveConfig()"
				[form]="form.get('abaRh')"></pacto-configuracoes-rh>
		</ng-template>

		<ng-template
			pactoTabTransparent
			i18n-label="@@configuracoes-cliente:dados-acessos"
			label="Acesso"
			tabIcone="pct pct-alert-triangle cor-hellboy-pri"
			[showTabIcon]="form.get('abaAcesso').invalid && showErrorMessages"
			*ngIf="!userIsOnlyClient"
			id="cli-tab-dados-acessos">
			<pacto-dados-acessos
				(saveConfig)="saveConfig()"
				[form]="form.get('abaAcesso')"></pacto-dados-acessos>
		</ng-template>

		<ng-template
			pactoTabTransparent
			*ngIf="!userIsOnlyClient && false"
			i18n-label="@@configuracoes-cliente:replicar-empresa"
			label="Replicar empresa"
			id="cli-tab-replicar-empresa"></ng-template>
	</pacto-cat-tabs-transparent>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@configuracoes-cliente:invalid-tabs-form"
		xingling="invalid-forms">
		Dados invalidos nos formulários. Verifique o(s) dados no(s) formulário(s)
		da(s) aba(s):
	</span>
	<span i18n="@@configuracoes-cliente:dados-basicos" xingling="abaDadosBasicos">
		Dados Básicos
	</span>
	<span
		i18n="@@configuracoes-cliente:acesso-catraca"
		xingling="abaAcessoCatraca">
		Acesso a catraca
	</span>
	<span
		i18n="@@configuracoes-cliente:dados-financeiros"
		xingling="abaDadosFinanceiros">
		Dados financeiros
	</span>
	<span i18n="@@configuracoes-cliente:familiares" xingling="abaFamiliares">
		Familiares
	</span>
	<span i18n="@@configuracoes-cliente:vinculos" xingling="abaVinculos">
		Vinculos
	</span>
	<span i18n="@@configuracoes-cliente:rh" xingling="abaRh">RH</span>
	<span i18n="@@configuracoes-cliente:dados-acessos" xingling="abaAcesso">
		Acesso
	</span>
	<span
		i18n="@@configuracoes-cliente:replicar-empresa"
		xingling="replicar-empresa">
		Replicar empresa
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-email-obrigatorio"
		xingling="cli-email-obrigatorio">
		Ao menos um e-mail deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-cpf-obrigatorio"
		xingling="cli-cpf-obrigatorio">
		CPF deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-telefone-obrigatorio"
		xingling="cli-telefone-obrigatorio">
		Ao menos um telefone deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-enderecos-obrigatorio"
		xingling="cli-enderecos-obrigatorio">
		Campos obrigatórios dos endereços adicionados não foram informados
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-nenhum-endereco-informado"
		xingling="cli-nenhum-endereco-informado">
		Ao menos um endereço deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-nome-caracter-especial"
		xingling="nome-caracter-especial">
		O campo NOME não pode conter caracteres especiais (!@#$%&*}{{
			"{"
		}}}][?:><,|"+).
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-rg-obrigatorio"
		xingling="cli-rg-obrigatorio">
		RG deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-bairro-obrigatorio"
		xingling="cli-bairro-obrigatorio">
		O bairro deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-categoria-obrigatorio"
		xingling="cli-categoria-obrigatorio">
		A categoria deve ser informada.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-cep-obrigatorio"
		xingling="cli-cep-obrigatorio">
		O CEP deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-cidade-obrigatorio"
		xingling="cli-cidade-obrigatorio">
		A cidade deve ser informada.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-contatoEmergencia-obrigatorio"
		xingling="cli-contatoEmergencia-obrigatorio">
		O contato de emergência deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-cpfMae-obrigatorio"
		xingling="cli-cpfMae-obrigatorio">
		O CPF da mãe ou responsável deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-cpfPai-obrigatorio"
		xingling="cli-cpfPai-obrigatorio">
		O CPF do pai ou responsável deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-dataNasc-obrigatorio"
		xingling="cli-dataNasc-obrigatorio">
		A data de nascimento deve ser informada.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-descricaoTelefone-obrigatorio"
		xingling="cli-descricaoTelefone-obrigatorio">
		A descrição do telefone deve ser informada.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-endereco-obrigatorio"
		xingling="cli-endereco-obrigatorio">
		O endereço deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-complemento-obrigatorio"
		xingling="cli-complemento-obrigatorio">
		O complemento do endereço deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-estado-obrigatorio"
		xingling="cli-estado-obrigatorio">
		O estado deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-estadoCivil-obrigatorio"
		xingling="cli-estadoCivil-obrigatorio">
		O estado civil deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-genero-obrigatorio"
		xingling="cli-genero-obrigatorio">
		O gênero deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-grauInstrucao-obrigatorio"
		xingling="cli-grauInstrucao-obrigatorio">
		O grau de instrução deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-matricula-obrigatorio"
		xingling="cli-matricula-obrigatorio">
		A matrícula deve ser informada.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-nome-obrigatorio"
		xingling="cli-nome-obrigatorio">
		O nome deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-nomeMae-obrigatorio"
		xingling="cli-nomeMae-obrigatorio">
		O nome da mãe ou responsável deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-nomePai-obrigatorio"
		xingling="cli-nomePai-obrigatorio">
		O nome do pai ou responsável deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-numero-obrigatorio"
		xingling="cli-numero-obrigatorio">
		O número deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-pais-obrigatorio"
		xingling="cli-pais-obrigatorio">
		O país deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-profissao-obrigatorio"
		xingling="cli-profissao-obrigatorio">
		A profissão deve ser informada.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-rg-obrigatorio"
		xingling="cli-rg-obrigatorio">
		O RG deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-rgMae-obrigatorio"
		xingling="cli-rgMae-obrigatorio">
		O RG Mãe deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-rgPai-obrigatorio"
		xingling="cli-rgPai-obrigatorio">
		O RG Pai deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-sexo-obrigatorio"
		xingling="cli-sexo-obrigatorio">
		O sexo deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-telefoneEmergencia-obrigatorio"
		xingling="cli-telefoneEmergencia-obrigatorio">
		O telefone de emergência deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-webPage-obrigatorio"
		xingling="cli-webPage-obrigatorio">
		A WebPage deve ser informada.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-autenticarGoogle-obrigatorio"
		xingling="cli-autenticarGoogle-obrigatorio">
		O campo Autenticar Google deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-cref-obrigatorio"
		xingling="cli-cref-obrigatorio">
		O CREF deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-enderecoCorrespondencia-obrigatorio"
		xingling="cli-enderecoCorrespondencia-obrigatorio">
		Deve ser marcado se o endereço é ou não para correspondência.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-dataCadastro-obrigatorio"
		xingling="cli-dataCadastro-obrigatorio">
		A data de cadastro deve ser informada.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-nacionalidade-obrigatorio"
		xingling="cli-nacionalidade-obrigatorio">
		A nacionalidade deve ser informada.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-nacionalidade-obrigatorio"
		xingling="cli-naturalidade-obrigatorio">
		A naturalidade deve ser informada.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-orgaoEmissor-obrigatorio"
		xingling="cli-orgaoEmissor-obrigatorio">
		O orgão emissor deve ser informado.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-porcentagemComissao-obrigatorio"
		xingling="cli-porcentagemComissao-obrigatorio">
		A porcentagem de comissão para o colaborador deve ser informada.
	</span>
	<span
		i18n="@@configuracoes-cliente:cli-produtoDefaultPersonal-obrigatorio"
		xingling="cli-produtoDefaultPersonal-obrigatorio">
		O produto default do personal trainer deve ser informado.
	</span>
	<span i18n="@@configuracoes-cliente:server-error" xingling="server-error">
		Houve um erro no servidor. Favor contatar o suporte para auxílio!
	</span>
	<span
		i18n="@@configuracoes-cliente:tipo-endereco-obrigatorio"
		xingling="tipo-endereco-obrigatorio">
		O tipo de endereço deve ser informado!
	</span>

	<span
		i18n="@@configuracoes-cliente:cli-sem-vinculo-consultor"
		xingling="cli-sem-vinculo-consultor">
		O cliente deve ter pelo menos um vínculo de consultor.
	</span>

	<span
		xingling="cli-cpf-rne-passport-um-obrigatorio"
		i18n="@@configuracoes-cliente:cli-cpf-rne-passport-um-obrigatorio">
		Você deve informar o RNE ou passaporte.
	</span>
	<span
		xingling="usuario-sem-permissao-alterar-idvindi"
		i18n="@@configuracoes-cliente:usuario-sem-permissao-alterar-idvindi">
		Alteração do IdVindi não permitida pois o usuário não possui a permissão:
		"2.76 - Permite alterar IdVindi nos dados do cliente"
	</span>
</pacto-traducoes-xingling>
