import { FormArray, FormControl, FormGroup, Validators } from "@angular/forms";

export const FormGroupDadosBasicos = new FormGroup({
	codigoPessoa: new FormControl(),
	nome: new FormControl("", Validators.required),
	fotoBase64OrUrl: new FormControl(),
	dataNasc: new FormControl(""),
	tipoDePessoa: new FormControl(""),
	tipoPessoa: new FormControl(""),
	cpf: new FormControl("", [
		Validators.pattern(
			/^([0-9]{3}\.?[0-9]{3}\.?[0-9]{3}\-?[0-9]{2}|[0-9]{2}\.?[0-9]{3}\.?[0-9]{3}\/?[0-9]{4}\-?[0-9]{2})$/
		),
		Validators.minLength(14),
		Validators.maxLength(14),
	]),
	rg: new FormControl(""),
	rgOrgao: new FormControl(""),
	rgUf: new FormControl(""),
	sexo: new FormControl(),
	genero: new FormControl(""),
	estadoCivil: new FormControl(""),
	estrangeira: new FormControl(),
	rne: new FormControl(""),
	email: new FormControl(),
	passaporte: new FormControl(""),
	profissao: new FormControl(""),
	grauInstrucao: new FormControl(""),
	nacionalidade: new FormControl(""),
	naturalidade: new FormControl(""),
	enderecos: new FormArray([]),
	categoria: new FormControl(),
	// // check if filled in
	// // if filled in make fields required
	// enderecoAlternativo: new FormGroup({
	//     endereco: new FormControl(''),
	//     bairro: new FormControl(''),
	//     numero: new FormControl(''),
	//     complemento: new FormControl(''),
	//     tipoEndereco: new FormControl(''),
	//     cidade: new FormControl(''),
	//     estado: new FormControl(''),
	//     cep: new FormControl(''),
	//     pessoa: new FormControl('')
	// }),
	nomePai: new FormControl(""),
	cpfPai: new FormControl("", [
		Validators.pattern(
			/^([0-9]{3}\.?[0-9]{3}\.?[0-9]{3}\-?[0-9]{2}|[0-9]{2}\.?[0-9]{3}\.?[0-9]{3}\/?[0-9]{4}\-?[0-9]{2})$/
		),
		Validators.minLength(14),
		Validators.maxLength(14),
	]),
	rgPai: new FormControl(""),
	nomeMae: new FormControl(""),
	cpfMae: new FormControl("", [
		Validators.pattern(
			/^([0-9]{3}\.?[0-9]{3}\.?[0-9]{3}\-?[0-9]{2}|[0-9]{2}\.?[0-9]{3}\.?[0-9]{3}\/?[0-9]{4}\-?[0-9]{2})$/
		),
		Validators.minLength(14),
		Validators.maxLength(14),
	]),
	rgMae: new FormControl(""),
	contatoEmergencia: new FormControl(""),
	telefoneEmergencia: new FormControl(""),
	telefone: new FormControl(""),
	grupoClientes: new FormControl(),
	classificacaoCliente: new FormControl(),
	dataCadastro: new FormControl(),
	emitirNotaNoNomeDeTerceiro: new FormControl(),
	emitirNotaNoNomeDoAluno: new FormControl(),
	inscricaoEstadual: new FormControl(),
	cnpj: new FormControl(),
	inscricaoMunicipal: new FormControl(),
	cfdf: new FormControl(),
	pais: new FormControl(),
	estado: new FormControl(),
	cidade: new FormControl(),
	webPage: new FormControl(),
	emailPai: new FormControl("", Validators.email),
	emailMae: new FormControl("", Validators.email),
	dataNascimentoResponsavel: new FormControl(),
	pessoaResponsavel: new FormControl(),
	utilizarResponsavelPagamento: new FormControl(false),
	//campos sesi-ce
	empresaFornecedor: new FormControl(),
	statusMatriculaSesiCe: new FormControl(""),
	razaoSocialEmpresaSesiCe: new FormControl(""),
	necessidadesEspeciaisSesiCe: new FormControl(""),
	dataValidadeCadastroSesiCe: new FormControl(),
	nomeResponsavelEmpresa: new FormControl(),
	cpfResponsavelEmpresa: new FormControl(),
	nomeRegistro: new FormControl(),
	cnpjClienteSesi: new FormControl(),
	nomeRespFinanceiro: new FormControl(""),
	cpfRespFinanceiro: new FormControl(""),
	rgRespFinanceiro: new FormControl(""),
	emailRespFinanceiro: new FormControl(""),
});
