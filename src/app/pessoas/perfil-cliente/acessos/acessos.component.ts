import { <PERSON><PERSON><PERSON>, AmChartsService } from "@amcharts/amcharts3-angular";
import {
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	AdmCoreApiAcessoClienteService,
	AdmCoreApiClienteService,
	InformacoesDeAcesso,
} from "adm-core-api";
import { AdmLegadoAutorizarAcessoService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { LayoutNavigationService } from "pacto-layout";
import { PlataformaModulo } from "src/app/microservices/client-discovery/client-discovery.model";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import { VerAcessosComponent } from "./ver-acessos/ver-acessos.component";

@Component({
	selector: "pacto-acessos",
	templateUrl: "./acessos.component.html",
	styleUrls: ["./acessos.component.scss"],
})
export class AcessosComponent implements OnInit {
	public codigoPessoa: number;
	public codigoCliente: number;
	public acessos: any = [];
	public permiteVerRelatorioDeAcessos: boolean = false;
	public informacoesDeAcessos: InformacoesDeAcesso = null;

	@ViewChild(RelatorioComponent, { static: true })
	public relatorio: RelatorioComponent;
	@ViewChild("cellSituacao", { static: true })
	public cellSituacao: TemplateRef<any>;
	data = false;

	constructor(
		private readonly admCoreApiAcessoCliente: AdmCoreApiAcessoClienteService,
		private readonly activatedRoute: ActivatedRoute,
		private readonly admCoreApiCliente: AdmCoreApiClienteService,
		private readonly cd: ChangeDetectorRef,
		private readonly snotifyService: SnotifyService,
		private readonly ngbModal: NgbModal,
		private readonly rest: RestService,
		private readonly layoutNavigationService: LayoutNavigationService,
		private readonly autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private readonly sessionService: SessionService,
		private readonly amCharts: AmChartsService
	) {}

	ngOnInit() {
		this.relatorio.tableTitle = "Períodos de acesso";
		this.relatorio.table = new PactoDataGridConfig({
			rowClick: false,
			pagination: true,
			columns: [],
		});

		this.admCoreApiCliente
			.dadosPessoais(this.activatedRoute.snapshot.params["aluno-matricula"])
			.subscribe((res) => {
				this.codigoPessoa = res.codigoPessoa;
				this.codigoCliente = res.codigoCliente;
				this.obterUltimosAcessosNoMes(res.codigoPessoa);
				this.obterUltimosAcessosNaSemana(res.codigoPessoa);
				this.obterInformacoes(res.codigoPessoa);
				this.construirRelatorio(res.codigoPessoa);
			});

		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"TotalizadorFrequenciaRel",
				"6.09 - Relatórios de Acessos - Lista e Totalizador"
			)
			.subscribe(
				() => {
					this.permiteVerRelatorioDeAcessos = true;
				},
				() => {
					this.permiteVerRelatorioDeAcessos = false;
				},
				() => this.cd.detectChanges()
			);
	}

	public verRelatorioDeAcessos(): void {
		if (!this.permiteVerRelatorioDeAcessos) {
			return;
		}

		const url = this.layoutNavigationService.createNovaPlataformaUrl(
			PlataformaModulo.NZW,
			"/adm/relatorios/relatorios-acessos/lista-acessos"
		);
		window.open(`${url}`, "_self");
	}

	public verAcessos(): void {
		const modal = this.ngbModal.open(VerAcessosComponent, {
			centered: true,
			size: "lg",
		});
		modal.componentInstance.codigoPessoa = this.codigoPessoa;
	}

	private obterInformacoes(codigoPessoa: number): void {
		this.admCoreApiAcessoCliente.obterInformacoes(codigoPessoa).subscribe(
			(res) => {
				if (res.content) {
					this.informacoesDeAcessos = res.content;
				} else {
					this.snotifyService.error(res.meta.message);
				}
			},
			({ error }) => {
				this.snotifyService.error(error.meta.message);
			},
			() => this.cd.detectChanges()
		);
	}

	private obterUltimosAcessosNaSemana(codigoPessoa: number): void {
		this.admCoreApiAcessoCliente
			.obterUltimosAcessosNaSemana(codigoPessoa)
			.subscribe(
				(res) => {
					if (res.content) {
						this.amCharts.makeChart(
							"graficoUltimosAcessosNaSemana",
							this.gerarGraficoDeUltimosAcessosNaSemana(res.content)
						);
					} else {
						this.snotifyService.error(res.meta.message);
					}
				},
				({ error }) => {
					this.snotifyService.error(error.meta.message);
				},
				() => this.cd.detectChanges()
			);
	}

	private obterUltimosAcessosNoMes(codigo) {
		this.admCoreApiAcessoCliente.obterUltimosAcessosNoMes(codigo).subscribe(
			(res) => {
				if (res.content) {
					this.amCharts.makeChart(
						"graficoUltimosAcessosNoMes",
						this.gerarGraficoDeUltimosAcessosNoMes(res.content)
					);
				} else {
					this.snotifyService.error(res.meta.message);
				}
			},
			({ error }) => {
				this.snotifyService.error(error.meta.message);
			},
			() => this.cd.detectChanges()
		);
	}

	private gerarGraficoDeUltimosAcessosNaSemana(
		dataProvider: number[]
	): AmChart {
		return {
			type: "serial",
			titles: [
				{
					size: 15.319,
					text: "Acessos nas últimas semanas",
					color: "#51555A",
					fontSize: 15.319,
					fontWeight: 700,
				},
			],
			color: "#90949A",
			fontFamily: "Nunito Sans",
			fontSize: 13.404,
			fontWeight: 400,
			categoryField: "category",
			valueAxes: [{ integersOnly: true }],
			graphs: [
				{
					valueField: "value",
					type: "line",
					lineColor: "#1998FC",
					lineThickness: 2,
					bullet: "round",
				},
			],
			categoryAxis: {
				gridPosition: "start",
				gridAlpha: 0,
			},
			dataProvider: dataProvider.map(
				(value: number, index: number, array: number[]) => {
					return {
						category: `Semana ${(array.length - index)
							.toString()
							.padStart(2, "0")}`,
						value: value,
					};
				}
			),
		};
	}

	private gerarGraficoDeUltimosAcessosNoMes(dataProvider: number[]): AmChart {
		return {
			type: "serial",
			titles: [
				{
					size: 15.319,
					text: "Acessos nos últimos meses",
					color: "#51555A",
					fontSize: 15.319,
					fontWeight: 700,
				},
			],
			color: "#90949A",
			fontFamily: "Nunito Sans",
			fontSize: 13.404,
			fontWeight: 400,
			categoryField: "category",
			valueAxes: [{ integersOnly: true }],
			graphs: [
				{
					valueField: "value",
					type: "line",
					lineColor: "#1998FC",
					lineThickness: 2,
					bullet: "round",
				},
			],
			categoryAxis: {
				gridPosition: "start",
				gridAlpha: 0,
			},
			dataProvider: dataProvider.map(
				(value: number, index: number, array: number[]) => {
					return {
						category: `Mês ${(array.length - index)
							.toString()
							.padStart(2, "0")}`,
						value: value,
					};
				}
			),
		};
	}

	private construirRelatorio(codigoPessoa: number) {
		this.relatorio.table = new PactoDataGridConfig({
			rowClick: false,
			pagination: true,
			endpointUrl: this.rest.buildFullUrlAdmCore(
				`clientes/${codigoPessoa}/periodo-acesso`
			),
			dataAdapterFn: (serveData) => {
				this.data = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "contrato",
					titulo: "Contrato",
					ordenavel: false,
					visible: true,
					inputType: null,
					valueTransform(v) {
						return v || "-";
					},
				},
				{
					nome: "dataInicioAcesso",
					titulo: "Início do acesso",
					ordenavel: false,
					visible: true,
					inputType: null,
					valueTransform(v) {
						return v ? new Date(v).toLocaleDateString() : "-";
					},
				},
				{
					nome: "dataFinalAcesso",
					titulo: "Final do acesso",
					ordenavel: false,
					visible: true,
					inputType: null,
					valueTransform(v) {
						return v ? new Date(v).toLocaleDateString() : "-";
					},
				},
				{
					nome: "tipoAcesso",
					titulo: "Tipo de acesso",
					ordenavel: false,
					visible: true,
					inputType: null,
					valueTransform(v, r) {
						const tipoAcesso = {
							CA: "Contrato Ativo",
							BO: "Bônus",
							TO: "Tolerância",
							TD: "Transfêrencia de dias",
							PL: "Passe Livre - Free Pass",
							AA: "Aula Avulsa",
							DI: "Diária",
							RT: "Retorno Trancamento",
							RR: "Retorno Retroativo",
							RA: "Retorno - Atestado",
							TR: "Trancado",
							AT: "Atestado",
							PF: "Pendente Financeiro",
							CN: "Cancelado",
							CR: "Férias",
							RC: "Retorno-Férias",
						};
						let descricao = tipoAcesso.hasOwnProperty(v) ? tipoAcesso[v] : "-";
						if (v === "PL" && r.token) {
							descricao = `Wellhub - Token: ${r.token}`;
						}
						return descricao;
					},
				},
				{
					nome: "legenda",
					titulo: "Status",
					ordenavel: false,
					visible: true,
					inputType: null,
					celula: this.cellSituacao,
				},
			],
		});

		this.relatorio.reloadData();
	}
}
