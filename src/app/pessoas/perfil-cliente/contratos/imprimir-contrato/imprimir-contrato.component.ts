import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { CadastroAuxApiAditivoService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { ClientDiscoveryService } from "../../../../microservices/client-discovery/client-discovery.service";

@Component({
	selector: "pacto-imprimir-contrato",
	templateUrl: "./imprimir-contrato.component.html",
	styleUrls: ["./imprimir-contrato.component.scss"],
})
export class ImprimirContratoComponent implements OnInit {
	public form: FormGroup = new FormGroup({
		contratoAditivo: new FormControl("", [Validators.required]),
	});

	public codigoContrato;
	public contratosAditivos: any[] = [];

	constructor(
		private readonly dialog: NgbActiveModal,
		private readonly notificationService: SnotifyService,
		private readonly sessionService: SessionService,
		private readonly clientDiscoveryService: ClientDiscoveryService
	) {}

	ngOnInit() {}

	cancel() {
		this.dialog.close();
	}

	imprimirContrato() {
		if (!this.form.get("contratoAditivo").value) {
			this.notificationService.error("Informe um contrato");
			return;
		}

		const contratoAditivo = JSON.parse(this.form.get("contratoAditivo").value);
		const aditivo =
			contratoAditivo && contratoAditivo.codigoAditivo
				? contratoAditivo.codigoAditivo
				: null;

		const trueUrl = `${
			this.clientDiscoveryService.getUrlMap().zwUrl
		}/faces/VisualizarContrato?k=${this.sessionService.chave}&c=${
			this.codigoContrato
		}&telaNova=true&aditivo=${aditivo}`;
		window.open(trueUrl, "_blank");
	}
}
