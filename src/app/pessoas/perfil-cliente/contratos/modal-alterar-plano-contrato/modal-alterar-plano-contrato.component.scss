@import "projects/ui/assets/ds3/colors.var";
@import "projects/ui/assets/ds3/colors";

.div-itens {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 2fr;
	grid-column-gap: 10px;
	border-top: 1px solid #c4c4c4;
}

.div-geral {
	padding: 16px;
}

.text-info {
	width: 100%;
	height: 100%;
	color: #797d86 !important;
	font-size: 12px;
	font-family: Nunito Sans;
	font-weight: 400;
	line-height: 15px;
	word-wrap: break-word;
}

.div-text-obs {
	padding: 10px 0 0 0;
}

.div-text-info {
	display: grid;
	grid-template-columns: 1fr 6fr;
	padding: 10px 0;
	border-top: 1px solid #c4c4c4;
	margin-top: 10px;
}

.div-inferior-btn {
	padding: 10px 0 0 0;
	text-align: end;
}

.item-titulo {
	color: #555;
	font-size: 16px;
	font-family: Nunito Sans;
	font-weight: bold;
	line-height: 24px;
	word-wrap: break-word;
	margin-top: 0.8rem;
}

.item-valor {
	color: #51555a;
	font-size: 14px;
	font-family: Nunito Sans;
	font-weight: 400;
	line-height: 24px;
	word-wrap: break-word;
}

.div-text-obs-info {
	padding: 5px 0 10px 0;
	margin-bottom: 2px;
}

.info-atencao,
.icone {
	color: #e10505;
	font-size: 14px;
	font-family: Nunito Sans;
	font-weight: 400;
	line-height: 17.5px;
	word-wrap: break-word;
}

.div-novo-layout {
	border: 1px solid #cce5ff;
	border-radius: 8px;
	background: white;
	padding: 1.5rem;
	margin-top: 1rem;
}

.div-novo-layout .row {
	display: flex;
	flex-wrap: wrap;
}

.div-novo-layout .col-6 {
	flex: 0 0 50%;
	max-width: 50%;
	padding: 0.5rem;
}

.div-novo-layout .col-12 {
	flex: 0 0 100%;
	max-width: 100%;
	padding: 0.5rem;
	text-align: center;
	margin-top: 1rem;
}

.comparativo-plano {
	padding: 10px;
	border: 1px solid #dee2e6;
	border-radius: 8px;
	background-color: #fff;
	font-family: Arial, sans-serif;
	font-size: 14px;
}

.comparativo-linhas {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	gap: 16px;
}

.coluna-titulo {
	flex: 1;
	font-weight: bold;
	color: #333;
}

.coluna-dados {
	flex: 1;
	color: #6c757d;
}

.titulo-item,
.dados-item {
	margin-bottom: 12px;
	height: 16px;
}

.nome-plano {
	font-weight: bold;
	font-size: 14px;
	margin-bottom: 8px;
	word-break: break-word;
	white-space: normal;
	color: #495057;
	max-width: 90%;
	line-height: 1.2;
}

.linha-separadora {
	margin: 20px 0;
	border-top: 1px solid #dee2e6;
}

.economia-texto {
	font-size: 16px;
	color: #6c757d;
	text-align: left;
}

.valor-positivo,
.economia {
	color: green;
}

.valor-negativo,
.acrescimo {
	color: $feedbackLoss03;
}

.tabela-comparativo {
	width: 100%;
	border-collapse: collapse;
	margin-bottom: 20px;
}

.tabela-comparativo th,
.tabela-comparativo td {
	border: none;
	padding: 4px 6px;
	text-align: left;
	line-height: 0.9;
}

.tabela-comparativo th {
	background-color: transparent;
	font-weight: bold;
}

.modal-body-scroll {
	max-height: 70vh;
	overflow-y: auto;
	overflow-x: hidden;
	padding: 1rem;
}
.div-itens {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 2fr;
	grid-column-gap: 10px;
	border-top: 1px solid #c4c4c4;
}

.div-geral {
	padding: 16px;
}

.text-info {
	width: 100%;
	height: 100%;
	color: #797d86 !important;
	font-size: 12px;
	font-family: Nunito Sans;
	font-weight: 400;
	line-height: 15px;
	word-wrap: break-word;
}

.div-text-obs {
	padding: 10px 0 0 0;
}

.div-text-info {
	display: grid;
	grid-template-columns: 1fr 6fr;
	padding: 10px 0 10px 0;
	border-top: 1px solid #c4c4c4;
	margin-top: 5px;
}

.div-inferior-btn {
	padding: 10px 0 0 0;
	text-align: end;
}

.item-titulo {
	color: #51555a;
	font-size: 16px;
	font-family: Nunito Sans;
	font-weight: 700;
	line-height: 24px;
	word-wrap: break-word;
}

.item-valor {
	color: #51555a;
	font-size: 14px;
	font-family: Nunito Sans;
	font-weight: 400;
	line-height: 24px;
	word-wrap: break-word;
}

.div-info-atencao {
	background: #fee6e6;
	padding: 8px;
	border-radius: 5px;
	margin-bottom: 1px;
}

.info-atencao {
	color: #e10505;
	font-size: 14px;
	font-family: Nunito Sans;
	font-weight: 400;
	line-height: 17.5px;
	word-wrap: break-word;
	padding: 2px 16px;
}

.icone {
	color: #e10505;
}
.info-box {
	display: flex;
	align-items: center; /* Corrige o alinhamento vertical */
	background-color: #f9f9f9;
	padding: 12px 16px;
	border-radius: 6px;
	color: #4a4a4a;
	font-size: 14px;
	line-height: 1.5;
}

.icon-info {
	font-size: 18px; /* Levemente maior para dar presença */
	color: #9c9c9c;
	margin-right: 8px;
	flex-shrink: 0;
}

.info-text {
	flex: 1;
}
.div-text-obs-info {
	width: 100%;
	margin-bottom: 2px;
}

.text-info-extra {
	display: flex;
	align-items: center; /* Centraliza ícone e texto verticalmente */
	background-color: #f9f9f9;
	border-radius: 6px;
	padding: 2px 18px;
	font-size: 14px;
	color: #4a4a4a;
}

.text-info-extra i.pct {
	font-size: 18px;
	color: #5c7080; /* ou #9c9c9c, conforme estilo do sistema */
	margin-right: 8px;
	flex-shrink: 0;
}
