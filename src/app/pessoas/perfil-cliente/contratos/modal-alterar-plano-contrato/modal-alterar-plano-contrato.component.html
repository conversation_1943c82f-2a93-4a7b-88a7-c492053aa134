<div class="div-geral modal-body-scroll">
	<pacto-cat-select
		[control]="form.get('plano')"
		[items]="lista"
		idKey="codigo"
		label="Escolha um novo plano"
		labelKey="descricao"></pacto-cat-select>

	<div class="col-12 div-text-obs">
		<div class="text-info">Mostra apenas planos recorrentes e sem turma.</div>
	</div>
	<div class="col-12 div-text-obs">
		<div class="text-info">
			O plano escolhido deve ser compatível com o plano atual.
		</div>
	</div>

	<div *ngIf="dadosCalculo" class="div-dados">
		<p></p>

		<div class="comparativo-plano">
			<table class="tabela-comparativo">
				<thead>
					<tr>
						<th></th>
						<!-- Vazio para alinhar -->
						<th></th>
						<th></th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td class="nome-plano">Plano</td>
						<td>{{ dadosCalculo?.planoAnterior }}</td>
						<td>{{ dadosCalculo?.planoNovo }}</td>
					</tr>
					<tr>
						<td class="nome-plano">Valor do plano</td>
						<td>{{ dadosCalculo?.valorPlanoAnterior | currency : "BRL" }}</td>
						<td>{{ dadosCalculo?.plano | currency : "BRL" }}</td>
					</tr>
					<tr>
						<td class="nome-plano">Valor da mensalidade</td>
						<td>{{ dadosCalculo?.mensalidadeAnterior | currency : "BRL" }}</td>
						<td>{{ dadosCalculo?.mensalidade | currency : "BRL" }}</td>
					</tr>
					<tr>
						<td class="nome-plano">Valor da manutenção</td>
						<td>{{ dadosCalculo?.manutencaoAnterior | currency : "BRL" }}</td>
						<td>{{ dadosCalculo?.manutencao | currency : "BRL" }}</td>
					</tr>
					<tr>
						<td class="nome-plano">Valor total</td>
						<td>{{ dadosCalculo?.valorFinalAnterior | currency : "BRL" }}</td>
						<td>{{ dadosCalculo?.valorFinal | currency : "BRL" }}</td>
					</tr>
					<tr>
						<td class="nome-plano">Data início</td>
						<td>
							{{
								dadosCalculo?.dataInicioAnterior | date : "dd/MM/yyyy" : "UTC"
							}}
						</td>
						<td>
							{{ dadosCalculo?.dataInicio | date : "dd/MM/yyyy" : "UTC" }}
						</td>
					</tr>
					<tr>
						<td class="nome-plano">Data de fim</td>
						<td>
							{{ dadosCalculo?.dataFimAnterior | date : "dd/MM/yyyy" : "UTC" }}
						</td>
						<td>{{ dadosCalculo?.dataFim | date : "dd/MM/yyyy" : "UTC" }}</td>
					</tr>
					<tr>
						<td class="nome-plano">Duração</td>
						<td>{{ dadosCalculo?.duracaoMesesAnterior }}</td>
						<td>{{ dadosCalculo?.duracaoMesesNovo }}</td>
					</tr>
					<tr>
						<td class="nome-plano">Dia da parcela</td>
						<td>{{ dadosCalculo?.diaCobrancaParcelasAnterior }}</td>
						<td>{{ dadosCalculo?.diaCobrancaParcelas }}</td>
					</tr>
				</tbody>
			</table>

			<div class="linha-separadora"></div>

			<div class="economia-texto">
				<strong>
					Esta troca irá gerar uma
					<span>
						{{
							dadosCalculo?.tipoEconomia === "ECONOMIA"
								? "economia"
								: "acréscimo"
						}}
					</span>
					no valor de
					<span
						class="valor-economia"
						[ngClass]="{
							'valor-positivo': dadosCalculo?.tipoEconomia === 'ECONOMIA',
							'valor-negativo': dadosCalculo?.tipoEconomia === 'ACRESCIMO'
						}">
						{{ dadosCalculo?.economia | currency : "BRL" }}
					</span>
				</strong>
			</div>
		</div>

		<div class="div-text-obs-info">
			<div class="text-info-extra">
				<i class="pct pct-info"></i>
				Ao realizar a transferência de plano, as próximas parcelas em aberto
				serão atualizadas com os novos valores acima. O horario será o mesmo do
				antigo contrato.
			</div>
		</div>

		<div class="col-12 div-info-atencao">
			<div class="info-atencao">
				<i class="pct pct-alert-triangle icone"></i>
				Esta mudança não pode ser realizada duas vezes em um mesmo dia.
			</div>
		</div>
	</div>

	<div class="col-12 div-inferior-btn text-lg-right">
		<pacto-cat-button
			(click)="close()"
			id="obj-btn-cancelar"
			label="Cancelar"
			size="NORMAL"
			type="OUTLINE"></pacto-cat-button>

		<pacto-cat-button
			(click)="salvar()"
			id="obj-btn-confirmar"
			label="Confirmar alteração de plano"
			size="NORMAL"
			style="padding-left: 10px"
			type="ALERT_DELETE"></pacto-cat-button>
	</div>
</div>
