import { DatePipe } from "@angular/common";
import {
	ChangeDetectorRef,
	Component,
	HostBinding,
	Inject,
	LOCALE_ID,
	OnInit,
	TemplateRef,
	ViewChild,
	ViewEncapsulation,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { LocalStorageSessionService } from "@base-core/rest/local-storage-session.service";
import {
	AdmCoreApiClienteRestricaoService,
	AdmCoreApiClienteService,
	AdmCoreApiContratoService,
	AdmCoreApiNegociacaoService,
	ClienteDadosPessoais,
	ClienteRestricao,
} from "adm-core-api";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
} from "adm-legado-api";
import { AdmRestService } from "projects/adm/src/app/adm-rest.service";
import {
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
	ConfirmDialogDeleteComponent,
} from "ui-kit";
import { ClientDiscoveryService } from "../../../../microservices/client-discovery/client-discovery.service";
import { AlterarVencimentoParcelasComponent } from "../alterar-vencimento-parcelas/alterar-vencimento-parcelas.component";
import { ModalContratosRenovarERematricularComponent } from "../components/modal-contratos-renovar-e-rematricular/modal-contratos-renovar-e-rematricular.component";
import { EnviarContratoEmailComponent } from "../enviar-contrato-email/enviar-contrato-email.component";
import { ContratoDocumentosComponent } from "./contrato-documentos/contrato-documentos.component";
import { isNullOrUndefinedOrEmpty, PlataformaModulo } from "sdk";
import { LayoutNavigationService, PermissaoService } from "pacto-layout";
import { ModalService } from "@base-core/modal/modal.service";
import { ModalAfastamentoContratoDependenteComponent } from "../modal-afastamento-contrato-dependente/modal-afastamento-contrato-dependente.component";
import { ModalHistoricoAfastamentoContratoDependenteComponent } from "../modal-historico-afastamento-contrato-dependente/modal-historico-afastamento-contrato-dependente.component";
import { CaptalizePipe } from "@base-shared/pipe/captalize.pipe";
import { SnotifyService } from "ng-snotify";
import { ModalClienteRestricaoMsgBloqueioComponent } from "../../perfil-cliente-header-v2/modal-cliente-restricao-msg-bloqueio/modal-cliente-restricao-msg-bloqueio.component";
import { NotificarClienteRestricaoService } from "../../perfil-cliente-header-v2/modal-cliente-restricao/notificar-cliente-restricao.service";
import { Subscription } from "rxjs";
import { PerfilClienteStateNotifierService } from "../../perfil-cliente-header-v2/services/perfil-cliente-state-notifier.service";
import { switchMap } from "rxjs/operators";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ModalAtualizarContratoComponent } from "../modal-atualizar-contrato/modal-atualizar-contrato.component";
import { CadastroAuxApiModeloContratoService } from "cadastro-aux-api";
import { ToastrService } from "ngx-toastr";
import moment from "moment";
import { ImprimirContratoComponent } from "../imprimir-contrato/imprimir-contrato.component";
import { CadastroAuxApiAditivoService } from "cadastro-aux-api";

@Component({
	selector: "pacto-contratos-container",
	templateUrl: "./contratos-container.component.html",
	styleUrls: [
		"./contratos-container.component.scss",
		"../contract-common.scss",
	],
	encapsulation: ViewEncapsulation.None,
})
export class ContratosContainerComponent implements OnInit {
	@HostBinding("class.pacto-tela-aluno-contratos-container")
	styleEncapsulation = true;
	@ViewChild("codigoContrato", { static: false })
	public codigoContratoTitulo: TemplateRef<any>;

	@ViewChild("tipoContrato", { static: false })
	public tipoContratoTitulo: TemplateRef<any>;

	@ViewChild("responsavel", { static: false })
	public responsavelTitulo: TemplateRef<any>;

	@ViewChild("situacao", { static: false })
	public situacaoTitulo: TemplateRef<any>;

	@ViewChild("colunaSituacao", { static: true })
	public colunaSituacao;

	@ViewChild("colunaPlano", { static: true })
	public colunaPlano;

	@ViewChild("colunaVigenciaDe", { static: true })
	public colunaVigenciaDe;
	@ViewChild("colunaVigenciaAte", { static: true })
	public colunaVigenciaAte;
	@ViewChild("colunaVigenciaAteAjustada", { static: true })
	public colunaVigenciaAteAjustada;

	@ViewChild("colunaDependenteTitular", { static: true })
	public colunaDependenteTitular;

	@ViewChild("colunaDependenteContrato", { static: true })
	public colunaDependenteContrato;

	@ViewChild("colunaDependenteInicio", { static: true })
	public colunaDependenteInicio;

	@ViewChild("colunaDependenteSituacao", { static: true })
	public colunaDependenteSituacao;

	@ViewChild("colunaDependenteFinal", { static: true })
	public colunaDependenteFinal;

	@ViewChild("colunaRenovarOuRematricular", { static: true })
	public colunaRenovarOuRematricular;

	@ViewChild("tableContrato", { static: false })
	public tableContrato: RelatorioComponent;
	public table: PactoDataGridConfig;

	@ViewChild("tableContratoDependente", { static: false })
	public tableContratoDependente: RelatorioComponent;
	public tableDependente: PactoDataGridConfig;
	private matricula: string;
	public dadosPessoais: ClienteDadosPessoais;
	public empresa;
	naoPermiteNovoContratoDeOutraEmpresa: boolean = true;
	public ableNovoContrato: boolean = true;
	public apresentarMenuContrato: boolean = false;
	public apresentarAtualizarContrato: boolean = false;
	public mutavelContrato: boolean = false;
	data = false;
	dataDependente = false;
	clienteRestricoes: Array<ClienteRestricao> = [];
	customActionsList = ["addAtualizaContrato", "addContrato"];
	notifier: Subscription =
		this.notificarClienteRestricaoService.subjectNotifier.subscribe(
			(notified) => {
				this.carregarClienteRestricoes();
			}
		);

	constructor(
		private readonly localStorageService: LocalStorageSessionService,
		private readonly cd: ChangeDetectorRef,
		private readonly route: ActivatedRoute,
		private readonly router: Router,
		private readonly admCoreApiClienteService: AdmCoreApiClienteService,
		private readonly admCoreApiNegociacaoService: AdmCoreApiNegociacaoService,
		private readonly telaClienteService: AdmLegadoTelaClienteService,
		private readonly autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private readonly admCoreApiContratoService: AdmCoreApiContratoService,
		private readonly admRest: AdmRestService,
		private readonly sessionService: SessionService,
		private readonly dialogService: DialogService,
		private readonly notificationService: SnotifyService,
		private readonly clientDiscoveryService: ClientDiscoveryService,
		private readonly datePipe: DatePipe,
		private readonly layoutNavigationService: LayoutNavigationService,
		private readonly pactoModal: ModalService,
		private activatedRoute: ActivatedRoute,
		private permissaoService: PermissaoService,
		private readonly admCoreApiClienteRestricaoService: AdmCoreApiClienteRestricaoService,
		private readonly notificarClienteRestricaoService: NotificarClienteRestricaoService,
		private readonly perfilClienteStateNotifierService: PerfilClienteStateNotifierService,
		private aditivoService: CadastroAuxApiAditivoService,
		@Inject(LOCALE_ID) private readonly locale: string,
		private ngbModal: NgbModal,
		private modeloContratoService: CadastroAuxApiModeloContratoService,
		private toastrService: ToastrService
	) {
		this.localStorageService.setLocalStorageItem("tabAluno", "contratos");
	}

	ngOnInit() {
		this.carregarDados();
		if (this.matricula) {
			this.admCoreApiClienteService
				.dadosPessoais(this.matricula)
				.subscribe((response) => {
					this.dadosPessoais = response;
					this.empresa = this.dadosPessoais.empresa;

					this.telaClienteService
						.apresentarMenuContrato(
							this.sessionService.chave,
							Number(this.sessionService.empresaId),
							this.dadosPessoais.codigoCliente,
							{}
						)
						.subscribe((data) => {
							this.apresentarMenuContrato = data.content;
							this.cd.detectChanges();
						});
				});
		}
		this.route.queryParams.subscribe((v) => {
			if (
				this.activatedRoute.snapshot.queryParams["adicionar"] ||
				v.adicionar
			) {
				this.novoContrato();
			}
		});
	}

	public carregarContratos(codigo) {
		this.table = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrlAdmCore(
				`contratos/by-matricula/${codigo}`
			),
			dataAdapterFn: (serveData) => {
				this.data = serveData.totalElements > 0 ? true : false;
				serveData.content.every((v) => {
					v.situacao =
						moment(v.vigenciaDe).isAfter(moment(), "day") && v.situacao === "AT"
							? "FT"
							: v.situacao;
				});
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "codigo",
					titulo: this.codigoContratoTitulo
						? this.codigoContratoTitulo
						: "Contrato",
					visible: true,
				},
				{
					nome: "tipo",
					titulo: this.tipoContratoTitulo ? this.tipoContratoTitulo : "Tipo",
					visible: true,
				},
				{
					nome: "responsavelLancamento",
					titulo: this.responsavelTitulo
						? this.responsavelTitulo
						: "Responsável",
					visible: true,
					valueTransform: (v) => {
						if (v) {
							const captalizePipe = new CaptalizePipe();
							return captalizePipe.transform(v);
						}
						return v;
					},
				},
				{
					nome: "plano",
					titulo: "Plano",
					visible: true,
					celula: this.colunaPlano,
				},
				{
					nome: "vigenciaDe",
					titulo: "Início",
					visible: true,
					celula: this.colunaVigenciaDe,
				},
				{
					nome: "vigenciaAteAjustada",
					titulo: "Fim",
					visible: true,
					celula: this.colunaVigenciaAteAjustada,
				},
				{
					nome: "situacao",
					titulo: this.situacaoTitulo ? this.situacaoTitulo : "Situação",
					visible: true,
					celula: this.colunaSituacao,
				},
				{
					nome: "renovarRematricular",
					titulo: "",
					visible: true,
					ordenavel: false,
					celula: this.colunaRenovarOuRematricular,
					width: "100px",
				},
			],
			actions: [
				{
					nome: "verDetalhamento",
					iconClass: "pct pct-eye cor-azulim05 pct-icon-mr-16",
					tooltipText: "Detalhamento de contrato",
				},
				{
					nome: "imprimirContrato",
					iconClass: "pct pct-printer cor-azulim05 pct-icon-mr-16",
					tooltipText: "Imprimir",
				},
				{
					nome: "enviarContratoPorEmail",
					iconClass: "pct pct-send cor-azulim05 pct-icon-mr-16",
					tooltipText: "Envio de contrato",
				},
				{
					nome: "verDocumentosAssinados",
					iconClass: "pct pct-archive cor-azulim05 pct-icon-mr-16",
					tooltipText: "Documentos assinados",
				},
				{
					nome: "alterarVencimento",
					iconClass: "pct pct-calendar cor-azulim05 pct-icon-mr-16",
					tooltipText: "Alterar vencimento",
				},
			],
		});
	}

	public carregarContratosDependente(matricula) {
		this.tableDependente = new PactoDataGridConfig({
			endpointUrl: this.admRest.buildFullUrlAdmCore(
				`contratos/dependente/by-matricula/${matricula}`
			),
			dataAdapterFn: (serveData) => {
				this.dataDependente = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "contrato",
					titulo: "Contrato",
					visible: true,
					celula: this.colunaDependenteContrato,
				},
				{
					nome: "Titular",
					titulo: "Titular",
					visible: true,
					celula: this.colunaDependenteTitular,
				},
				{
					nome: "dataInicio",
					titulo: "Início",
					visible: true,
					celula: this.colunaDependenteInicio,
				},
				{
					nome: "dataFinalAjustada",
					titulo: "Fim",
					visible: true,
					celula: this.colunaDependenteFinal,
				},
				{
					nome: "situacao",
					titulo: "Situação",
					visible: true,
					celula: this.colunaDependenteSituacao,
				},
				// {
				// 	nome: 'posicaoDependente',
				// 	titulo: 'Dependência',
				// 	visible: true
				// }
			],
			actions: [
				{
					nome: "ferias",
					iconClass: "pct pct-sun cor-azulim05 pct-icon-mr-16",
					tooltipText: "Lançar férias",
					actionFn: (row) => this.abrirAfastamentoContratoDependente(row, true),
				},
				{
					nome: "atestado",
					iconClass: "pct pct-shield-health cor-azulim05 pct-icon-mr-16",
					tooltipText: "Lançar atestado",
					actionFn: (row) =>
						this.abrirAfastamentoContratoDependente(row, false),
				},
				{
					nome: "historicoAfastamentos",
					iconClass: "pct pct-wod cor-azulim05 pct-icon-mr-16",
					tooltipText: "Afastamentos lançados",
					actionFn: (row) =>
						this.abrirHistoricoAfastamentoContratoDependente(row),
				},
			],
		});
	}

	public carregarDados() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.carregarContratos(this.matricula);
		this.carregarContratosDependente(this.matricula);
		this.carregarClienteRestricoes();
		this.validaPermissaoAtualizarContrato();
		this.buscaConfiguracaoContratoMutavel();
	}

	openDetails(codigo) {
		this.router.navigate(
			[
				"..",
				"contratos",
				this.route.snapshot.paramMap.get("aluno-matricula"),
				"detail",
				codigo,
			],
			{
				relativeTo: this.route.parent,
			}
		);
	}

	iconClick(iconData: { row: any; iconName: string }) {
		this[iconData.iconName](iconData.row);
	}

	verDetalhamento(row) {
		this.openDetails(row.codigo);
	}

	enviarContratoPorEmail(row) {
		this.telaClienteService
			.configuracoesSistema(this.sessionService.chave, 0)
			.subscribe((data) => {
				const dialogRef = this.dialogService.open(
					"Solicitar envio de contrato",
					EnviarContratoEmailComponent,
					PactoModalSize.MEDIUM
				);
				dialogRef.componentInstance.codigoContrato = row.codigo;
				dialogRef.componentInstance.emails = this.dadosPessoais.emails;
				dialogRef.componentInstance.telefones = this.dadosPessoais.telefones;
				dialogRef.componentInstance.configuracoesSistemaData = data.content;
			});
	}

	verDocumentosAssinados(row) {
		const dialogRef = this.dialogService.open(
			`Documentos associados ao contrato ${row.codigo}`,
			ContratoDocumentosComponent,
			PactoModalSize.LARGE,
			"design-system3-adjust"
		);
		dialogRef.componentInstance.codigoContrato = row.codigo;
	}

	imprimirContrato(row) {
		this.aditivoService
			.buscarContratosAditivos(row.codigo)
			.subscribe(async (res) => {
				if (res.content && res.content.length > 1) {
					const contratosAditivos = res.content.map((item) => {
						return {
							value: JSON.stringify(item),
							name: item.nome,
						};
					});
					const dialogRef = this.dialogService.open(
						"Imprimir contrato",
						ImprimirContratoComponent,
						PactoModalSize.MEDIUM
					);

					dialogRef.componentInstance.codigoContrato = row.codigo;
					dialogRef.componentInstance.contratosAditivos = contratosAditivos;
				} else {
					this.imprimirContratoUnico(row);
				}
			});
	}

	imprimirContratoUnico(row) {
		const trueUrl = `${
			this.clientDiscoveryService.getUrlMap().zwUrl
		}/faces/VisualizarContrato?k=${this.sessionService.chave}&c=${
			row.codigo
		}&telaNova=true`;
		window.open(trueUrl, "_blank");
	}

	alterarVencimento(row) {
		const dialogRef = this.dialogService.open(
			"Alterar vencimento",
			AlterarVencimentoParcelasComponent,
			PactoModalSize.LARGE,
			"alterar-vencimento-modal design-system3-adjust"
		);
		dialogRef.componentInstance.contrato = row;
		dialogRef.componentInstance.codigoCliente =
			this.dadosPessoais.codigoCliente;
		dialogRef.componentInstance.update.subscribe((res) => {
			if (res === "reloadContrato") {
				this.tableContrato.reloadData();
			}
		});
	}

	afterLoadData() {
		this.empresa = this.dadosPessoais.empresa;
		const data = this.tableContrato.data.content;
		if (data && data.length > 0) {
			this.naoPermiteNovoContratoDeOutraEmpresa =
				data.find((v) => !v.permiteNovoContratoDeOutraEmpresa) > 0;
		}
		this.ableNovoContrato =
			(this.empresa && this.empresa.permiteContratosConcomintante) ||
			this.naoPermiteNovoContratoDeOutraEmpresa;
	}

	lancarNovoContrato() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"VendaRapidaTelaPadraoLancarContrato",
				"3.40 - Tela (Venda Rápida) como tela padrão para lançamento de contrato"
			)
			.subscribe(
				(response) => {
					this.abrirVendaRapidaAntiga();
				},
				(httpResponseError) => {
					this.admCoreApiNegociacaoService
						.negociacaoHabilitada()
						.subscribe((rest) => {
							if (rest) {
								this.abrirNovaNegociacao("novo");
								return;
							} else {
								this.clientDiscoveryService
									.linkZw(
										this.sessionService.usuarioOamd,
										this.sessionService.empresaId
									)
									.subscribe((result) => {
										window.open(
											`${result}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=NOVO_CONTRATO&isContratoOperacao=true&contratoConcomitante=true`,
											"_self"
										);
									});
							}
						});
				}
			);
	}
	validaPermissaoAtualizarContrato() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"PermitirAtualizacaoContratoAssinado",
				"13.17 - Permitir atualização manual de contratos não assinados"
			)
			.subscribe(
				(response) => {
					console.log("validaPermissaoAtualizarContrato");
					console.log(response);
					this.apresentarAtualizarContrato = true;
				},
				(httpResponseError) => {
					this.apresentarAtualizarContrato = false;
				}
			);
	}

	getLabelButtonAddContrato(str) {
		if (str == "IN" || str == "CA") {
			return "Rematricular contrato";
		} else if (str == "AT") {
			return "Renovar contrato";
		} else {
			return "Novo contrato";
		}
	}

	novoContrato() {
		if (!this.apresentarMenuContrato) {
			return;
		}

		if (this.clienteRestricoes && this.clienteRestricoes.length > 0) {
			this.openModalAvisoClienteRestricao();
			return;
		}

		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"VendaRapidaTelaPadraoLancarContrato",
				"3.40 - Tela (Venda Rápida) como tela padrão para lançamento de contrato"
			)
			.subscribe(
				(response) => {
					this.abrirVendaRapidaAntiga();
				},
				(httpResponseError) => {
					this.telaClienteService
						.getListaContratoRematricularERenovar(
							this.sessionService.chave,
							this.dadosPessoais.codigoCliente,
							this.sessionService.empresaId
						)
						.subscribe((response) => {
							if (response.content.length > 0) {
								const dialogRef = this.dialogService.open(
									"Aviso",
									ModalContratosRenovarERematricularComponent,
									PactoModalSize.LARGE
								);
								dialogRef.componentInstance.contratos = response.content;
								dialogRef.componentInstance.codCliente =
									this.dadosPessoais.codigoCliente;
								dialogRef.componentInstance.update.subscribe((res) => {
									console.log(res);
									if (res === "novocontrato") {
										this.lancarNovoContrato();
									} else if (res.startsWith("renovar")) {
										this.renovarRematricular(
											res.replace("renovar", ""),
											"renovar"
										);
									} else if (res.startsWith("rematricular")) {
										this.renovarRematricular(
											res.replace("rematricular", ""),
											"rematricular"
										);
									}
								});
							} else {
								this.admCoreApiNegociacaoService
									.negociacaoHabilitada()
									.subscribe((rest) => {
										if (rest) {
											this.abrirNovaNegociacao("novo");
											return;
										} else {
											this.clientDiscoveryService
												.linkZw(
													this.sessionService.usuarioOamd,
													this.sessionService.empresaId
												)
												.subscribe((result) => {
													let url = `${result}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=NOVO_CONTRATO&isContratoOperacao=true`;
													if (
														this.empresa &&
														this.empresa.permiteContratosConcomintante &&
														this.tableContrato &&
														this.tableContrato.data &&
														this.tableContrato.data.content &&
														this.tableContrato.data.content.length > 0
													) {
														url += `&contratoConcomitante=${true}`;
													}
													window.open(
														`${result}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=NOVO_CONTRATO&isContratoOperacao=true`,
														"_self"
													);
													this.cd.detectChanges();
												});
										}
									});
							}
						});
				}
			);
	}

	abrirNovaNegociacao(linha) {
		this.router.navigateByUrl(
			`/adm/negociacao/contrato/${this.dadosPessoais.matricula}/${linha}`
		);
	}

	abrirVendaRapidaAntiga() {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((result) => {
				window.open(
					`${result}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=NOVO_CONTRATO_VENDA_RAPIDA&isContratoOperacao=true`,
					"_self"
				);
				this.cd.detectChanges();
			});
	}

	renovar(contrato) {
		this.renovarRematricular(contrato.codigo, "renovar");
	}

	rematricular(contrato) {
		this.renovarRematricular(contrato.codigo, "rematricular");
	}

	renovarRematricular(codContrato, operacao) {
		if (this.clienteRestricoes && this.clienteRestricoes.length > 0) {
			this.openModalAvisoClienteRestricao();
			return;
		}
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"VendaRapidaTelaPadraoLancarContrato",
				"3.40 - Tela (Venda Rápida) como tela padrão para lançamento de contrato"
			)
			.subscribe(
				(response) => {
					this.abrirVendaRapidaAntiga();
				},
				(httpResponseError) => {
					this.admCoreApiNegociacaoService
						.negociacaoHabilitada()
						.subscribe((rest) => {
							if (rest) {
								this.abrirNovaNegociacao(operacao + "_" + codContrato);
								return;
							} else {
								if (operacao === "rematricular") {
									console.log(codContrato);
									this.clientDiscoveryService
										.linkZw(
											this.sessionService.usuarioOamd,
											this.sessionService.empresaId
										)
										.subscribe((result) => {
											let url = `${result}&contratoRenovar=${codContrato}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=REMATRICULAR_RENOVAR_CONTRATO&isContratoOperacao=true&rematricular=true`;
											window.open(url, "_self");
										});
								} else if (operacao === "renovar") {
									console.log(codContrato);
									this.clientDiscoveryService
										.linkZw(
											this.sessionService.usuarioOamd,
											this.sessionService.empresaId
										)
										.subscribe((result) => {
											let url = `${result}&contratoRenovar=${codContrato}&codCliente=${this.dadosPessoais.codigoCliente}&operacaoClienteEnumName=REMATRICULAR_RENOVAR_CONTRATO&isContratoOperacao=true&renovar=true`;
											window.open(url, "_self");
										});
								}
							}
						});
				}
			);
	}

	abrirAfastamentoContratoDependente(row, ferias) {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"LancarAfastamentoContratoDependente",
				"2.84 - Lançar afastamento para clientes que são dependentes"
			)
			.pipe(
				switchMap(() => {
					const codigoContratoDepdentente = Number(row.row.codigo);
					return this.telaClienteService.validarRegrasAfastamentoContratoDependente(
						Number(codigoContratoDepdentente),
						Number(this.sessionService.empresaId),
						this.sessionService.loggedUser.usuarioZw,
						ferias ? "CR" : "AT",
						this.sessionService.chave
					);
				})
			)
			.subscribe(
				(response) => {
					const modalRef = this.pactoModal.open(
						ferias ? "Férias" : "Atestado",
						ModalAfastamentoContratoDependenteComponent,
						PactoModalSize.LARGE
					);
					modalRef.componentInstance.dadosPessoais = this.dadosPessoais;
					modalRef.componentInstance.contratoDependente = row.row;
					modalRef.componentInstance.ferias = ferias;
					modalRef.componentInstance.update.subscribe((res) => {
						if (res === "atualizar_contrato_dependente") {
							this.tableContratoDependente.reloadData();
							this.perfilClienteStateNotifierService.reloadDataSituacoes();
							this.cd.detectChanges();
						} else if (res === "atualizar_cliente") {
							this.cd.detectChanges();
						}
					});
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
				}
			);
	}

	abrirHistoricoAfastamentoContratoDependente(row) {
		const modalRef = this.pactoModal.open(
			"Afastamentos lançados",
			ModalHistoricoAfastamentoContratoDependenteComponent,
			PactoModalSize.LARGE
		);
		modalRef.componentInstance.dadosPessoais = this.dadosPessoais;
		modalRef.componentInstance.contratoDependente = row.row;
		modalRef.componentInstance.update.subscribe((res) => {
			if (res === "atualizar_contrato_dependente") {
				this.tableContratoDependente.reloadData();
				this.perfilClienteStateNotifierService.reloadDataSituacoes();
				this.cd.detectChanges();
			} else if (res === "atualizar_cliente") {
				this.cd.detectChanges();
			}
		});
	}

	private carregarClienteRestricoes() {
		if (
			this.utilizarGestaoClienteComRestricao &&
			!isNullOrUndefinedOrEmpty(this.matricula)
		) {
			this.admCoreApiClienteRestricaoService
				.findByMatricula(this.matricula)
				.subscribe((response) => {
					this.clienteRestricoes = response;
				});
		}
	}

	get utilizarGestaoClienteComRestricao() {
		return this.permissaoService.temConfiguracaoEmpresaAdm(
			"utilizaGestaoClientesComRestricoes"
		);
	}

	private openModalAvisoClienteRestricao() {
		let unidades = "";
		this.clienteRestricoes.forEach((cr) => {
			if (!unidades.includes(cr.nomeEmpresa)) {
				unidades += unidades === "" ? cr.nomeEmpresa : ", " + cr.nomeEmpresa;
			}
		});
		const msg =
			"Identificamos que este cliente está bloqueado devido a pendências na(s) unidade(s): " +
			unidades +
			". Para resolver esta situação e reativar seus serviços, solicitamos que o cliente entre em contato " +
			"com a(s) referida(s) unidade(s) para regularizar suas pendências.";

		const dialogRef = this.dialogService.open(
			"Aviso",
			ModalClienteRestricaoMsgBloqueioComponent,
			PactoModalSize.LARGE,
			"modal-cliente-restricao-msg-bloqueio design-system3-adjust"
		);
		dialogRef.componentInstance.titulo = "Cliente com pendências";
		dialogRef.componentInstance.mensagem = msg;
	}

	openModalConfirmacaoAtualizacaoContrato(event) {
		const modalConfirmacao = this.ngbModal.open(
			ModalAtualizarContratoComponent,
			{
				size: "lg",
			}
		);

		modalConfirmacao.result
			.then((result) => {
				if (result) {
					this.admCoreApiContratoService
						.atualizaContrato(this.matricula)
						.subscribe((response) => {
							if (Array.isArray(response) && response.length > 0) {
								this.toastrService.success(
									"O processo foi executado com sucesso e alguns contratos foram atualizados.",
									"Processo concluído"
								);
							} else {
								this.toastrService.info(
									"O processo foi executado, mas nenhum contrato foi alterado — todos já estavam assinados.",
									"Processo concluído"
								);
							}
						});
				}
			})
			.catch((error) => {
				console.log(error);
			});

		this.cd.detectChanges();
	}

	public buscaConfiguracaoContratoMutavel() {
		this.modeloContratoService.configuracaoSistemaCompleto().subscribe({
			next: (response) => {
				this.mutavelContrato =
					response.content.permiteImpressaoContratoMutavel || false;
			},
			error: (err) => {
				console.error(
					"Erro ao pesquisar o parâmetro da replicação do modelo de contrato:",
					err
				);
			},
		});
	}
}
