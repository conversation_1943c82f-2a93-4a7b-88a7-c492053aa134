@import "projects/ui/assets/ds3/fonts/fonts.scss";

::ng-deep .modal-dialog {
	max-width: 681px;
}

.container {
	padding: 1.5rem;

	.form {
		display: flex;

		.input-email {
			width: -webkit-fill-available;
		}

		.input-whats {
			width: -webkit-fill-available;
		}

		.input-telefone-whats {
			width: 250px;
			margin-top: 10px;
		}

		.button {
			height: min-content;
			margin-top: 28px;
		}
	}

	.column {
		flex-direction: column;
	}

	.lista-de-emails {
		display: flex;
		flex-direction: column;
		margin-top: 5%;
		@extend .pct-title4;

		span {
			margin-bottom: 5%;
		}

		.email {
			display: flex;
			justify-content: space-between;
			font-family: Nunito Sans;
			font-size: 14px;
			font-weight: 400;
			line-height: 19.2px;
			text-align: left;

			::ng-deep pacto-cat-radio {
				.content-div {
					width: 15px !important;
					height: 15px !important;
				}

				.radio {
					border: 2px solid #1e60fa;
				}
			}
		}
	}

	.acoes {
		display: flex;
		justify-content: flex-end;
		gap: 1rem;
	}

	.sucesso {
		display: flex;
		justify-content: center;
		padding: 1.5rem;
		text-align: center;
	}

	.radio-btn {
		width: 14px;
		height: 14px;
		border-radius: 7px;
		border: 1px solid #b4b7bb;
		color: #b4b7bb;
		display: flex;
		justify-content: center;
		margin-right: 10px;
		align-items: center;

		.radio-checked {
			width: 4px;
			height: 4px;
			border-radius: 2px;
			background-color: white;
		}
	}

	.radio-option {
		display: flex;
		align-items: center;
		margin: 10px 0px;
		cursor: pointer;
	}

	.radio-option.selected {
		.radio-btn {
			color: #51555a;
			border-color: #51555a;
			background-color: #51555a;
		}

		.radio-value {
			color: #51555a;
		}
	}

	.radio-value {
		color: gray;
		font-size: 14px;
		font-weight: 400;
	}
}
