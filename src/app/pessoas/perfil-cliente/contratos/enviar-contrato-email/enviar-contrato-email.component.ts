import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SnotifyService } from "ng-snotify";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "../../../../microservices/client-discovery/client-discovery.service";
import { CadastroAuxApiAditivoService } from "cadastro-aux-api";

@Component({
	selector: "pacto-enviar-contrato-email",
	templateUrl: "./enviar-contrato-email.component.html",
	styleUrls: ["./enviar-contrato-email.component.scss"],
})
export class EnviarContratoEmailComponent implements OnInit {
	@Input()
	public emails: string[] = [];
	@Input()
	public telefones: any[] = [];

	public form: FormGroup = new FormGroup({
		email: new FormControl("", [Validators.email, Validators.required]),
		contratoAditivo: new FormControl("", [Validators.required]),
	});
	public formWhats: FormGroup = new FormGroup({
		msg: new FormControl("", null),
		telefone: new FormControl("", Validators.required),
	});
	public contratoProduto: boolean = false;
	public codigoContrato;
	public codigoVendaAvulsa;
	public codigoAulaAvulsaDiaria;
	public codigoProduto;
	public message: string;
	public contratosAditivos: any[] = [];

	configuracoesSistemaData;

	constructor(
		private readonly dialog: NgbActiveModal,
		private readonly notificationService: SnotifyService,
		private readonly telaClienteService: AdmLegadoTelaClienteService,
		private readonly sessionService: SessionService,
		private readonly clientDiscoveryService: ClientDiscoveryService,
		private readonly cd: ChangeDetectorRef,
		private aditivoService: CadastroAuxApiAditivoService
	) {}

	envioWhats: boolean = false;
	envioEmail: boolean = true;
	msgPadraoWhats: string =
		"Olá! Para assinar seu contrato com a academia, clique no link abaixo.";

	ngOnInit() {
		this.formWhats.get("msg").setValue(this.msgPadraoWhats);
		this.initContratosAditivos();
	}

	initContratosAditivos() {
		this.aditivoService.buscarContratosAditivos(this.codigoContrato).subscribe(
			async (res) => {
				if (res.content) {
					this.contratosAditivos = res.content.map((item) => {
						return {
							value: JSON.stringify(item),
							name: item.nome,
						};
					});
				}
			},
			(error) => {
				console.error("Erro ao buscar aditivos:", error);
				this.notificationService.error("Erro ao carregar aditivos do contrato");
			}
		);
	}

	addEmail() {
		this.form.markAsTouched();
		if (this.form.hasError("required")) {
			this.notificationService.error("Informe o e-mail");
			return;
		}
		if (this.form.hasError("email")) {
			this.notificationService.error("Informe um e-mail válido");
			return;
		}
		if (this.form.valid) {
			const emailValue = this.form.get("email").value;
			this.emails.push(emailValue);
			this.form.reset("");
		}
	}

	removeEmail(index) {
		this.emails.splice(index, 1);
	}

	cancel() {
		this.dialog.close();
	}

	sendEmail() {
		if (!this.form.get("contratoAditivo").value) {
			this.notificationService.error("Informe um contrato");
			return;
		}

		const contratoAditivo = JSON.parse(this.form.get("contratoAditivo").value);
		const aditivo =
			contratoAditivo && contratoAditivo.codigoAditivo
				? contratoAditivo.codigoAditivo
				: null;

		if (this.contratoProduto) {
			this.telaClienteService
				.enviarContratoProdutoEmail(
					this.sessionService.chave,
					this.codigoVendaAvulsa,
					this.codigoAulaAvulsaDiaria,
					this.codigoProduto,
					this.sessionService.loggedUser.usuarioZw,
					this.emails
				)
				.subscribe(
					(response) => {
						this.message = response.content;
						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.message) {
							this.notificationService.error(err.meta.message);
						} else if (err.meta && err.meta.error) {
							this.notificationService.error(err.meta.error);
						}
					}
				);
		} else {
			this.telaClienteService
				.enviarContratoEmail(
					this.sessionService.chave,
					this.codigoContrato,
					this.sessionService.loggedUser.usuarioZw,
					this.emails,
					aditivo
				)
				.subscribe(
					(response) => {
						this.message = response.content;
						this.cd.detectChanges();
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.message) {
							this.notificationService.error(err.meta.message);
						} else if (err.meta && err.meta.error) {
							this.notificationService.error(err.meta.error);
						}
					}
				);
		}
	}

	sendEmailAssinar() {
		this.telaClienteService
			.enviarContratoEmailAssinar(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.codigoContrato,
				this.emails
			)
			.subscribe(
				(response) => {
					this.message = response.content;
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.notificationService.error(err.meta.message);
					} else if (err.meta && err.meta.error) {
						this.notificationService.error(err.meta.error);
					}
				}
			);
	}
	sendWhatsAssinar() {
		const linkVisualizarContrato = `${
			this.clientDiscoveryService.getUrlMap().zwUrl
		}/faces/VisualizarContrato?k=${this.sessionService.chave}&c=${
			this.codigoContrato
		}&telaNova=true&exibirBotaoAssinatura=true`;

		this.telaClienteService
			.enviarContratoWhatsappAssinar(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.codigoContrato,
				this.formWhats.get("msg").value,
				this.formWhats.get("telefone").value,
				linkVisualizarContrato
			)
			.subscribe(
				(response) => {
					this.message =
						"O WhatsApp foi aberto em uma nova aba com o link para assinatura.";
					window.open(response.content, "_blank");
					this.cd.detectChanges();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.message) {
						this.notificationService.error(err.meta.message);
					} else if (err.meta && err.meta.error) {
						this.notificationService.error(err.meta.error);
					}
				}
			);
	}

	setEnvioWhats(value) {
		this.envioWhats = value;
		this.envioEmail = false;
	}

	setEnvioEmail(value) {
		this.envioEmail = value;
		this.envioWhats = false;
	}
}
