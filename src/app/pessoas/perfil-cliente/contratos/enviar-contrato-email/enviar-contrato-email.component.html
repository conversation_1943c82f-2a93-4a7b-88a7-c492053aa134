<div class="container">
	<ng-container *ngIf="!message">
		<ds3-field-label>Modo de envio</ds3-field-label>
		<div
			style="
				display: flex;
				align-items: center;
				gap: 10px;
				margin-bottom: 1rem;
			">
			<div
				(click)="setEnvioEmail(true)"
				[ngClass]="{ selected: envioEmail }"
				class="radio-option">
				<div class="radio-btn">
					<div *ngIf="envioEmail" class="radio-checked"></div>
				</div>
				<div class="radio-value">Enviar por E-mail</div>
			</div>
			<div
				(click)="setEnvioWhats(true)"
				[ngClass]="{ selected: envioWhats }"
				class="radio-option">
				<div class="radio-btn">
					<div *ngIf="envioWhats" class="radio-checked"></div>
				</div>
				<div class="radio-value">Enviar por WhatsApp</div>
			</div>
		</div>
		<form *ngIf="envioEmail" [formGroup]="form" class="form">
			<div class="row">
				<div class="col-md-12" style="margin-bottom: 30px">
					<ds3-form-field>
						<ds3-field-label>
							Selecione a versão do contrato que deseja enviar*
						</ds3-field-label>
						<ds3-select
							id="select-contrato"
							[formControl]="form.get('contratoAditivo')"
							[options]="contratosAditivos"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>

				<div class="col-md-10">
					<ds3-form-field class="input-email">
						<ds3-field-label>Adicionar novo e-mail</ds3-field-label>
						<input
							id="enviar-contrato-email-novo-email"
							[type]="email"
							autofocus
							ds3Input
							formControlName="email" />
					</ds3-form-field>
				</div>
				<div class="col-md-2" style="padding-top: 5px">
					<button
						id="enviar-contrato-email-add"
						(click)="addEmail()"
						class="button"
						ds3-text-button>
						Adicionar
					</button>
				</div>
			</div>
		</form>

		<div *ngIf="envioEmail" class="lista-de-emails">
			<span>E-mails para envio</span>
			<div *ngFor="let email of emails; index as i" class="email">
				<span>{{ email }}</span>
				<!-- <i class="pct pct-trash-2" (click)="removeEmail(i)"></i> -->
			</div>
		</div>

		<div *ngIf="envioWhats" style="margin-bottom: 1rem">
			<form *ngIf="envioWhats" [formGroup]="formWhats" class="form column">
				<ds3-form-field class="input-whats">
					<ds3-field-label>Mensagem a ser enviada</ds3-field-label>
					<input
						id="enviar-contrato-assinatura-whatsapp-msg"
						autofocus
						ds3Input
						formControlName="msg" />
				</ds3-form-field>

				<ds3-form-field class="input-telefone-whats">
					<ds3-field-label>Número</ds3-field-label>
					<ds3-select
						[formControl]="formWhats.get('telefone')"
						[options]="telefones"
						ds3Input
						nameKey="numero"
						valueKey="numero"></ds3-select>
				</ds3-form-field>
			</form>
		</div>

		<div class="acoes">
			<pacto-cat-button
				id="enviar-contrato-email-btn-cancelar"
				(click)="cancel()"
				label="Cancelar"
				type="OUTLINE"></pacto-cat-button>
			<pacto-cat-button
				id="enviar-contrato-email-btn-assinatura"
				(click)="sendEmailAssinar()"
				*ngIf="
					envioEmail &&
					!contratoProduto &&
					configuracoesSistemaData?.assinaturacontratoviaemail
				"
				label="Enviar para assinatura por E-mail"
				type="OUTLINE"></pacto-cat-button>
			<pacto-cat-button
				id="enviar-contrato-whatsapp-btn-assinatura"
				(click)="sendWhatsAssinar()"
				*ngIf="envioWhats"
				label="Enviar para assinatura por WhatsApp"
				type="OUTLINE"></pacto-cat-button>
			<pacto-cat-button
				id="enviar-contrato-email-btn-enviar"
				*ngIf="envioEmail"
				(click)="sendEmail()"
				label="Enviar"></pacto-cat-button>
		</div>
	</ng-container>

	<ng-container *ngIf="message">
		<div class="sucesso">
			<div style="word-break: break-word">
				<span id="enviar-contrato-email-msg">{{ message }}</span>
			</div>
		</div>
		<div class="acoes">
			<pacto-cat-button (click)="cancel()" label="Fechar"></pacto-cat-button>
		</div>
	</ng-container>
</div>
