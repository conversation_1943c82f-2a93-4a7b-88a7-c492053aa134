import { <PERSON><PERSON><PERSON>cyPipe, DatePipe, DecimalPipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { Api } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import { AdmCoreApiClienteService, ClienteDadosPessoais } from "adm-core-api";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
} from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import {
	AutorizacaoAcessoComponent,
	DataFiltro,
	DialogAutorizacaoAcessoComponent,
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
} from "ui-kit";
import { ModalActionEditVencimentosComponent } from "../financeiro/financeiro-produtos-vencimentos/modals/modal-action-edit-vencimentos/modal-action-edit-vencimentos.component";
import { ModalActionRenovarProdutoComponent } from "../financeiro/financeiro-produtos-vencimentos/modals/modal-action-renovar-produto/modal-action-renovar-produto.component";
import { CaptalizePipe } from "@base-shared/pipe/captalize.pipe";
import { MatDialog } from "@angular/material";
import { ClientDiscoveryService } from "../../../microservices/client-discovery/client-discovery.service";
import { EnviarContratoEmailComponent } from "../contratos/enviar-contrato-email/enviar-contrato-email.component";
import { ImprimirContratoComponent } from "../contratos/imprimir-contrato/imprimir-contrato.component";

declare var moment;

@Component({
	selector: "pacto-produtos",
	templateUrl: "./produtos.component.html",
	styleUrls: ["./produtos.component.scss"],
	providers: [CurrencyPipe, DecimalPipe, DatePipe],
})
export class ProdutosComponent implements OnInit, AfterViewInit {
	private dadosPessoais: ClienteDadosPessoais;
	private matricula;
	data = false;

	public readonly formGroup = new FormGroup({
		"input-pesquisa": new FormControl(""),
		"select-filter": new FormControl("0"),
	});

	private filtrosForm = {
		searchTerm: this.formGroup.get("input-pesquisa").value,
		todosOsProdutos: "true",
		produtosEstoque: "false",
		produtoServico: "false",
		produtosVencidos: "false",
		produtosComVencimento: "false",
		ignorarPlano: "true",
	};

	public baseFilter: DataFiltro = {};

	public readonly items = [
		{ id: "produto_todos", label: "Todos os produtos" },
		{ id: "produto_estoque", label: "Produto estoque" },
		{ id: "produto_servico", label: "Produto serviço" },
		{ id: "produto_vencido", label: "Produto vencido" },
		{ id: "produto_validade", label: "Produto com validade" },
	];

	@ViewChild("columnCod", { static: false })
	private columnCod: TemplateRef<any>;

	@ViewChild("columnDescricao", { static: false })
	private columnDescricao: TemplateRef<any>;

	@ViewChild("columnEmpresa", { static: false })
	private columnEmpresa: TemplateRef<any>;

	@ViewChild("columnLancamento", { static: false })
	private columnLancamento: TemplateRef<any>;

	@ViewChild("columnDataInicio", { static: false })
	private columnDataInicio: TemplateRef<any>;

	@ViewChild("columnDataFinal", { static: false })
	private columnDataFinal: TemplateRef<any>;

	@ViewChild("columnRenovacaoAutomatica", { static: false })
	private columnRenovacaoAutomatica: TemplateRef<any>;

	@ViewChild("columnValorUnit", { static: false })
	private columnValorUnit: TemplateRef<any>;

	@ViewChild("cellDataInicioVigencia", { static: false })
	private cellDataInicioVigencia: TemplateRef<any>;

	@ViewChild("cellDataFinalVigencia", { static: false })
	private cellDataFinalVigencia: TemplateRef<any>;

	@ViewChild("cellRenovavelAutomaticamente", { static: false })
	private cellRenovavelAutomaticamente: TemplateRef<any>;

	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	public table: PactoDataGridConfig;

	constructor(
		private readonly cd: ChangeDetectorRef,
		private readonly dialogService: DialogService,
		private readonly admLegadoTelaClienteService: AdmLegadoTelaClienteService,
		private readonly autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private readonly sessionService: SessionService,
		private readonly notificationService: SnotifyService,
		private readonly route: ActivatedRoute,
		private readonly msAdmCoreService: AdmCoreApiClienteService,
		private readonly restApi: RestService,
		private readonly clientDiscoveryService: ClientDiscoveryService,
		private readonly telaClienteService: AdmLegadoTelaClienteService,
		private matDialog: MatDialog
	) {}

	public ngOnInit() {
		this.matricula =
			this.route.snapshot.params["aluno-matricula"] ||
			sessionStorage.getItem("pacto-aluno-matricula");

		if (this.matricula) {
			this.msAdmCoreService.dadosPessoais(this.matricula).subscribe((data) => {
				this.dadosPessoais = data;
				this.initTable(data.codigoPessoa);
			});
			this.cd.detectChanges();
		}
	}

	public ngAfterViewInit(): void {
		this.formGroup.valueChanges.subscribe(() => {
			const valorInicial = this.formGroup.get("input-pesquisa").value;
			this.filterSelector();
			setTimeout(() => {
				const valorFinal = this.formGroup.get("input-pesquisa").value;
				if (valorInicial === valorFinal) {
					this.tableData.reloadData();
				}
			}, 300);
		});
	}

	private resetFilters() {
		this.filtrosForm.searchTerm = "";
		this.filtrosForm.todosOsProdutos = "true";
		this.filtrosForm.produtosEstoque = "false";
		this.filtrosForm.produtoServico = "false";
		this.filtrosForm.produtosVencidos = "false";
		this.filtrosForm.produtosComVencimento = "false";
		this.filtrosForm.ignorarPlano = "true";
	}

	private filterSelector() {
		this.resetFilters();
		this.filtrosForm.searchTerm = this.formGroup.get("input-pesquisa").value;
		switch (this.formGroup.get("select-filter").value) {
			case "produto_todos":
				this.filtrosForm.todosOsProdutos = "true";
				break;
			case "produto_estoque":
				this.filtrosForm.produtosEstoque = "true";
				break;
			case "produto_servico":
				this.filtrosForm.produtoServico = "true";
				break;
			case "produto_vencido":
				this.filtrosForm.produtosVencidos = "true";
				break;
			case "produto_validade":
				this.filtrosForm.produtosComVencimento = "true";
				break;
			default:
				this.filtrosForm.todosOsProdutos = "true";
				break;
		}
	}

	private initTable(codigoPessoa: number) {
		this.resetFilters();
		this.baseFilter.filters = this.filtrosForm;
		this.table = new PactoDataGridConfig({
			endpointUrl: this.restApi.buildFullUrl(
				`pessoas/${codigoPessoa}/produtos/tela-cliente`,
				true,
				Api.MSADMCORE
			),
			dataAdapterFn: (serveData) => {
				this.data = serveData.totalElements > 0 ? true : false;
				this.cd.detectChanges();
				return serveData;
			},
			columns: [
				{
					nome: "codigo",
					titulo: this.columnCod,
					visible: true,
					ordenavel: true,
				},
				{
					nome: "produto",
					titulo: this.columnDescricao,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.captalize(v.descricao),
				},
				{
					nome: "empresa",
					titulo: this.columnEmpresa,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => this.captalize(v.nome),
				},
				{
					nome: "dataLancamento",
					titulo: this.columnLancamento,
					visible: true,
					ordenavel: true,
					valueTransform: (v) => moment(v).format("DD/MM/YYYY"),
				},
				{
					nome: "dataInicioVigencia",
					titulo: this.columnDataInicio,
					visible: true,
					ordenavel: true,
					celula: this.cellDataInicioVigencia,
				},
				{
					nome: "dataFinalVigencia",
					titulo: this.columnDataFinal,
					visible: true,
					ordenavel: true,
					celula: this.cellDataFinalVigencia,
				},
				{
					nome: "renovavelAutomaticamente",
					titulo: this.columnRenovacaoAutomatica,
					visible: true,
					ordenavel: true,
					celula: this.cellRenovavelAutomaticamente,
				},
				{
					nome: "precoUnitario",
					titulo: this.columnValorUnit,
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return v.toLocaleString("pt-BR", {
							style: "currency",
							currency: "BRL",
						});
					},
				},
			],
			actions: [
				{
					nome: "editar",
					iconClass: "pct pct-edit cor-azulim05",
					tooltipText: "Editar",
					showIconFn(row) {
						return row.permiteEditar;
					},
				},
				{
					nome: "imprimirContrato",
					iconClass: "pct pct-printer cor-azulim05 pct-icon-mr-16",
					tooltipText: "Imprimir",
					showIconFn(row) {
						return (
							((row.vendaAvulsa && row.vendaAvulsa.codigo > 0) ||
								(row.aulaAvulsaDiaria && row.aulaAvulsaDiaria.codigo > 0)) &&
							row.produto.contratoTextoPadrao &&
							row.produto.contratoTextoPadrao > 0
						);
					},
				},
				{
					nome: "enviarContratoPorEmail",
					iconClass: "pct pct-send cor-azulim05 pct-icon-mr-16",
					tooltipText: "Envio de contrato",
					showIconFn(row) {
						return (
							((row.vendaAvulsa && row.vendaAvulsa.codigo > 0) ||
								(row.aulaAvulsaDiaria && row.aulaAvulsaDiaria.codigo > 0)) &&
							row.produto.contratoTextoPadrao &&
							row.produto.contratoTextoPadrao > 0
						);
					},
				},
				{
					nome: "renovar",
					iconClass: "pct pct-refresh-ccw cor-azulim05",
					tooltipText: "Renovar",
					showIconFn(row) {
						return row.permiteRenovar;
					},
				},
			],
		});
		this.cd.detectChanges();
	}

	imprimirContrato(row) {
		let trueUrl = `${
			this.clientDiscoveryService.getUrlMap().zwUrl
		}/faces/VisualizarContrato?k=${this.sessionService.chave}&p=${
			row.produto.codigo
		}&telaNova=true`;
		if (row.vendaAvulsa && row.vendaAvulsa.codigo > 0) {
			trueUrl += `&va=${row.vendaAvulsa.codigo}`;
		}
		if (row.aulaAvulsaDiaria && row.aulaAvulsaDiaria.codigo > 0) {
			trueUrl += `&avd=${row.aulaAvulsaDiaria.codigo}`;
		}
		window.open(trueUrl, "_blank");
	}

	enviarContratoPorEmail(row) {
		this.telaClienteService
			.configuracoesSistema(this.sessionService.chave, 0)
			.subscribe((data) => {
				const dialogRef = this.dialogService.open(
					"Solicitar envio de contrato",
					EnviarContratoEmailComponent,
					PactoModalSize.MEDIUM
				);
				dialogRef.componentInstance.contratoProduto = true;
				dialogRef.componentInstance.codigoVendaAvulsa = row.vendaAvulsa
					? row.vendaAvulsa.codigo
					: null;
				dialogRef.componentInstance.codigoAulaAvulsaDiaria =
					row.aulaAvulsaDiaria ? row.aulaAvulsaDiaria.codigo : null;
				dialogRef.componentInstance.codigoProduto = row.produto.codigo;
				dialogRef.componentInstance.emails = this.dadosPessoais.emails;
				dialogRef.componentInstance.telefones = this.dadosPessoais.telefones;
				dialogRef.componentInstance.configuracoesSistemaData = data.content;
			});
	}

	apresentarAlterarRenovacaoAutomatica(item) {
		if (item.produto && item.dataFinalVigencia) {
			const first = new Date();
			const second = new Date(item.dataFinalVigencia);
			const one = new Date(
				first.getFullYear(),
				first.getMonth(),
				first.getDate()
			);
			const two = new Date(
				second.getFullYear(),
				second.getMonth(),
				second.getDate()
			);
			const millisecondsPerDay = 1000 * 60 * 60 * 24;
			const millisBetween = two.getTime() - one.getTime();
			const days = millisBetween / millisecondsPerDay;
			return item.produto.renovavelAutomaticamente && days > -2;
		} else {
			return false;
		}
	}

	captalize(valor) {
		const captalizePipe = new CaptalizePipe();
		return captalizePipe.transform(valor);
	}

	public iconClickFn(event: { row: any; iconName: string; rowIndex: number }) {
		switch (event.iconName) {
			case "editar":
				this.editar(event.row, event.rowIndex);
				break;
			case "imprimirContrato":
				this.imprimirContrato(event.row);
				break;
			case "enviarContratoPorEmail":
				this.enviarContratoPorEmail(event.row);
				break;
			case "detalhar":
				this.detalhar(event.row, event.rowIndex);
				break;
			case "renovar":
				this.renovar(event.row, event.rowIndex);
				break;
		}
	}

	private renovar(rowData, rowIndex) {
		const modalConfirmacao: any = this.matDialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form
			.get("usuario")
			.setValue(this.sessionService.loggedUser.username);
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					"VendaAvulsa",
					"4.11 - Vendas avulsas",
					this.sessionService.empresaId
				)
				.subscribe(
					(response: any) => {
						result.modal.close();
						const dialogRef = this.dialogService.open(
							"Renovar produto com vencimento",
							ModalActionRenovarProdutoComponent
						);
						dialogRef.componentInstance.codMovProduto = rowData.codigo;
						dialogRef.componentInstance.codProduto = rowData.produto.codigo;
						this.cd.detectChanges();
						dialogRef.result
							.then((r) => {
								if (r) {
									result.modal.close();
									this.tableData.reloadData();
								}
							})
							.catch((e) => {});
					},
					(error) => {
						this.notificationService.error(error.error.meta.message);
					}
				);
		});
	}

	private editar(rowData, rowIndex) {
		const modalConfirmacao: any = this.matDialog.open(
			DialogAutorizacaoAcessoComponent,
			{
				disableClose: true,
				id: "autorizacao-acesso",
				autoFocus: false,
			}
		);
		modalConfirmacao.componentInstance.form
			.get("usuario")
			.setValue(this.sessionService.loggedUser.username);
		modalConfirmacao.componentInstance.confirm.subscribe((result) => {
			this.autorizarAcessoService
				.validarPermissao(
					this.sessionService.chave,
					result.data.usuario,
					result.data.senha,
					"AlterarDataVigenciaValidade_Autorizar",
					"3.29 - Alterar data de vigência do produto com validade",
					this.sessionService.empresaId
				)
				.subscribe(
					(response: any) => {
						result.modal.close();
						const dialogRef = this.dialogService.open(
							"Editar vigência de produto",
							ModalActionEditVencimentosComponent
						);
						dialogRef.componentInstance.produto = rowData.codigo;
						dialogRef.componentInstance.codigoUsuarioValidado =
							response.content;
						dialogRef.componentInstance.produtoDescricao =
							rowData.produto.descricao;
						dialogRef.componentInstance.produtoData = rowData.dataFinalVigencia;
						dialogRef.componentInstance.tableRef = this.tableData;
						dialogRef.result
							.then((r) => {
								if (r) {
									result.modal.close();
								}
							})
							.catch((e) => {});
						this.cd.detectChanges();
					},
					(error) => {
						this.notificationService.error(error.error.meta.message);
					}
				);
		});
	}

	private detalhar(rowData, rowIndex) {
		//     this.openModal(rowData, 'Detalhar', rowIndex);
		// }
	}

	renovacaoAutomatica(item) {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"adicionarRemoverProdutoRenovacaoAutomatica",
				"2.83 - Alterar renovação automática do produto."
			)
			.subscribe(
				(response) => {
					this.admLegadoTelaClienteService
						.alterarRenovacaoAutomaticaMovProduto(
							this.sessionService.chave,
							this.sessionService.codigoUsuarioZw,
							item.codigo
						)
						.subscribe(
							(resp) => {
								this.notificationService.success("Produto alterado");
								this.tableData.reloadData();
								this.cd.detectChanges();
								setTimeout(() => {
									this.limparPactoCatTolltip();
								}, 500);
							},
							(httpErrorResponse) => {
								const err = httpErrorResponse.error;
								if (err.meta && err.meta.message) {
									this.notificationService.error(err.meta.message);
								} else if (err.meta && err.meta.error) {
									this.notificationService.error(err.meta.error);
								}
							}
						);
				},
				(httpResponseError) => {
					this.notificationService.error(httpResponseError.error.meta.message);
					setTimeout(() => {
						this.limparPactoCatTolltip();
					}, 500);
				}
			);
	}

	limparPactoCatTolltip() {
		try {
			const elementsByClassName =
				document.getElementsByClassName("pacto-cat-tolltip");
			const array = Array.from(elementsByClassName);
			for (const element of array) {
				document.getElementById(element.id).style.visibility = "hidden";
			}
		} catch (e) {
			console.log(e);
		}
	}

	getFiltroSelect() {
		const value = this.formGroup.get("select-filter").value;
		if (value === "produto_todos") {
			return "produto";
		} else {
			return this.items.find((v) => v.id === value).label;
		}
	}

	getTitleEmpty() {
		switch (this.formGroup.get("select-filter").value) {
			case "0":
				return "produto";
				break;
			case "1":
				return "produto estoque";
				break;
			case "2":
				return "produto serviço";
				break;
			case "3":
				return "produto vencido";
				break;
			case "4":
				return "produto com validade";
				break;
			default:
				return "produto";
				break;
		}
	}
}
