// @import 'projects/ui/assets/import.scss';

// pacto-cat-form-input, pacto-cat-form-textarea, pacto-cat-form-select {
//   margin: 0px !important;
// }

// .action-button{
//   margin-top: 20px;
// }

// .row {
//   align-items: flex-end;
// }

// input {
//   width: 100%;
//   border-radius: 3px;
//   border: 1px solid $gelo03;
//   line-height: 40px;
//   color: $gelo05;

//   outline: 0px !important;

//   border-color: $cinzaClaroPri !important;
//   background-color: $cinzaClaroPri !important;

//   text-align: center;
// }

// .loader {
//   display: flex;
//   align-content: center;
//   align-items: center;
//   width: 100%;
//   height: 100%;

//   img {
//     width: 50px;
//     height: 50px;
//   }
// }
@import "projects/ui/assets/import.scss";

pacto-cat-form-input,
pacto-cat-form-textarea,
pacto-cat-form-select {
	margin: 0px !important;
}

.action-button {
	margin-top: 20px;
}

.row {
	align-items: flex-end;
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	border-color: $cinzaClaroPri !important;
	background-color: $cinzaClaroPri !important;

	text-align: center;
}

.loader {
	display: flex;
	align-content: center;
	align-items: center;
	width: 100%;
	height: 100%;

	img {
		width: 50px;
		height: 50px;
	}
}

.actions {
	display: flex;
	justify-content: flex-end;

	button {
		margin: 8px 0 0 8px;
	}

	i.icon-drop {
		margin-top: 8px;
	}
}

.div-explicacao-item-crm {
	display: flex;
	background: #fafafa;
	padding: 5px !important;
	color: #797d86;
	align-items: center;
	margin-top: 15px;
	font-size: 14px;
}

.div-explicacao-item-crm-icon {
	padding: 10px;
}

.div-explicacao-item-crm-text {
	color: #40424c;
}
