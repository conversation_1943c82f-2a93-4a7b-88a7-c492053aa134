@import "projects/ui/assets/ui-kit.scss";
// pacto-cat-card-plain {
//     padding: 1rem;
// }

// :host {
//     ::ng-deep {
//         pacto-cat-form-input .nome,
//         pacto-cat-form-textarea .textarea-label,
//         pacto-cat-form-textarea textarea,
//         pacto-cat-form-textarea textarea::placeholder,
//         pacto-cat-select-filter .pacto-label,
//         pacto-cat-select-filter .option-label {
//             font-size: 14px;
//         }

//         pacto-cat-tabs-transparent .tabs .tab {
//             color: #a1a5aa !important;
//         }
//         pacto-cat-tabs-transparent .tabs .tab.active {
//             color: #51555a !important;
//         }

//         pacto-cat-form-textarea {
//             margin-top: 1.5rem;
//             margin-bottom: 1rem;
//         }
//     }
// }

.row {
	div {
		padding: 0px;
	}
}

.actions {
	display: flex;
	justify-content: flex-end;
	width: 100%;

	button {
		margin: 8px 0 0 8px;
	}

	i.icon-drop {
		margin-top: 8px;
	}
}

.apply-button {
	display: flex;
	flex-direction: row;
	padding: 8px !important;
	align-items: center;
	justify-content: center;

	button {
		i.pct {
			padding-right: 8px;
		}
	}
}

button[ds3-outlined-button] {
	&:has(+ .icon-drop) {
		border-radius: 4px 0px 0px 4px;
	}

	& + .icon-drop {
		height: 42px;
		padding: 5.5px 4px 5px 3px;
		border-radius: 0 4px 4px 0;
		border-top: 1px solid var(--color-action-default-able-4);
		border-left: none;
		border-right: 1px solid var(--color-action-default-able-4);
		border-bottom: 1px solid var(--color-action-default-able-4);
		display: flex;
		align-items: center;
		justify-content: center;
		color: var(--color-action-default-able-4);
		margin-left: -2px;
	}
}

button[ds3-flat-button] {
	&:has(+ .icon-drop) {
		border-radius: 4px 0px 0px 4px;
	}

	& + .icon-drop {
		height: 42px;
		padding: 5.5px 4px 5px 3px;
		border-radius: 0 4px 4px 0;
		border-top: 0px;
		border-left: 1px solid var(--color-action-bgdark-able-3);
		border-right: 0px;
		border-bottom: 0px;
		display: flex;
		background-color: var(--color-action-default-able-4);
		align-items: center;
		justify-content: center;
		color: var(--color-action-bgdark-able-3);
		margin-left: -2px;
	}
}

.tags {
	display: flex;
	gap: 16px;
	flex-direction: row;
	flex-wrap: wrap;
	margin-top: 8px;

	.tag {
		padding: 4px 8px 4px 8px;
		border-radius: 100px;
		border: 1px solid var(--color-action-default-able-4);

		i {
			padding-left: 8px;
		}

		&:hover {
			cursor: pointer;
		}
	}
}

.historico {
	display: flex;
	align-items: center;
	flex-direction: column;
	width: 100%;
	gap: 16px;

	.isTheFirstOfTheMonth {
		margin-bottom: 16px;
		padding: 8px;
		background-color: var(--color-support-gray-0);
		@extend .pct-title5;
		color: var(--color-typography-default-text);
	}

	.entry {
		width: 100%;
		padding: 16px;
		border-radius: 5px;
		background-color: var(--color-background-plane-3);
		border: 0.5px solid var(--color-support-gray-3);

		.col-3,
		.col-2 {
			display: flex;
			align-items: center;
			flex-direction: column;

			.entry-label {
				@extend .pct-title5;
				color: var(--color-typography-default-text);
			}

			.entry-body {
				@extend .pct-overline2;
				color: var(--color-typography-default-text);
			}
		}

		.script {
			display: flex;
			flex-direction: column;
			align-items: center;
			line-break: anywhere;
			margin-top: 20px;
		}
	}
}

.div-contato {
	margin-bottom: 20px;

	::ng-deep .tab-content {
		color: #242528 !important;
	}
}

.title-text-hist-contato {
	font-family: Poppins;
	font-size: 16px;
	font-weight: 600;
	line-height: 20px;
	letter-spacing: 0.25px;
	text-align: left;
	color: #51555a;
}

.div-nao-tem-recurso {
	display: flex;
	justify-content: center;
	padding: 50px 0 30px 0;
}

.div-empty {
	display: grid;
	justify-items: center;
	width: 100%;
	margin: 30px 0 30px 0;
}

* {
	cursor: pointer;
}

.slider-container {
	display: flex;
	color: #797d86;
	font-size: 16px;
	font-weight: 400;
	line-height: 16px;

	label {
		margin-left: 8px;
	}

	.switch {
		position: relative;
		display: inline-block;
		width: 36px;
		height: 20px;
	}

	/* Hide default HTML checkbox */
	.switch input {
		opacity: 0;
		width: 0;
		height: 0;
	}

	/* The slider */
	.slider {
		position: absolute;
		cursor: pointer;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #ccc;
		-webkit-transition: 0.4s;
		transition: 0.4s;
	}

	.slider:before {
		position: absolute;
		content: "";
		height: 16px;
		width: 13px;
		left: 2px;
		bottom: 2px;
		background-color: white;
		-webkit-transition: 0.4s;
		transition: 0.4s;
	}

	.checked + .slider {
		background-color: #1e60fa;
	}

	input:focus + .slider {
		box-shadow: 0 0 1px #1e60fa;
	}

	.checked + .slider:before {
		-webkit-transform: translateX(13px);
		-ms-transform: translateX(13px);
		transform: translateX(18px);
	}

	/* Rounded sliders */
	.slider.round {
		border-radius: 17px;
	}

	.slider.round:before {
		border-radius: 50%;
	}
}

.historico-chat {
	display: flex;
	flex-direction: column;
	gap: 10px;
	padding: 20px;
	max-height: 400px;
	/* Ajuste conforme necessário */
	overflow-y: auto;
	background-color: #f0f0f0;
	border-radius: 10px;
}

.message-container {
	display: flex;
}

.message-container:last-of-type {
	padding-bottom: 20px !important;
}

.message-container:first-of-type {
	padding-top: 20px !important;
}

.message {
	max-width: 70%;
	padding: 10px 15px;
	border-radius: 15px;
	word-wrap: break-word;
	box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
	font-size: 1rem;
}

.ia-message {
	justify-content: flex-end;
	align-self: flex-start;
	background-color: #e1f5fe;
	color: #01579b;
	text-align: left;
}

.user-message {
	justify-content: flex-start;
	align-self: flex-end;
	background-color: #c8e6c9;
	color: #2e7d32;
	text-align: right;
}

.sistem-message {
	justify-content: center;
	align-self: flex-end;
	background-color: #fff3cd;
	/* Alerta amarelo */
	color: #856404;
	/* Texto de alerta amarelo */
	text-align: center;
}

.message-text {
	display: inline-block;
	white-space: pre-wrap;
	word-break: break-word;
}

.ia-message-format {
	justify-content: flex-end;
	display: flex;
	width: 100%;
	cursor: default !important;
}

.cursor-default {
	cursor: default !important;
}

.user-message-format {
	justify-content: flex-start;
	display: flex;
	width: 100%;
	cursor: default !important;
	padding-left: 10px !important;
}

.sistem-message-format {
	justify-content: center;
	display: flex;
	width: 100%;
	cursor: default !important;
}
