// @import 'projects/ui/assets/import.scss';
@import "projects/ui/assets/ui-kit.scss";
// pacto-cat-form-input, pacto-cat-form-textarea {
//   margin: 0px !important;
// }

// .form-group {
//   margin: 0px !important;
// }

// .alinhamento-linha-base {
//   align-items: baseline;
// }

// .alinhamento-centro {
//   align-items: center;
// }

// .titulo-col {
//   margin-top: -4px;
// }

// input {
//   width: 100%;
//   border-radius: 3px;
//   border: 1px solid $gelo03;
//   line-height: 40px;
//   color: $gelo05;

//   outline: 0px !important;

//   border-color: $cinzaClaroPri !important;
//   background-color: $cinzaClaroPri !important;

//   text-align: center;
// }

.row {
	div {
		padding: 0px;
	}
}

.actions {
	display: flex;
	justify-content: flex-end;

	button {
		margin: 8px 0 0 8px;
	}

	i.icon-drop {
		margin-top: 8px;
	}
}

.apply-button {
	display: flex;
	flex-direction: row;
	padding: 8px !important;
	align-items: center;
	justify-content: center;

	button {
		i.pct {
			padding-right: 8px;
		}
	}
}

button[ds3-outlined-button] {
	&:has(+ .icon-drop) {
		border-radius: 4px 0px 0px 4px;
	}

	& + .icon-drop {
		height: 42px;
		padding: 5.5px 4px 5px 3px;
		border-radius: 0 4px 4px 0;
		border-top: 1px solid var(--color-action-default-able-4);
		border-left: none;
		border-right: 1px solid var(--color-action-default-able-4);
		border-bottom: 1px solid var(--color-action-default-able-4);
		display: flex;
		align-items: center;
		justify-content: center;
		color: var(--color-action-default-able-4);
		margin-left: -2px;
	}
}

button[ds3-flat-button] {
	&:has(+ .icon-drop) {
		border-radius: 4px 0px 0px 4px;
	}

	& + .icon-drop {
		height: 42px;
		padding: 5.5px 4px 5px 3px;
		border-radius: 0 4px 4px 0;
		border-top: 0px;
		border-left: 1px solid var(--color-action-bgdark-able-3);
		border-right: 0px;
		border-bottom: 0px;
		display: flex;
		background-color: var(--color-action-default-able-4);
		align-items: center;
		justify-content: center;
		color: var(--color-action-bgdark-able-3);
		margin-left: -2px;
	}
}

.iconAddResp3 {
	border: none;
	background: none;
	cursor: pointer;
}

.div-explicacao-item-crm {
	display: flex;
	background: #fafafa;
	padding: 5px !important;
	color: #797d86;
	align-items: center;
	margin-top: 15px;
	font-size: 14px;
}

.div-explicacao-item-crm-icon {
	padding: 10px;
}

.div-explicacao-item-crm-text {
	color: #40424c;
}

.div-tipoContato-app {
	margin-top: 15px;
}
