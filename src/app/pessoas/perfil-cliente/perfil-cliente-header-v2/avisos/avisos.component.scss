@import "projects/ui/assets/ds3/fonts/fonts.scss";

mat-dialog-container {
	padding: unset !important;
	position: relative;
	border-radius: 9px !important;
	border: 0;
}

header {
	display: flex;
	justify-content: space-between;
	padding: 16px 16px;
	position: sticky; // Fixar o header no topo
	top: 0;
	background-color: white; // Definir a cor de fundo para o header
	z-index: 1000; // Garantir que o header fique acima do conteúdo

	span {
		font-family: "Nunito Sans", sans-serif;
		line-height: 22px;
		font-size: 14px !important;
		font-weight: 700 !important;
		color: #51555a;
	}

	border-bottom: 1px solid #dcdddf;
}

.cursor-pointer {
	cursor: pointer;
}

.pacto-avisos-modal-component {
	.container {
		padding: 1.5rem;
	}

	.icon-button {
		all: unset;
		cursor: pointer;
	}

	.action-buttons {
		display: inline-flex;
		gap: 0.5rem;
	}

	.textarea-label {
		display: none;
	}

	pacto-cat-form-textarea {
		margin: 0;
	}

	.form-buttons {
		margin-top: 1rem;
	}

	.form-buttons > * + * {
		margin-left: 1rem;
	}

	.low-opacity {
		opacity: 0.6;
	}

	pacto-relatorio {
		.table-content {
			padding: 0;
		}

		.pacto-table-title-block {
			padding: 0;
			padding-bottom: 1.5rem;
		}

		.table-content table.table {
			th {
				font-size: 14px;
				font-weight: 700;
			}

			td {
				font-size: 12px;

				i {
					font-size: 16px;
				}
			}

			.action-cell i {
				margin-right: 8px;
			}
		}
	}

	pacto-relatorio td,
	pacto-cat-form-input input::placeholder,
	pacto-cat-form-input input {
		font-size: 12px;
		color: #494b50;
	}

	pacto-relatorio td .clearStyles * {
		font-weight: 300 !important;
		color: #67757c;
	}

	pacto-cat-form-select-filter {
		margin: 0;
	}

	.pacto-label,
	pacto-cat-form-input .nome,
	pacto-cat-form-select-filter .nome,
	.pct-error-msg {
		display: none;
	}

	.form-label {
		font-size: 14px;
		margin-bottom: 12px;
	}

	pacto-cat-form-input {
		margin-top: 12px;
		margin-bottom: 20px;
	}
}
