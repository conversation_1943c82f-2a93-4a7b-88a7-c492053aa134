.pacto-observacoes-modal-component {
	.container {
		padding: 1.5rem;
		width: 100%;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.icon-button {
		all: unset;
		cursor: pointer;
	}

	.action-buttons {
		display: flex;
		flex-wrap: nowrap;
		gap: 0.5rem;
	}

	.textarea-label {
		display: none;
	}

	pacto-cat-form-textarea {
		margin: 0;
		width: 100%;
		flex: 1;
	}

	.form-buttons {
		margin-top: 1rem;
		display: flex;
		flex-wrap: nowrap;
		gap: 1rem;
	}

	.overflow-ellipsis {
		text-overflow: ellipsis;
		width: auto;
		max-width: 100%;
		white-space: nowrap;
		display: inline-block;
		overflow: hidden;
	}

	pacto-relatorio td,
	pacto-cat-form-input input::placeholder,
	pacto-cat-form-input input {
		font-size: 12px;
		color: #494b50;
	}

	pacto-relatorio {
		.table-content {
			padding: 0;
			overflow: hidden;
			display: flex;
			flex-direction: column;
			max-height: calc(540px - 150px);
		}

		table.table {
			width: 100%;
			border-collapse: collapse;

			th {
				font-size: 14px;
				text-align: center;
				padding: 0.5rem;
			}

			td {
				font-size: 12px;
				word-wrap: break-word;
				text-align: center;
				white-space: normal;
				overflow-wrap: anywhere;
				span {
					color: #40424c;
				}
			}

			/* Efeito zebrado */
			tr:nth-child(even) {
				background-color: #fafafa;
			}

			.actions-cell {
				text-align: right;
				padding-right: 1rem;
				width: 100px;
			}

			.action-buttons {
				display: flex;
				justify-content: flex-end;
				gap: 0.5rem;
				margin-right: 1.5rem;
			}
		}
	}

	pacto-relatorio td i {
		font-size: 16px;
	}

	pacto-cat-form-input .nome span {
		color: #51555a;
	}
}
