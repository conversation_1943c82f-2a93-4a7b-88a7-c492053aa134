/* You can add global styles to this file, and also import other style files */

html,
body {
	height: 100%;
	width: 100%;
}

body {
	margin: 0;
}

.modal-detalhar-bv .modal-dialog {
	max-width: 1300px;
	width: 85vw;
}

strong {
	font-weight: bold !important;
}

.title-text-empty {
	font-family: Poppins;
	font-size: 14px;
	font-weight: 600;
	line-height: 17.5px;
	letter-spacing: 0.25px;
	text-align: left;
	color: #51555a;
}

.body-text-empty {
	font-family: Poppins;
	font-size: 14px;
	font-weight: 600;
	line-height: 17.5px;
	letter-spacing: 0.25px;
	text-align: center;
	color: #55585e;
}

.pacto-button {
	border: 0;
	padding: 0;
	box-shadow: none;
	border-radius: 4px;
	line-height: 32px;
	cursor: pointer;
	position: relative;
	outline: 0;

	.large {
		line-height: 40px;
		font-size: 16px;
	}
}

.pacto-primary {
	background-color: #0380e3;
	border: 1px solid #1998fc;
	color: #fff;
}

.margin-5 {
	margin: 5px;
}

.margin-right-5 {
	margin-right: 5px;
}

.tooltipster .tooltip-inner {
	max-width: 500px !important;
	word-wrap: break-word;
	white-space: normal;
	text-align: left;
}

/* Tooltip do ngb-tooltip com z-index alto para aparecer na frente do menu */
ngb-tooltip-window {
	z-index: 10000 !important;
}

.tooltip {
	z-index: 10000 !important;
}

/* Classe específica para tooltip do checkbox com z-index ainda mais alto */
.checkbox-tooltip-high-zindex {
	z-index: 15000 !important;
	position: relative !important;
}

ngb-tooltip-window.checkbox-tooltip-high-zindex {
	z-index: 15000 !important;
	position: fixed !important;
}

/* Classe específica para tooltip dos botões */
.button-tooltip-high-zindex {
	z-index: 15000 !important;
	position: relative !important;
}

ngb-tooltip-window.button-tooltip-high-zindex {
	z-index: 15000 !important;
	position: fixed !important;
}
